<template>
  <div class="table-wrapper" :style="computedOverAllStyle">
    <header :id="theadId" class="table__header">
      <div class="table__row" :style="computedTheadStyle">
        <span v-for="(item, index) in chartData.column" :key="index" :title="item.indexName" style="flex: 1">
          {{ item.indexName }}{{ item.unit ? '（' + item.unit + '）' : '' }}
        </span>
      </div>
    </header>
    <div
      class="table__body__container"
      :style="{
        height: tbodyContainerHeight,
        overflow: 'auto'
      }"
    >
      <main :class="{ table__body: true, scroll: scroll }">
        <div v-for="(item, index) in chartData.data" :key="index" class="table__row" :style="computedRowStyle">
          <div
            v-for="(column, i) in chartData.column"
            :key="i"
            :style="{
              flex: 1,
              ...computedColumnStyle
            }"
            :title="item[column.sourceColumn]"
          >
            {{ item[column.sourceColumn] }}
          </div>
        </div>
      </main>
    </div>
  </div>
</template>

<script>
import { getAction, postAction } from '@/api/manage'
import showMoreIcon from '@/assets/icon/showMoreTable.svg'
import { mapWritableState } from 'pinia'
import { topicDataStore } from '@/stores/component.js'
export default {
  name: 'TableComp',
  props: {
    option: Object,
    width: Number,
    height: Number,
    indexId: {
      type: String,
      default: ''
    },
    paramsForUnBindingComp: {
      type: Object,
      default: () => ({
        tableName: ''
      })
    },
    id: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      theadId: '',
      showMoreIcon,
      querys: [],
      chartData: {
        column: [],
        data: [],
        unitList: {}
      },
      scroll: true,
      pageHeight: true,
      tbodyContainerHeight: 0
    }
  },
  computed: {
    ...mapWritableState(topicDataStore, ['topicData', 'componentData', 'isPad']),
    computedOverAllStyle() {
      return this.$convertPxToVw(
        { height: this.height + 'px', ...this.$getStyle(this.option.attribute.overallStyle) },
        this.topicData.topicWidth,
        this.isPad
      )
    },
    computedTheadStyle() {
      return this.$convertPxToVw(
        this.$getStyle(this.option.attribute.theadStyle),
        this.topicData.topicWidth,
        this.isPad
      )
    },
    computedRowStyle() {
      return this.$convertPxToVw(this.$getStyle(this.option.attribute.trowStyle), this.topicData.topicWidth, this.isPad)
    },
    computedColumnStyle() {
      return this.$convertPxToVw(this.$getStyle(this.option.attribute.tdStyle), this.topicData.topicWidth, this.isPad)
    }
  },
  created() {
    this.theadId = this.$uuid()
    if (this.computedTheadStyle.height) this.tbodyContainerHeight = `calc(100% - ${this.computedTheadStyle.height})`
    this.$nextTick(() => {
      const theadDom = document.getElementById(this.theadId)
      this.tbodyContainerHeight = `calc(100% - ${theadDom.clientHeight + 'px'})`
    })
  },
  mounted() {
    this.refreshCptData()
    this.$bus.$on('onTabClick', (columnInfo) => {
      if (columnInfo.elementsId.some((element) => element.id === this.id)) {
        const index = columnInfo.elementsId.findIndex((element) => element.id === this.id)
        const query = {
          value: columnInfo.option,
          column: columnInfo.columns[index].name
        }
        const foundIndex = this.querys.findIndex((item) => item.column === query.column)
        if (foundIndex < 0) this.querys.push(query)
        else this.querys[foundIndex].value = query.value
        this.loadData(this.querys)
      }
    })
  },
  beforeDestroy() {
    this.$bus.$off('onTabClick')
  },
  methods: {
    refreshCptData() {
      this.loadData()
    },
    async loadData(querys = []) {
      if (!this.option.attribute.indexSet) return
      let columnUsed, dataRes
      // 已绑定组件
      const indexSetInfoRes = await getAction('cockpit/index/scCockpitIndexGroupConfig/queryById', {
        id: this.option.attribute.indexSet
      })
      const indexSetInfo = indexSetInfoRes.result
      columnUsed = this.option.attribute.columnUsed.map((column) => {
        if (!indexSetInfo.queryColumnList.some((item) => item.sourceColumn === column.sourceColumn)) {
          return indexSetInfo.groupColumnList.filter((item) => item.sourceColumn === column)[0]
        }
      })
      dataRes = await postAction(
        `cockpit/index/scCockpitIndexGroupConfig/previewData?id=${this.option.attribute.indexSet}`,
        {
          connType: '',
          pageNo: 1,
          pageSize: 1000,
          where: querys.length
            ? querys.map((item) => {
                return { column: item.column, value: item.value, operator: 'eq' }
              })
            : []
        }
      )
      let data = dataRes.result.records
      this.chartData = {
        column: columnUsed.map((column) => {
          return {
            sourceColumn: column.sourceColumn,
            indexName: column.indexName,
            unit: column.unit
          }
        }),
        data: data
      }
    }
  }
}
</script>

<style lang="less" scoped>
.table-wrapper {
  position: relative;
  width: 100%;
  max-height: 1260px;
  overflow: auto;

  .table__header {
    position: sticky;
    top: 0;
    left: 0;
    width: 100%;

    .table__row {
      font-weight: 600;
      background-color: hsla(208, 100%, 50%, 1);
      color: hsla(0, 0%, 100%, 1);
    }
  }
  @keyframes rotate {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  .table__body {
    margin-top: 14px;

    .img1 {
      width: 78px;
      height: 78px;
    }

    .img2 {
      width: 40px;
      height: 52px;
    }

    .img3 {
      width: 34px;
      height: 46px;
    }
  }

  .circleNum {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;

    div {
      width: 36px;
      height: 36px;
      border-radius: 50%;
      background-color: #0089ff;
      color: #fff;
      line-height: 36px;
      text-align: center;
    }
  }
  .table__row {
    width: 100%;
    min-height: 60px;
    background-color: hsla(213, 100%, 98%, 1);
    color: hsla(208, 100%, 37%, 1);
    border-radius: 5000px;
    display: flex;
    align-items: center;
    font-size: 14px;
    line-height: 36px;
    padding: 0 16px;
    display: flex;
    > * {
      // min-width: 100px;
      text-align: center;
      line-height: 1.3;
      overflow: hidden;
      // text-overflow: ellipsis;
      // /* white-space: nowrap; */
      // display: -webkit-box;
      // -webkit-box-orient: vertical;
      // -webkit-line-clamp: 2;

      &:not(:last-of-type) {
        margin-right: 4px;
      }
    }

    > .order-class {
      width: 100px !important;
      // flex: 0;
    }

    &:not(:last-of-type) {
      margin-bottom: 14px;
    }
  }
  .scroll {
    overflow-y: auto;
  }
}

@media screen and (min-width: 600px) {
  .table-wrapper {
    .table__row {
      min-height: 30px;
    }
  }
}
</style>
