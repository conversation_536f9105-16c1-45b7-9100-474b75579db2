import Vue from 'vue'
import cpt_image from './cpt-image.vue'
import cpt_text from './cpt-text.vue'
import cpt_map from './cpt-map.vue'
import cpt_container_draggable from './cpt-container-draggable.vue'
import cpt_container_draggable_tab from './cpt-containers-draggable-tab.vue'
import groupedHistogramChart from './groupedHistogramChart.vue'
import tabComp from './tabComp.vue'
import selectComp from './selectComp.vue'
import basicBarChart from './basicBarChart.vue'
import groupedBarChart from './groupedBarChart.vue'
import BasicHistogramChart from './basicHistogramChart.vue'
import stackedHistogramChart from './stackedHistogramChart.vue'
import stackedBarChart from './stackedBarChart.vue'
import basicLineChart from './basicLineChart.vue'
import stackedLineChart from './stackedLineChart.vue'
import stackedAreaChart from './stackedAreaChart.vue'
import areaChart from './areaChart.vue'
import BarLineChart from './BarLineChart.vue'
import pieChart from './pieChart.vue'
import ringChart from './ringChart.vue'
import roseChart from './roseChart.vue'
import radarChart from './radarChart.vue'
import customTextGroup from './customTextGroup.vue'
import tableComp from './tableComp.vue'
import customComponent from './customComponent.vue'
import progressBar from './progressBar.vue'
import cptTitle from './cpt-title.vue'
let cptList = [
  cpt_image,
  cpt_text,
  cpt_map,
  cpt_container_draggable,
  cpt_container_draggable_tab,
  groupedHistogramChart,
  tabComp,
  selectComp,
  basicBarChart,
  groupedBarChart,
  BasicHistogramChart,
  stackedHistogramChart,
  stackedBarChart,
  basicLineChart,
  stackedLineChart,
  stackedAreaChart,
  areaChart,
  BarLineChart,
  pieChart,
  ringChart,
  roseChart,
  radarChart,
  customTextGroup,
  tableComp,
  customComponent,
  progressBar,
  cptTitle
]
cptList.forEach((ele) => {
  Vue.component(ele.name, ele)
})
