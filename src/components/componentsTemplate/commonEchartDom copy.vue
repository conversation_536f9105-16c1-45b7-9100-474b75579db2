<template>
  <div style="position: relative">
    <!-- <queryComp
      v-if="!!queryCompList.filter((queryComp) => !!queryComp.isShow).length"
      class="queryComp"
      :queryCompList="queryCompList"
      :style="{
        position: 'absolute',
        top: currentCpt.cptOption?.attribute.queryTop + 'px',
        left: currentCpt.cptOption?.attribute.queryLeft + 'px'
      }"
      @queryInfo="(query) => loadData(query)"
    ></queryComp> -->
    <div :id="uuid" ref="chart" :style="{ width: width + 'px', height: height + 'px' }"></div>
  </div>
</template>
<script>
import { useDesignerStore } from '@/stores/designer'
import { mapWritableState } from 'pinia'
import { loadDataMixin } from './loadDataMixin'

export default {
  name: 'CommonEchartDom',
  mixins: [loadDataMixin],
  computed: {
    ...mapWritableState(useDesignerStore, ['currentCpt', 'currentCptId', 'componentData'])
  },
  methods: {
    loadChart() {
      // let chartOption = JSON.parse(JSON.stringify(this.option))
      this.$echarts.dispose(this.$refs.chart)
      this.chart = null
      this.chart = this.$echarts.init(this.$refs.chart)
      let chartOption = Object.assign({}, this.option)
      this.$emit('chartOption', chartOption)
    }
  }
}
</script>
