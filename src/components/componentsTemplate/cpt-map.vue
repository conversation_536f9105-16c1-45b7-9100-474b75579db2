<template>
  <div class="map-wrapper">
    <div v-if="option.attribute.url">
      <component
        :is="option.attribute.url"
        :width="($attrs.width * (isPad ? 50 : 100)) / topicData.topicWidth + 'vw'"
        :height="($attrs.height * (isPad ? 50 : 100)) / topicData.topicWidth + 'vw'"
      ></component>
    </div>
    <div v-else class="map-container" style="width: 100%; height: 100%">
      <sdMap
        :width="($attrs.width * (isPad ? 50 : 100)) / topicData.topicWidth + 'vw'"
        :height="($attrs.height * (isPad ? 50 : 100)) / topicData.topicWidth + 'vw'"
      />
    </div>
  </div>
</template>
<script>
import { mapWritableState } from 'pinia'
import { topicDataStore } from '@/stores/component.js'
import sdMap from './sd-map.vue'
export default {
  name: 'CptMap',
  components: { sdMap },
  props: {
    option: {
      type: Object,
      default: () => ({ url: '' })
    }
  },
  computed: {
    ...mapWritableState(topicDataStore, ['topicData', 'isPad'])
  },
  watch: {
    'option.attribute.url'(url) {
      if (url) {
        this.$options.components[url] = () => import(`@/components/custom/${url}.vue`)
      }
    }
  },
  created() {
    if (this.option.attribute.url) {
      this.$options.components[this.option.attribute.url] = () =>
        import(`@/components/custom/${this.option.attribute.url}.vue`)
      return
    }
  }
}
</script>
