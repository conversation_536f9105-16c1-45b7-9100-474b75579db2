<!-- 柱线混合图·已改造 -->
<template>
  <common-echart-dom
    :id="id"
    ref="chart"
    :width="width"
    :height="height"
    :option="option"
    :index-id="indexId"
    :params-for-un-binding-comp="paramsForUnBindingComp"
    @chartOption="loadChart"
  />
</template>

<script>
export default {
  name: 'BarLineChart',
  props: {
    width: Number,
    height: Number,
    option: {
      type: Object,
      default: () => ({})
    },
    indexId: {
      type: String,
      default: ''
    },
    paramsForUnBindingComp: {
      type: Object,
      default: () => ({
        tableName: ''
      })
    },
    id: {
      type: String,
      default: ''
    }
  },
  methods: {
    loadChart(chartOption) {
      chartOption.xAxis.data = this.$refs.chart.dimensionData[0].data
      chartOption.xAxis.name = this.$refs.chart.dimensionData[0].name
      let barUnit, lineUnit
      this.$refs.chart.indexData.forEach((data, i) => {
        chartOption.series[i].data = data.data
        chartOption.series[i].name = data.name
        chartOption.legend.data[i] = data.name
        if (chartOption.series[i].type === 'bar' || chartOption.series[i].type === 'pictorialBar') {
          barUnit = data.unit
          chartOption.series[i].yAxisIndex = 0
        } else {
          lineUnit = data.unit
          chartOption.series[i].yAxisIndex = 1
        }
      })
      chartOption.yAxis[0].name = '单位: ' + barUnit
      chartOption.yAxis[1].name = '单位: ' + lineUnit
      chartOption.tooltip.formatter = function (params) {
        let formatterValue = params[0].axisValue + '</br>'
        params.forEach((param) => {
          formatterValue +=
            param.seriesName + '&nbsp;' + param.value + (param.seriesType === 'bar' ? barUnit : lineUnit) + '</br>'
        })

        return formatterValue
      }
      try {
        this.$refs.chart.chart.setOption(chartOption)
      } catch (e) {}
    }
  }
}
</script>

<style scoped></style>
