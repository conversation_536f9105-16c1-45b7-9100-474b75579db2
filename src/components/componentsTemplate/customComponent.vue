<template>
  <div
    :id="uuid"
    :class="currentComponent ? 'customCpt-container' : 'empty-class'"
    :style="{
      width: (width * (isPad ? 50 : 100)) / topicData.topicWidth + 'vw',
      height: (height * (isPad ? 50 : 100)) / topicData.topicWidth + 'vw'
    }"
  >
    <!-- <div v-if="!currentComponent">自定义组件</div> -->
    <component :is="currentComponent" v-if="currentComponent"></component>
  </div>
</template>
<script>
import { mapWritableState } from 'pinia'
import { topicDataStore } from '@/stores/component.js'

const customComponents = import.meta.glob('@/components/custom/**/*.vue')

// 创建路径映射表
const componentMap = {}
for (const key in customComponents) {
  let normalizedKey = key.replace(/^.*\/components\/custom\//, '')
  normalizedKey = normalizedKey.replace(/\.vue$/, '')
  // normalizedKey = normalizedKey.replace(/\/index$/, '')
  componentMap[normalizedKey] = customComponents[key]

  if (normalizedKey.endsWith('/index')) {
    const dirKey = normalizedKey.replace(/\/index$/, '')
    if (!componentMap[dirKey]) {
      componentMap[dirKey] = customComponents[key]
    }
  }
}

export default {
  name: 'CustomComponent',
  props: {
    option: Object,
    width: Number,
    height: Number
  },
  data() {
    return {
      uuid: '',
      currentComponent: null
    }
  },
  computed: {
    ...mapWritableState(topicDataStore, ['topicData', 'componentData', 'isPad'])
  },
  watch: {
    'option.attribute.url': {
      handler() {
        this.loadCustomComponent()
      },
      immediate: true
    }
  },
  created() {
    this.uuid = this.$uuid()
    this.loadCustomComponent()
  },
  methods: {
    async loadCustomComponent() {
      if (!this.option?.attribute?.url) {
        this.currentComponent = null
        return
      }
      const url = this.option.attribute.url
      if (componentMap[url]) {
        try {
          const module = await componentMap[url]()
          this.currentComponent = module.default
        } catch (e) {
          this.currentComponent = null
        }
      } else {
        this.currentComponent = null
      }
    }
  }
}
</script>
<style lang="less" scoped>
.empty-class {
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
