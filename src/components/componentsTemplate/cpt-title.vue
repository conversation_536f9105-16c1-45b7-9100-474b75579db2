<template>
  <div class="title" :class="{ 'sub-title': !!option.attribute.type }" :style="computedStyle">
    {{ option.attribute.content }}
  </div>
</template>
<script>
import { mapWritableState } from 'pinia'
import { topicDataStore } from '@/stores/component'
export default {
  name: 'CptTitle',
  props: {
    id: String,
    option: Object
  },
  data() {
    return {}
  },
  computed: {
    ...mapWritableState(topicDataStore, ['topicData', 'isPad']),
    computedStyle() {
      return this.$convertPxToVw(this.$getStyle(this.option.attribute.style), this.topicData?.topicWidth, this.isPad)
    }
  },
  created() {}
}
</script>
<style lang="less" scoped>
.sub-title {
  padding-left: 22px;
  position: relative;
  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 10px;
    height: 31px;
    width: 12px;
    background: url('@/assets/img/title-before.png') no-repeat center center / 100% 100%;
  }
}
@media screen and (min-width: 600px) {
  .sub-title {
    padding-left: 11px;
    position: relative;
    &::before {
      content: '';
      position: absolute;
      width: 0.8vw;
      height: 2.06667vw;
      top: 0.6vw;
      left: 0;
      background: url('@/assets/img/title-before.png') no-repeat center center / 100% 100%;
    }
  }
}
</style>
