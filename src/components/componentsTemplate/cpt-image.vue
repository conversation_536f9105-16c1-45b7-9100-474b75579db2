<!-- 图片·已改造 -->
<template>
  <div
    :style="{
      width: ($attrs.width * (isPad ? 50 : 100)) / topicData.topicWidth + 'vw',
      height: ($attrs.height * (isPad ? 50 : 100)) / topicData.topicWidth + 'vw'
    }"
  >
    <el-image
      style="width: 100%; height: 100%"
      :preview-src-list="option.attribute.preview ? [option.attribute.url] : []"
      :src="option.attribute.url ? fileUrl + option.attribute.url : ''"
      :fit="option.attribute.fit"
      @dragstart.prevent
    />
  </div>
</template>

<script>
import { mapWritableState } from 'pinia'
import { topicDataStore } from '@/stores/component.js'
export default {
  name: 'CptImage',
  props: { option: Object },
  data() {
    return {
      fileUrl: import.meta.env.VITE_APP_API_BASE_URL + '/cockpit/'
    }
  },
  computed: {
    ...mapWritableState(topicDataStore, ['topicData', 'componentData', 'isPad'])
  }
}
</script>

<style scoped></style>
