<!-- 已改造 -->
<template>
  <div
    :id="uuid"
    ref="chart"
    :style="{
      width: (width * (isPad ? 50 : 100)) / topicData.topicWidth + 'vw',
      height: (height * (isPad ? 50 : 100)) / topicData.topicWidth + 'vw'
    }"
  ></div>
</template>
<script>
import { loadDataMixin } from './loadDataMixin'
import { mapWritableState } from 'pinia'
import { topicDataStore } from '@/stores/component.js'
export default {
  name: 'CommonEchartDom',
  mixins: [loadDataMixin],
  props: {
    width: Number,
    height: Number
  },
  computed: {
    ...mapWritableState(topicDataStore, ['topicData', 'componentData', 'isPad'])
  },
  methods: {
    loadChart() {
      if (!this.$refs.chart) return
      // let chartOption = JSON.parse(JSON.stringify(this.option))
      this.$echarts.dispose(this.$refs.chart)
      this.chart = null
      this.chart = this.$echarts.init(this.$refs.chart)
      let chartOption = Object.assign({}, this.option)
      this.$emit('chartOption', chartOption)
    }
  }
}
</script>
