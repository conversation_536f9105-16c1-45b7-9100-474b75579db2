<!-- 基础条形图·已改造 -->
<template>
  <common-echart-dom
    :id="id"
    ref="chart"
    :width="width"
    :height="height"
    :option="option"
    :index-id="indexId"
    :params-for-un-binding-comp="paramsForUnBindingComp"
    @chartOption="loadChart"
  />
</template>

<script>
export default {
  name: 'BasicBarChart',
  props: {
    width: Number,
    height: Number,
    option: {
      type: Object,
      default: () => ({})
    },
    indexId: {
      type: String,
      default: ''
    },
    paramsForUnBindingComp: {
      type: Object,
      default: () => ({
        tableName: ''
      })
    },
    id: {
      type: String,
      default: ''
    }
  },
  methods: {
    loadChart(chartOption) {
      // let chartOption = JSON.parse(JSON.stringify(this.option))
      chartOption.yAxis.data = this.$refs.chart.dimensionData[0].data
      chartOption.yAxis.name = this.$refs.chart.dimensionData[0].name
      chartOption.xAxis.name = '单位: ' + this.$refs.chart.indexData[0].unit
      chartOption.series[0].data = this.$refs.chart.indexData[0].data
      chartOption.series[0].name = this.$refs.chart.indexData[0].name
      this.$refs.chart.chart.setOption(chartOption)
    }
  }
}
</script>

<style scoped></style>
