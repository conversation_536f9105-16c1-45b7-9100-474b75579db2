<template>
  <div :id="uuid" class="container-group" :style="{ width: width + 'px', height: height + 'px' }">
    <div
      v-for="(row, rowIndex) in computedLayoutIndex"
      :key="rowIndex"
      class="container-row"
      :style="getRowStyle(rowIndex)"
    >
      <div
        v-for="containerIndex in row"
        :key="'container' + containerIndex"
        class="container"
        :style="getContainerStyle(row)"
      >
        <div class="dimenssion" :style="dimenssionStyle">
          {{ chartData.data[containerIndex]?.dimensionData }}
        </div>
        <div class="index" :style="indexStyle">
          {{ chartData.data[containerIndex]?.indexData
          }}<span :style="unitStyle">{{ chartData.data[containerIndex]?.unit }}</span>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { getFileAccessHttpUrl, getAction, postAction } from '@/api/manage'
export default {
  name: 'TextContainerGroup',
  props: {
    option: Object,
    width: Number,
    height: Number,
    indexId: {
      type: String,
      default: ''
    },
    id: {
      type: String,
      default: ''
    },
    paramsForUnBindingComp: {
      type: Object,
      default: () => ({
        tableName: ''
      })
    }
  },
  data() {
    return {
      chartData: {
        data: [],
        dimensionName: '',
        indexName: ''
      },
      layoutIndex: [],
      dimenssionStyle: {},
      indexStyle: {},
      unitStyle: {},
      querys: []
    }
  },
  computed: {
    computedLayoutIndex() {
      if (!this.option?.attribute?.layout) {
        return this.chartData.data.map((_, index) => [index])
      }
      return this.option.attribute.layout.split('*').reduce((acc, curr) => {
        const start = acc.flat().length || 0
        acc.push(Array.from({ length: +curr }, (_, i) => start + i))
        return acc
      }, [])
    },
    backgroundImage() {
      return this.option.attribute.bgImage ? getFileAccessHttpUrl(this.option.attribute.bgImage) : ''
    }
  },
  watch: {
    'option.attribute': {
      handler: 'refreshCptData',
      deep: true,
      immediate: true
    },
    paramsForUnBindingComp: {
      handler(val) {
        val && this.loadData()
      },
      deep: true
    }
  },
  created() {
    this.uuid = require('nanoid').nanoid(12)
  },
  mounted() {
    this.refreshCptData()
    this.$bus.$on('onTabClick', (columnInfo) => {
      if (columnInfo.elementsId.some((element) => element.id === this.id)) {
        const index = columnInfo.elementsId.findIndex((element) => element.id === this.id)
        const query = {
          value: columnInfo.option,
          column: columnInfo.columns[index].name
        }
        const foundIndex = this.querys.findIndex((item) => item.column === query.column)
        if (foundIndex < 0) this.querys.push(query)
        else this.querys[foundIndex].value = query.value
        this.loadData(this.querys)
      }
    })
  },
  methods: {
    refreshCptData() {
      // 从左侧拖入模板组件，无绑定数据，不执行数据加载方法
      this.parseStyleSettings()
      this.loadData()
      // pollingRefresh(this.uuid, this.option.cptDataForm, this.loadData)
    },
    parseStyleSettings() {
      this.dimenssionStyle = this.parseStyle(this.option.attribute.dimenssionStyle)
      this.indexStyle = this.parseStyle(this.option.attribute.indexStyle)
      this.unitStyle = this.parseStyle(this.option.attribute.unitStyle)
    },
    parseStyle(styleStr, defaultValue = {}) {
      try {
        if (styleStr) {
          return new Function('return ' + styleStr)() // 字符串转对象，并去掉了'option = ''
        }
        return defaultValue
      } catch (e) {
        return defaultValue
      }
    },
    async loadData(querys = []) {
      let dimensionList, indexList, dataRes
      if (this.indexId) {
        // 已绑定组件
        const indexSetInfoRes = await getAction('cockpit/app/menu/queryAppIndexById', {
          indexId: this.indexId
        })
        const indexSetInfo = indexSetInfoRes.result
        dimensionList = indexSetInfo.groupColumnList.filter((column) => {
          return column.indexType === 'dimenssion'
        })
        indexList = indexSetInfo.groupColumnList.filter((column) => {
          return column.indexType === 'index'
        })
        dataRes = await postAction(`cockpit/index/scCockpitIndexGroupConfig/previewData?id=${this.indexId}`, {
          connType: '',
          pageNo: 1,
          pageSize: 1000,
          where: querys.length
            ? querys.map((item) => {
                return { column: item.column, value: item.value, operator: 'eq' }
              })
            : []
        })
      } else if (this.paramsForUnBindingComp.tableName) {
        // 模板组件
        const { tableName, dataSourceCode, ...rest } = this.paramsForUnBindingComp
        indexList = rest.indexList
        dimensionList = rest.dimensionList
        dataRes = await postAction('/index/scCockpitIndexGroupConfig/previewDataWithOutSave', {
          connType: 'And',
          pageNo: 1,
          pageSize: 1000,
          where: querys.length
            ? querys.map((item) => {
                return { column: item.column, value: item.value, operator: 'eq' }
              })
            : [],
          tableName,
          columns: [
            ...indexList.map((index) => {
              return index.columnName
            }),
            ...dimensionList.map((dimension) => {
              return dimension.columnName
            }),
            ...(this.paramsForUnBindingComp.unitList || [])
          ],
          dataSourceCode
        })
      } else {
        return
      }
      const data = dataRes.result.records
      const [dimension] = dimensionList || []
      const [index] = indexList || []

      if (!dimension || !index) return
      this.chartData = {
        dimensionName: dimension.indexName || dimension.columnComment,
        indexName: index.indexName || index.columnComment,
        data: data.map((item) => ({
          dimensionData: item[dimension.sourceColumn || dimension.columnName],
          indexData: this.formatIndexData(item[index.sourceColumn || index.columnName]),
          unit: this.getUnit(index, item)
        }))
      }
    },
    getUnit(index, item) {
      if (this.option.attribute.unit || index.unit) return this.option.attribute.unit || index.unit
      if (this.paramsForUnBindingComp.unitList) return item[this.paramsForUnBindingComp.unitList[0]]
      return ''
    },
    formatIndexData(value) {
      return typeof value === 'string' ? value.trim() : value
    },
    getFileAccessHttpUrl(url) {
      return getFileAccessHttpUrl(url)
    },
    getRowStyle(rowIndex) {
      const { rowGap } = this.option.attribute
      const totalRows = this.computedLayoutIndex.length
      return {
        marginBottom: rowIndex < totalRows - 1 ? `${rowGap}px` : 0,
        height: `calc((100% - ${(totalRows - 1) * rowGap}px) / ${totalRows})`
      }
    },
    getContainerStyle(row) {
      const { columnGap } = this.option.attribute
      return {
        background: this.backgroundImage ? `url(${this.backgroundImage}) no-repeat center center / 100% 100%` : 'none',
        width: `calc((100% - ${(row.length - 1) * columnGap}px) / ${row.length})`
      }
    }
  }
}
</script>
<style lang="less" scoped>
.container-group {
  height: 100%;
  width: 100%;
}
.container-row {
  display: flex;
  justify-content: space-between;
  text-align: center;
}
.container {
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  position: relative;
}
</style>
