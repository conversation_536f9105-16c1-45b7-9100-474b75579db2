import { getAction, postAction } from '@/api/manage'
// import { getDictItemsFromCache } from '@/api/api'
export const loadDataMixin = {
  props: {
    width: Number,
    height: Number,
    option: {
      type: Object,
      default: () => ({})
    },
    indexId: {
      type: String,
      default: ''
    },
    paramsForUnBindingComp: {
      type: Object,
      default: () => ({
        tableName: ''
      })
    },
    id: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      uuid: '',
      chartOption: {},
      chart: undefined,
      querys: [], // 可能有多个查询控件绑定
      queryParam: {},
      dimensionData: [],
      indexData: [],
      scrollInterval: null
    }
  },
  watch: {
    width() {
      this.chart.resize()
    },
    height() {
      this.chart.resize()
    },
    indexId(val) {
      val && this.loadData()
    },
    paramsForUnBindingComp: {
      handler(val) {
        val && this.loadData()
      },
      deep: true
    }
  },
  created() {
    this.uuid = this.$uuid()
  },
  mounted() {
    this.chart = this.$echarts.init(this.$refs.chart)
    this.refreshCptData()
    window.addEventListener('resize', () => {
      this.chart && this.chart.resize()
    })
    this.$bus.$on('onTabClick', (columnInfo) => {
      if (columnInfo.elementsId.some((element) => element.id === this.id)) {
        const index = columnInfo.elementsId.findIndex((element) => element.id === this.id)
        const query = {
          value: columnInfo.option,
          column: columnInfo.columns[index].name
        }
        const foundIndex = this.querys.findIndex((item) => item.column === query.column)
        if (foundIndex < 0) this.querys.push(query)
        else this.querys[foundIndex].value = query.value
        this.loadData(this.querys)
      }
    })
  },
  beforeDestroy() {
    this.$bus.$off('onTabClick')
    window.removeEventListener('resize', () => {
      this.chart && this.chart.resize()
    })
  },
  methods: {
    refreshCptData() {
      this.loadData()
      // pollingRefresh(this.uuid, this.option.cptDataForm, this.loadData)
    },
    async loadData(querys = []) {
      let dataRes, dimensionList, indexList
      if (!this.indexId && !this.option.attribute.indexSet) return
      // 已绑定组件
      const indexSetInfoRes = await getAction('/cockpit/app/menu/queryAppIndexById', {
        indexId: this.indexId || this.option.attribute.indexSet
      })
      if (indexSetInfoRes.success) {
        const indexSetInfo = indexSetInfoRes.result
        if (!this.option.attribute.indexUsed) {
          indexList = indexSetInfo.groupColumnList.filter((column) => {
            column.indexType === 'index'
          })
        } else {
          indexList = this.option.attribute.indexUsed.map((index) => {
            return indexSetInfo.groupColumnList.filter((column) => index === column.sourceColumn)[0]
          })
        }
        if (!indexList.length) return
        if (!this.option.attribute.dimenssionUsed) {
          dimensionList = indexSetInfo.groupColumnList.filter((column) => {
            return (
              column.indexType === 'dimenssion' &&
              !indexSetInfoRes.result.queryColumnList.some((item) => item.sourceColumn === column.sourceColumn)
            )
          })
        } else {
          dimensionList = this.option.attribute.dimenssionUsed.map((index) => {
            return indexSetInfo.groupColumnList.filter((column) => index === column.sourceColumn)[0]
          })
        }
        dataRes = await postAction(
          `/cockpit/index/scCockpitIndexGroupConfig/previewData?id=${this.indexId || this.option.attribute.indexSet}`,
          {
            connType: 'And',
            pageNo: 1,
            pageSize: 1000,
            where: querys.length
              ? querys.map((query) => {
                  return { column: query.column, value: query.value, operator: 'eq' }
                })
              : []
          }
        )
      }

      const data = dataRes?.result.records
      this.dimensionData = dimensionList?.map((dimension) => {
        return {
          name: dimension.columnComment || dimension.indexName,
          data: data.map((element) => {
            return element[dimension.columnName || dimension.sourceColumn]
          })
        }
      })
      this.indexData = indexList?.map((index, i) => {
        return {
          name: index.columnComment || index.indexName,
          data: data.map((element) => {
            return element[index.columnName || index.sourceColumn]
          }),
          unit: this.getUnit(index, data, i)
        }
      })
      this.loadChart()
    },
    getUnit(index, data, i) {
      if (index.unit) return index.unit // 已绑定指标集的组件，存在unit字段，可直接赋值
      if (this.paramsForUnBindingComp.unitList) {
        if (this.currentCpt.cptOption.attribute.barColumns) {
          // 分组柱线图和堆叠柱线图需要根据柱个数设置多个单位
          return data[0][
            this.paramsForUnBindingComp.unitList[i < this.currentCpt.cptOption.attribute.barColumns ? 0 : 1]
          ]
        } else {
          // 其余最多设置两个指标单位即可
          return data[0][this.paramsForUnBindingComp.unitList[i]]
        }
      }
      return ''
    },
    /**
     * 日期计算
     * @param dateStr 原日期字符串
     * @param value 加减的数值
     * @param unit 加减的日期单位
     */
    adjustDate(value, unit) {
      // 解析原始日期
      const date = new Date() // 替换为兼容格式
      // 根据单位进行增减操作
      switch (unit.toLowerCase()) {
        case 'year':
          date.setFullYear(date.getFullYear() + value)
          break
        case 'month':
          date.setMonth(date.getMonth() + value)
          break
        case 'day':
          date.setDate(date.getDate() + value)
          break
        case 'hour':
          date.setHours(date.getHours() + value)
          break
        case 'minute':
          date.setMinutes(date.getMinutes() + value)
          break
        case 'second':
          date.setSeconds(date.getSeconds() + value)
          break
        default:
          throw new Error('Invalid time unit')
      }

      // 格式化为字符串
      const pad = (num) => num.toString().padStart(2, '0')
      return (
        [date.getFullYear(), pad(date.getMonth() + 1), pad(date.getDate())].join('-') +
        ' ' +
        [pad(date.getHours()), pad(date.getMinutes()), pad(date.getSeconds())].join(':')
      )
    }
  }
}
