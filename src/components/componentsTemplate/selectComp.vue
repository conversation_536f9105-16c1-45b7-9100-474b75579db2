<!-- 下拉框·已改造 -->
<template>
  <div class="select-container">
    <div
      :id="uuid"
      class="select"
      :style="{
        width: ((isPad ? 50 : 100) * width) / topicData.topicWidth + 'vw',
        height: ((isPad ? 50 : 100) * height) / topicData.topicWidth + 'vw',
        lineHeight: ((isPad ? 50 : 100) * height) / topicData.topicWidth + 'vw',
        textAlign: 'center',
        background: '#fff',
        ...$convertPxToVw($getStyle(option.attribute.selectStyle), topicData.topicWidth, isPad)
      }"
      @click="showPicker = true"
    >
      {{ currentOption }}
      <van-icon name="play" color="#000" style="transform: rotate(90deg)" />
    </div>
    <van-popup v-model="showPicker" position="bottom" get-container="body">
      <van-picker
        show-toolbar
        :columns="customOptions && customOptions.length ? customOptions : indexSetOptions"
        @cancel="showPicker = false"
        @confirm="onSelect"
      />
    </van-popup>
  </div>
</template>
<script>
import { postAction } from '@/api/manage'
import { mapWritableState } from 'pinia'
import { topicDataStore } from '@/stores/component.js'
export default {
  name: 'SelectComp',
  props: {
    option: Object,
    width: Number,
    height: Number,
    indexId: {
      type: String,
      default: ''
    },
    paramsForUnBindingComp: {
      type: Object,
      default: () => ({
        tableName: ''
      })
    }
  },
  data() {
    return {
      uuid: '',
      indexSetOptions: [],
      currentOption: '',
      showPicker: false
    }
  },
  computed: {
    ...mapWritableState(topicDataStore, ['topicData', 'componentData', 'isPad']),
    customOptions() {
      // 自定义tab内容
      const customOptions = this.option.attribute.customOptions ? this.option.attribute.customOptions.split('*') : []
      this.onSelect(customOptions[0])
      return customOptions
    }
  },
  created() {
    this.uuid = this.$uuid()
  },
  mounted() {
    this.refreshCptData()
  },
  methods: {
    refreshCptData() {
      const inputInner = document.getElementById(this.uuid)
      if (this.$getStyle(this.option.attribute.selectStyle)) {
        const selectStyle = this.$convertPxToVw(this.$getStyle(this.option.attribute.selectStyle))
        for (const key of Object.keys(selectStyle)) {
          inputInner.style[key] = selectStyle[key]
        }
      }
      this.indexSetOptions = []
      this.option.attribute.ownColumn && this.getOptionsFromIndexSet()
      if (!this.option.attribute.customOptions && !this.option.attribute.ownColumn) {
        this.option.attribute.bindingElements &&
          this.option.attribute.bindingElements[0] &&
          this.getOptionsFromBindingElements()
      }
    },
    async getOptionsFromIndexSet() {
      const res = await postAction(
        `/cockpit/index/scCockpitIndexGroupConfig/previewData?id=${this.option.attribute.ownIndexSet}`,
        {
          connType: 'And',
          pageNo: 1,
          pageSize: 1000
        }
      )
      if (res.success && res.result.records.length) {
        const records = Array.from(
          new Set(
            res.result.records.map((record) => {
              return record[this.option.attribute.ownColumn]
            })
          )
        )
        this.indexSetOptions = records
        this.onSelect(this.indexSetOptions[0])
      } else if (!res.result.records.length) {
        this.$message.warning('该字段数据为空')
      }
    },
    async getOptionsFromBindingElements() {
      let arr = []
      this.option.attribute.bindingElements.map((element) => {
        arr.push(
          postAction(`cockpit/index/scCockpitIndexGroupConfig/previewData?id=${element.indexId}`, {
            connType: 'And',
            pageNo: 1,
            pageSize: 1000
          })
        )
      })
      const dataRes = await Promise.all(arr)
      let records = []
      // 选项取绑定元素的交集
      dataRes.map((item, index) => {
        if (!item.success) return
        const record = item.result.records
        records.push(
          record.map((recordItem) => {
            return recordItem[this.option.attribute.columns[index].name]
          })
        )
      })
      records = Array.from(new Set(records[0].filter((item) => records.every((arr) => arr.includes(item)))))
      this.indexSetOptions = records
      this.onSelect(this.indexSetOptions[0])
    },
    onSelect(text) {
      this.currentOption = text
      this.showPicker = false
      this.$bus.$emit('onTabClick', {
        elementsId: this.option.attribute.bindingElements,
        columns: this.option.attribute.columns,
        option: text
      })
    }
  }
}
</script>
<style lang="less" scoped>
.van-cell {
  display: flex;
  align-items: center;
  padding-right: 0;
  padding-left: 0;
  ::v-deep .van-field__control {
    text-align: center;
  }
}
</style>
