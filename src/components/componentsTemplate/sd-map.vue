<template>
  <div ref="chart" :style="{ width: width, height: height }" class="echarts-container map"></div>
</template>

<script>
import shanDong from '@/assets/json/shandong.json'
import * as echarts from 'echarts'
export default {
  name: 'SdMap',
  props: {
    width: {
      type: String,
      required: true
    },
    height: {
      type: String,
      required: true
    },
    inputChartData: {
      type: Array,
      default: () => null
    },
    selectedOptionPopulation: {
      type: String,
      default: ''
    },
    visualMapPieces: {
      type: Array,
      default: () => [
        {
          gt: 800,
          label: '800以上',
          color: '#00539E'
        },
        {
          gte: 600,
          lte: 800,
          label: '800-600',
          color: '#0081F6'
        },
        {
          gte: 400,
          lt: 600,
          label: '600-400',
          color: '#40A4FF'
        },
        {
          gte: 200,
          lt: 400,
          label: '400-200',
          color: '#74BDFF'
        },
        {
          gte: 0,
          lt: 200,
          label: '200-0',
          color: '#C4E3FF'
        }
      ]
    },
    unit: {
      type: String,
      default: '人/平方公里'
    },
    isDistrict: {
      type: Boolean,
      default: false
    },
    showVisualMap: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      chart: null,
      cityData: [
        {
          name: '济南市',
          coords: [117.121225, 36.66466],
          value: 943
        },
        {
          name: '青岛市',
          coords: [120.3, 36.62],
          value: 1037
        },
        {
          name: '淄博市',
          coords: [118.05, 36.78],
          value: 467
        },
        {
          name: '枣庄市',
          coords: [117.57, 34.86],
          value: 380
        },
        {
          name: '东营市',
          coords: [118.49, 37.46],
          value: 220
        },
        {
          name: '烟台市',
          coords: [120.9, 37.32],
          value: 703
        },
        {
          name: '潍坊市',
          coords: [119.1, 36.62],
          value: 936
        },
        {
          name: '济宁市',
          coords: [116.7, 35.42],
          value: 824
        },
        {
          name: '泰安市',
          coords: [117.13, 36.18],
          value: 534
        },
        {
          name: '威海市',
          coords: [122.1, 37.2],
          value: 291
        },
        {
          name: '日照市',
          coords: [119.1, 35.62],
          value: 294
        },
        {
          name: '临沂市',
          coords: [118.35, 35.05],
          value: 1094
        },
        {
          name: '德州市',
          coords: [116.39, 37.45],
          value: 553
        },
        {
          name: '聊城市',
          coords: [115.97, 36.45],
          value: 585
        },
        {
          name: '滨州市',
          coords: [118.03, 37.36],
          value: 390
        },
        {
          name: '菏泽市',
          coords: [115.480656, 35.23375],
          value: 863
        }
      ],
      option: {
        visualMap: {
          right: 10,
          bottom: 10,
          showLabel: true,
          pieces: this.visualMapPieces,
          show: this.showVisualMap
        },
        tooltip: {
          triggerOn: 'click',
          confine: true,
          backgroundColor: 'rgba(0,0,0,0.6)',
          textStyle: {
            color: 'white'
          },
          formatter: function (e) {
            return `<div>${e.name}</div>
      <div>人口密度：${e.value}人/平方公里</div>`
          },
          extraCssText: 'z-index: 0'
        },
        series: [
          {
            name: 'shanDong',
            type: 'map',
            map: 'shanDong',
            coordinateSystem: 'geo',
            data: [],
            select: {
              // disabled: true,
              // itemStyle: {
              //   areaColor: 'rgb(3, 243, 252, 0)',
              //   borderWidth: 2
              // }
            },

            zoom: 1.2,
            // geoIndex: 0,
            itemStyle: {
              borderColor: 'white',
              borderWidth: 1,
              borderType: 'solid',
              borderDashOffset: 2,
              areaColor: 'rgb(3, 243, 252, 0)'
            },
            emphasis: {
              areaColor: 'rgb(3, 243, 252, 0)',
              borderColor: 'white',
              borderWidth: 4
            },
            label: {
              show: true,
              color: '#000',
              fontSize: '10px',
              emphasis: {
                color: '#000'
              }
            }
          }
        ]
      }
    }
  },
  watch: {
    // option: {
    // 需要即时渲染则打开注释
    //   handler(newObj) {
    //     this.loadData()
    //   },
    //   deep: true
    // },
    width() {
      this.chart.resize()
    },
    height() {
      this.chart.resize()
    }
  },
  created() {},
  mounted() {
    this.initCharts()
  },
  methods: {
    async initCharts() {
      this.$echarts.dispose(this.$refs.chart)
      this.chart = null
      this.chart = this.$echarts.init(this.$refs.chart)
      echarts.registerMap('shanDong', shanDong)
      if (!this.inputChartData) {
        this.option.series[0].data = this.cityData
      }
      this.option.visualMap = {
        right: 10,
        bottom: 10,
        showLabel: true,
        pieces: [
          {
            gt: 1000,
            label: '1000以上',
            color: '#00539E'
          },
          {
            gte: 800,
            lte: 1000,
            label: '1000-800',
            color: '#0081F6'
          },
          {
            gte: 500,
            lt: 800,
            label: '800-500',
            color: '#40A4FF'
          },
          {
            gte: 300,
            lt: 500,
            label: '500-300',
            color: '#74BDFF'
          },
          {
            gte: 0,
            lt: 300,
            label: '300-0',
            color: '#C4E3FF'
          }
        ],
        selectedMode: false,
        show: this.showVisualMap
      }
      this.option.visualMap = {
        right: 10,
        bottom: 10,
        showLabel: true,
        selectedMode: false,
        pieces: [
          {
            gt: 800,
            label: '800以上',
            color: '#00539E'
          },
          {
            gte: 600,
            lte: 800,
            label: '800-600',
            color: '#0081F6'
          },
          {
            gte: 400,
            lt: 600,
            label: '600-400',
            color: '#40A4FF'
          },
          {
            gte: 200,
            lt: 400,
            label: '400-200',
            color: '#74BDFF'
          },
          {
            gte: 0,
            lt: 200,
            label: '200-0',
            color: '#C4E3FF'
          }
        ],
        show: this.showVisualMap
      }
      this.chart.setOption(this.option)
    }
  }
}
</script>
<style lang="less" scoped></style>
