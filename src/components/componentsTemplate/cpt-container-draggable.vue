<!-- 画布容器·已改造 -->
<template>
  <div
    ref="containerRef"
    class="container-draggable"
    :style="{
      width: `${($attrs.width * (isPad ? 50 : 100)) / topicData.topicWidth}vw`,
      backgroundImage: option.attribute.backgroundImage
        ? `url('${getFileAccessHttpUrl(option.attribute.backgroundImage)}')`
        : 'none',
      backgroundRepeat: 'no-repeat',
      backgroundPosition: 'center center',
      backgroundSize: '100% 100%',
      backgroundColor: option.attribute.backgroundColor,
      borderColor: option.attribute.borderColor,
      ...(option.attribute.background && {
        background: `${option.attribute.background} !important`
      }),
      borderWidth: `${(option.attribute.borderWidth * (isPad ? 50 : 100)) / topicData.topicWidth}vw`,
      borderStyle: option.attribute.borderStyle,
      borderRadius: (option.attribute.borderRadius * (isPad ? 50 : 100)) / topicData.topicWidth + 'vw',
      overflow: 'hidden',
      height: `${(($attrs.height + diff) * (isPad ? 50 : 100)) / topicData.topicWidth}vw`
    }"
  >
    <button v-if="option.attribute.isPopup" class="close-btn" @click="close">关闭</button>
    <div
      v-for="item in containerComponentData"
      v-show="item.cptOption.attribute.visible"
      :key="item.id"
      :ref="setChildRef"
      :style="{
        width: `${(item.cptWidth * (isPad ? 50 : 100)) / topicData.topicWidth}vw`,
        height: `${(item.cptHeight * (isPad ? 50 : 100)) / topicData.topicWidth}vw`,
        top:
          Math.round((item.cptY > offsetY ? item.cptY + diff : item.cptY) * (isPad ? 50 : 100)) / topicData.topicWidth +
          'vw',
        left: Math.round(item.cptX * (isPad ? 50 : 100)) / topicData.topicWidth + 'vw',
        position: 'absolute'
      }"
    >
      <component
        :is="item.cptKey"
        :id="item.id"
        :ref="item.id"
        :width="Math.round(item.cptWidth)"
        :height="Math.round(item.cptHeight)"
        :option="item.cptOption"
        :index-id="item.cptIndexId"
        :p-index-set-data="indexSetData"
        :p-index-set-id="indexSetId"
        :topic-data="topicData"
        :change-height-params="{ diff, offsetY }"
      />
    </div>
    <div
      v-if="option.attribute.showFootTimeInfo"
      class="footer"
      :style="{ width: '100%', position: 'absolute', bottom: '0', padding: `${1000 / topicData.topicWidth}vw 5%` }"
    >
      <div class="separator"></div>
      <div :id="uuid" class="grid-container">
        <div>数据来源：{{ footerTimeInfo?.dataOrigin }}</div>
        <div>更新时间：{{ footerTimeInfo?.dataTime }}</div>
        <div>更新周期：{{ footerTimeInfo?.dataFrequency }}</div>
        <div>数据期别：{{ footerTimeInfo?.syncTime }}</div>
      </div>
    </div>
  </div>
</template>

<script>
import { postAction, getFileAccessHttpUrl } from '@/api/manage'
import { findNodeById } from '@/utils/util'
import { mapWritableState } from 'pinia'
import { topicDataStore } from '@/stores/component.js'
export default {
  name: 'CptContainerDraggable',
  props: {
    id: {
      type: String,
      required: true
    },
    option: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      queryOption: [],
      indexSetData: null,
      indexSetId: '',
      childRefs: [],
      observer: null,
      footerTimeInfo: {},
      diff: 0,
      offsetY: 0,
      url: {
        previewData: '/cockpit/index/scCockpitIndexGroupConfig/previewData',
        queryIndexSet: '/cockpit/module/scCockpitModuleConfig/queryByIndexId'
      }
    }
  },
  computed: {
    ...mapWritableState(topicDataStore, ['topicData', 'componentData', 'isPad']),
    containerComponentData() {
      return findNodeById(this.id, this.componentData)?.children ?? []
    }
  },
  created() {
    this.uuid = this.$uuid()
    this.indexSetId = this.option.attribute.indexSet
    this.getIndexSetData()
    this.getFootTimeInfo()
    this.$bus.$on('pid', (pid) => {
      this.id === pid && this.refreshCptData()
    })
    this.$bus.$on('indexSetId', (data) => {
      this.indexSetId = data.indexSetId
      this.getIndexSetData()
    })
    this.$bus.$on('onTabClick', (columnInfo) => {
      if (columnInfo.elementsId.some((element) => element.id === this.id)) {
        const index = columnInfo.elementsId.findIndex((element) => element.id === this.id)
        const foundIndex = this.queryOption.findIndex((item) => item.column === columnInfo.columns[index].name)
        if (foundIndex < 0)
          this.queryOption.push({
            value: columnInfo.option,
            column: columnInfo.columns[index].name
          })
        else this.queryOption[foundIndex].value = columnInfo.option
        this.getIndexSetData()
      }
    })
    this.$bus.$on('changeHeight', (property) => {
      // 改变当前容器高度，并改变对应组件下方所有组件的位置
      if (this.id === property.id) {
        this.diff += property.diff
        this.offsetY = property.offsetY
      }
    })
  },
  beforeDestroy() {
    this.$bus.$off('pid')
    this.$bus.$off('indexSetId')
    this.$bus.$off('onTabClick')
    this.$bus.$off('changeHeight')
    // 清理观察器
    if (this.observer) {
      this.observer.disconnect()
      this.observer = null
    }
  },
  methods: {
    setChildRef(el) {
      if (el) {
        this.childRefs.push(el)
      }
    },
    refreshCptData() {
      const refName = this.currentCptId
      if (!this.$refs[refName][0].refreshCptData) {
        this.$message.warning('当前图层还未实现refreshCptData方法')
      } else {
        this.$refs[refName][0].refreshCptData() //刷新子组件数据，refs为组建名加index
      }
    },
    getIndexSetData() {
      const params = {
        connType: 'And',
        pageNo: 1,
        pageSize: 1000,
        where: this.queryOption.length
          ? this.queryOption.map((item) => {
              return { column: item.column, value: item.value, operator: 'eq' }
            })
          : []
      }
      if (this.indexSetId) {
        postAction(`/cockpit/index/scCockpitIndexGroupConfig/previewData?id=${this.indexSetId}`, params).then((res) => {
          this.indexSetData = res.result.records
        })
      }
      const node = findNodeById(this.id, this.componentData)
      if (node && node.children) {
        for (const child of node.children) {
          child.pIndexSetId = this.indexSetId
        }
      }
    },
    getFootTimeInfo() {
      if (!this.option.attribute.showFootTimeInfo) return
      this.footerTimeInfo = this.option.attribute.updateInfo
      if (!this.footerTimeInfo) return
    },
    getFileAccessHttpUrl(url) {
      return getFileAccessHttpUrl(url)
    },
    open() {
      this.$emit('popup', true)
    },
    close() {
      this.$emit('popup', false)
    }
  }
}
</script>

<style lang="less" scoped>
.container-draggable {
  position: relative;
  overflow: visible; /* 确保内容不会被裁剪 */
  min-height: 30px; /* 最小高度防止空容器过小 */
}
.container-draggable {
  position: relative;
  isolation: isolate;
}
.close-btn {
  position: absolute;
  right: 0;
  top: 0;
  z-index: 10000;
}
.separator {
  border: 1px dashed #cbcbcb;
  margin-bottom: 20px;
}
.grid-container {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 24px;
  color: #999999;
  line-height: 24px;
  font-style: normal;
  display: grid;
  grid-template-columns: repeat(2, auto);
  row-gap: 22px;
  justify-content: space-between;
  > div:nth-child(even) {
    text-align: right;
  }
  > div:nth-child(odd) {
    text-align: left;
  }
}
@media screen and (min-width: 600px) {
  .separator {
    margin-bottom: 10px;
  }
  .grid-container {
    font-size: 12px;
    line-height: 12px;
    row-gap: 11px;
  }
}
</style>
