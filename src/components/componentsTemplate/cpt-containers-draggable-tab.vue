<template>
  <div
    class="containers-draggable-tab"
    :style="{
      width: `${($attrs.width * (isPad ? 50 : 100)) / topicData.topicWidth}vw`,
      height: `${($attrs.height * (isPad ? 50 : 100)) / topicData.topicWidth}vw`,
      backgroundImage: option.attribute.backgroundImage
        ? `url('${getFileAccessHttpUrl(option.attribute.backgroundImage)}')`
        : 'none',
      backgroundRepeat: 'no-repeat',
      backgroundPosition: 'center center',
      backgroundSize: '100% 100%',
      backgroundColor: option.attribute.backgroundColor,
      borderColor: option.attribute.borderColor,
      borderWidth: `${(option.attribute.borderWidth * (isPad ? 50 : 100)) / topicData.topicWidth}vw`,
      borderRadius: option.attribute.borderRadius,
      borderStyle: option.attribute.borderStyle
    }"
  >
    <button v-if="option.attribute.isPopup" class="close-btn" @click="close">关闭</button>
    <div v-if="!option.attribute.tabType" style="display: flex; justify-content: space-around; width: 100%">
      <div
        v-for="(item, index) in option.attribute.tabContent.length"
        :key="item"
        :style="{
          cursor: 'pointer',
          ...$convertPxToVw(
            $getStyle(index === activeTab ? option.attribute.activeTabStyle : option.attribute.tabStyle),
            topicData.topicWidth,
            isPad
          )
        }"
        @click="changeActiveTab(index)"
      >
        {{ option.attribute.tabContent[item - 1] }}
      </div>
    </div>
    <el-tabs
      v-else-if="option.attribute.tabType === 1"
      style="width: 100%"
      @tab-click="(tab) => changeActiveTab(tab.index)"
    >
      <el-tab-pane
        v-for="item in option.attribute.tabContent.length"
        :key="item"
        class="tab"
        :label="option.attribute.tabContent[item - 1]"
      ></el-tab-pane>
    </el-tabs>
    <div
      v-for="item in containerComponentData"
      v-show="
        item.cptOption.attribute.visible &&
        (!option.attribute.innerContainer.some((element) => element === item.id) ||
          option.attribute.innerContainer.findIndex((element) => element === item.id) == activeTab)
      "
      :key="item.id"
      :ref="setChildRef"
      :style="{
        width: `${(item.cptWidth * (isPad ? 50 : 100)) / topicData.topicWidth}vw`,
        height: `${(item.cptHeight * (isPad ? 50 : 100)) / topicData.topicWidth}vw`,
        top: (Math.round(item.cptY) * (isPad ? 50 : 100)) / topicData.topicWidth + 'vw',
        left: (Math.round(item.cptX) * (isPad ? 50 : 100)) / topicData.topicWidth + 'vw',
        position: 'absolute'
      }"
      class="shape-in-draggable"
    >
      <component
        :is="item.cptKey"
        :id="item.id"
        :ref="item.id"
        :width="Math.round(item.cptWidth)"
        :height="Math.round(item.cptHeight)"
        :option="item.cptOption"
        :index-id="item.cptIndexId"
        :p-index-set-data="indexSetData"
        :query-option="queryOption"
        :p-index-set-id="indexSetId"
        :topic-data="topicData"
      />
    </div>
    <div
      v-if="option.attribute.showFootTimeInfo && footerTimeInfo"
      class="footer"
      :style="{ width: '100%', position: 'absolute', bottom: '0', padding: `${1000 / topicData.topicWidth}vw 5%` }"
    >
      <div class="separator"></div>
      <div class="grid-container">
        <div>数据来源：{{ footerTimeInfo.dataOrigin }}</div>
        <div>更新时间：{{ footerTimeInfo.dataTime }}</div>
        <div>更新周期：{{ footerTimeInfo.dataFrequency }}</div>
        <div>数据期别：{{ footerTimeInfo.syncTime }}</div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapWritableState } from 'pinia'
import { topicDataStore } from '@/stores/component.js'
import { postAction, getFileAccessHttpUrl } from '@/api/manage'
import { findNodeById } from '@/utils/util'
export default {
  name: 'CptContainersDraggableTab',
  props: {
    id: {
      type: String,
      required: true
    },
    option: {
      type: Object,
      required: true
    },
    isPreview: {
      type: Boolean,
      default: false
    },
    containerScale: {
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      isDragOver: false,
      activeTab: 0,
      childRefs: [],
      indexSetData: null,
      indexSetId: '',
      footerTimeInfo: null,
      queryOption: [],
      url: {
        previewData: 'cockpit/index/scCockpitIndexGroupConfig/previewData',
        queryIndexSet: 'cockpit/module/scCockpitModuleConfig/queryByIndexId'
      }
    }
  },
  computed: {
    ...mapWritableState(topicDataStore, ['topicData', 'componentData', 'isPad']),
    containerComponentData() {
      return findNodeById(this.id, this.componentData)?.children ?? []
    }
  },
  created() {
    this.indexSetId = this.option.attribute.indexSet
    this.getIndexSetData()
    this.getFootTimeInfo()
    this.$bus.$on('pid', (pid) => {
      this.id === pid && this.refreshCptData()
    })
    this.$bus.$on('indexSetId', (data) => {
      if (this.id === data.pid) {
        this.indexSetId = data.indexSetId
        this.getIndexSetData()
      }
    })
    this.$bus.$on('onTabClick', (columnInfo) => {
      if (columnInfo.elementsId.some((element) => element.id === this.id)) {
        const index = columnInfo.elementsId.findIndex((element) => element.id === this.id)
        const foundIndex = this.queryOption.findIndex((item) => item.column === columnInfo.columns[index].name)
        if (foundIndex < 0)
          this.queryOption.push({
            value: columnInfo.option,
            column: columnInfo.columns[index].name
          })
        else this.queryOption[foundIndex].value = columnInfo.option
        this.getIndexSetData()
      }
    })
  },
  beforeDestroy() {
    this.$bus.$off('pid')
    this.$bus.$off('indexSetId')
    this.$bus.$off('onTabClick')
  },
  methods: {
    setChildRef(el) {
      if (el) {
        this.childRefs.push(el)
      }
    },
    refreshCptData() {
      const refName = this.currentCptId
      const cptRef = this.$refs[refName]
      let componentInstance = Array.isArray(cptRef) ? cptRef[0] : cptRef
      if (componentInstance.$refs.chart) componentInstance = componentInstance.$refs.chart
      if (!componentInstance.refreshCptData) {
        this.$message.warning('当前图层还未实现refreshCptData方法')
      } else {
        componentInstance.refreshCptData() //刷新子组件数据，refs为组建名加index
      }
    },
    getIndexSetData() {
      const params = Object.assign({
        connType: 'And',
        pageNo: 1,
        pageSize: 1000,
        where: this.queryOption.length
          ? this.queryOption.map((item) => {
              return { column: item.column, value: item.value, operator: 'eq' }
            })
          : []
      })
      if (this.indexSetId) {
        postAction(`/index/scCockpitIndexGroupConfig/previewData?id=${this.indexSetId}`, params).then((res) => {
          this.indexSetData = res.result.records
        })
      }
      const node = findNodeById(this.id, this.componentData)
      if (node && node.children) {
        for (const child of node.children) {
          child.pIndexSetId = this.indexSetId
        }
      }
    },
    getFootTimeInfo() {
      if (!this.option.attribute.showFootTimeInfo) return
      this.footerTimeInfo = this.option.attribute.updateInfo
      if (!this.footerTimeInfo) return
    },
    getFileAccessHttpUrl(url) {
      return getFileAccessHttpUrl(url)
    },
    changeActiveTab(index) {
      this.activeTab = index
    },
    open() {
      this.$emit('popup', true)
    },
    close() {
      this.$emit('popup', false)
    }
  }
}
</script>

<style scoped>
.containers-draggable-tab {
  position: relative;
  overflow: hidden;
}

.containers-draggable-tab {
  position: relative;
  isolation: isolate;
}
.close-btn {
  position: absolute;
  right: 0;
  top: 0;
  z-index: 10000;
}
.separator {
  border: 1px dashed #cbcbcb;
  margin-bottom: 20px;
}
.grid-container {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 24px;
  color: #999999;
  line-height: 24px;
  font-style: normal;
  display: grid;
  row-gap: 22px;
  grid-template-columns: repeat(2, auto);
  justify-content: space-between;
  > div:nth-child(even) {
    text-align: right;
  }
  > div:nth-child(odd) {
    text-align: left;
  }
}
::v-deep .el-tabs__item {
  font-family: PingFang SC;
  font-weight: 700;
  font-size: 30px;
  margin: 9px 0;
  text-align: center;
}
</style>
