<!-- 堆叠柱状图·已改造 -->
<template>
  <common-echart-dom
    :id="id"
    ref="chart"
    :width="width"
    :height="height"
    :option="option"
    :index-id="indexId"
    :params-for-un-binding-comp="paramsForUnBindingComp"
    @chartOption="loadChart"
  />
</template>

<script>
export default {
  name: 'StackedHistogramChart',
  props: {
    width: Number,
    height: Number,
    option: {
      type: Object,
      default: () => ({})
    },
    indexId: {
      type: String,
      default: ''
    },
    paramsForUnBindingComp: {
      type: Object,
      default: () => ({
        tableName: ''
      })
    },
    id: {
      type: String,
      default: ''
    }
  },
  methods: {
    loadChart(chartOption) {
      chartOption.xAxis.data = this.$refs.chart.dimensionData[0].data
      chartOption.xAxis.name = this.$refs.chart.dimensionData[0].name
      chartOption.yAxis.name = '单位: ' + this.$refs.chart.indexData[0].unit
      this.$refs.chart.indexData.forEach((data, i) => {
        chartOption.series[i].data = data.data
        chartOption.series[i].name = data.name
        chartOption.series[i].stack = '1'
        chartOption.legend.data[i] = data.name
      })
      chartOption.tooltip.valueFormatter = (value) => `${value}${this.$refs.chart.indexData[0].unit}`
      this.$refs.chart.chart.setOption(chartOption)
    }
  }
}
</script>

<style scoped></style>
