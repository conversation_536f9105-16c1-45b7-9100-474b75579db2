<template>
  <div
    class="title"
    :style="{
      paddingRight: showExpendView ? '120px' : '',
      fontSize: bolder ? '16px' : '12px',
      fontWeight: bolder ? 'bolder' : ''
    }"
  >
    {{ title }}<more v-show="showExpendView" :expend="expend" @click="handleToggle" />
    <span>{{ rightData }}</span>
  </div>
</template>
<script>
import more from '@/views/economy/custom/compents/more.vue'
export default {
  components: { more },
  props: {
    title: {
      type: String,
      default: null
    },
    rightData: {
      type: [String, Number],
      default: ''
    },
    showExpendView: {
      type: Boolean,
      default: false
    },
    bolder: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return { expend: false }
  },
  mounted() {},
  methods: {
    handleToggle() {
      this.expend = !this.expend
      this.$emit('expendClick', this.expend)
    }
  }
}
</script>
<style lang="scss" scoped>
.title {
  position: relative;
  height: 52px;
  margin: 10px;
  background: url(./img/hg-bg.png) no-repeat;
  background-size: 100% 100%;
  border-radius: 10px;
  font-size: 12px;
  font-family: PingFangSC, PingFang SC;
  display: flex;
  align-items: center;
  line-height: 16px; /* 将 line-height 设置为合理的行高 */
  padding-left: 50px; /* (52px - 26px) / 2 = 13px，保持视觉上的垂直居中 */
  span {
    flex: 1;
    text-align: right;
    font-weight: 400;
    font-size: 26px;
    color: #3b93fb;
    font-family: DIN-Regular;
    right: 10px;
    position: absolute;
  }
}
.title::before {
  content: '';
  position: absolute;
  left: 14px;
  top: 50%;
  transform: translateY(-50%);
  width: 28px;
  height: 28px;
  background: url(./img/hg-title.png) no-repeat center;
  background-size: contain;
}
</style>
