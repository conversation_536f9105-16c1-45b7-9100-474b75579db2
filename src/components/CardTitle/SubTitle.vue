<template>
  <div class="title">
    {{ title }}
  </div>
</template>
<script>
export default {
  props: {
    title: {
      type: String,
      default: null
    }
  }
}
</script>
<style lang="scss" scoped>
.title {
  position: relative;
  font-size: 32px;
  font-family: <PERSON><PERSON><PERSON>, serif;
  color: #1f1f21;
  padding-left: 12px;
  margin-left: 12px;
  margin-bottom: 12px;
  font-weight: 700;
}
.title::before {
  position: absolute;
  content: '';
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8px;
  height: 31px;
  background: #4176ca;
  border-radius: 3px 0 0 3px;
}
</style>
