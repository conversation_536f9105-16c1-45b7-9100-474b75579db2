<template>
  <div class="statistic-index2">
    <div
      v-for="(item, index) in list"
      :key="'key0' + index"
      :class="chartData.data.length % 2 != 0 && index == 0 ? 'statistic-index1__item' : 'statistic-index2__item'"
    >
      <div class="statistic-index2__title">
        <div>{{ item.title }}</div>
      </div>
      <div v-for="(item1, index1) in item.objs" :key="'key1' + index1" class="text-data__data-content">
        <div class="text-data__data-name">{{ item1.name }}</div>
        <div class="text-data__data-value">{{ item1.value }}</div>
        <div class="text-data__data-unit">{{ item1.unit }}</div>
      </div>
      <div
        v-show="chartData.data.length % 2 != 0 ? index % 2 == 1 : index % 2 == 0"
        class="statistic-index2-border"
        style="position: absolute"
        :style="{ height: 10 * item.objs.length + 70 + 'px' }"
      ></div>
    </div>
  </div>
</template>

<script>
import chart from './mixins/chart'
export default {
  name: 'MultipleTextStatistic',
  mixins: [chart],
  props: {
    chartData: {
      type: Object,
      default: () => {
        return {
          data: [],
          column: []
        }
      }
    }
  },
  data() {
    return {
      list: []
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      this.list = []
      if (this.chartData.data) {
        this.chartData.data.forEach((item) => {
          var title = item.title
          var names = []
          var values = []
          this.chartData.column.forEach((item) => {
            // if(item.indexOf('name') > -1) {
            //   names.push(item)
            // }
            if (item.indexOf('value') > -1) {
              names.push('name' + (values.length + 1))
              values.push(item)
            }
          })

          var objs = []
          values.forEach((item1, index1) => {
            objs.push({
              name: this.chartData.columnName[names[index1]],
              value: item[values[index1]],
              unit: this.chartData.unitList[values[index1]]
            })
          })

          this.list.push({
            title: title,
            objs: objs
          })
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.statistic-index2 {
  display: flex;
  flex-wrap: wrap;
}
.statistic-index1__item {
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-items: center;
  align-items: center;
  margin-bottom: 15px;
  .statistic-index2-border {
    border-right: none;
    margin-top: 7%;
    width: 40%;
  }
}
.statistic-index1__item {
  .text-data__data-name {
    width: 35% !important;
  }
  .text-data__data-value {
    width: 40% !important;
  }
  .text-data__data-unit {
    width: 25% !important;
  }
}
.statistic-index2__item,
.statistic-index1__item {
  // 指标名称
  .statistic-index2__title {
    font-size: 28px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #333333;
    // line-height: 100px;
    width: 90%;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    div {
      text-align: center;
    }
  }

  // 内容框
  .text-data__data-content {
    width: 90%;
    min-width: 80%;
    display: flex;
  }
  // 指标数值

  .text-data__data-name {
    width: 40%;
    font-size: 24px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #666666;
    line-height: 56px;
  }

  // 数值
  .text-data__data-value {
    width: 50%;
    text-align: center;
    font-size: 38px;
    font-family: PingFangSC-Semibold, PingFang SC;
    font-weight: 600;
    color: #3b93fb;
  }
  // 单位
  .text-data__data-unit {
    width: 10%;
    text-align: center;
    font-size: 24px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #666666;
    line-height: 56px;
  }
}
.statistic-index2__item {
  width: 50%;
  display: flex;
  flex-direction: column;
  justify-items: center;
  align-items: center;
  margin-bottom: 15px;

  .statistic-index2-border {
    border-right: 0.5px dashed rgba(153, 153, 153, 0.3);
    margin-top: 7%;
    width: 40%;
  }

  // 增长降低趋势
  .statistic-index2__ratio {
    // 上下左右居中
    display: flex;
    // flex-direction: column;
    justify-items: center;
    align-items: center;
    .t1 {
      line-height: 30px;
      font-size: 28px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 500;
      color: #3b93fb;
    }
  }
  // Divider 分割线
  .statistic-index2-divider {
    width: 100%;
    margin: 20px 0 2px 0;
  }
}
</style>
