export default {
  props: {
    chartData: {
      type: Object,
      default: () => {}
    },
    id: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      // random: Math.random()
    }
  },
  mounted() {
    // console.log('[ mounted ]', this.random)
    // console.log('[ mounted indicatorData ]', this.indicatorData)
    // this.init(this.indicatorData)
  },
  watch: {
    chartData: {
      handler(newVal) {
        // console.log('[ oldVal ] >', oldVal)
        // console.log('[ newVal ] >', this.random, newVal)
        this.$nextTick(() => {
          this.init(newVal)
        })
      },
      immediate: true,
      deep: true
    }
  }
}
