<template>
  <div ref="chartRef" class="echarts-container stacked-bar" />
</template>

<script setup>
import { ref, reactive, watchEffect } from 'vue'
import { useECharts } from '@/composables/useECharts'
import { isEmpty } from 'lodash'
import { colors } from '@/utils/constant'

const props = defineProps({
  chartData: {
    type: Object,
    default: () => ({})
  }
})

const chartRef = ref(null)
const { setOptions } = useECharts(chartRef)
const option = reactive({
  color: colors,
  grid: {
    top: '10%',
    bottom: '5%',
    left: '2.5%',
    right: '5%',
    containLabel: true
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    },
    // 将 tooltip 框限制在图表的区域内
    confine: 'true',
    extraCssText: 'z-index: 9;'
  },
  legend: {
    top: 'top',
    left: 'left'
  },
  // dataZoom: [
  //   {
  //     type: 'inside',
  //     start: 0,
  //     end: 11, // 数据窗口范围的结束百分比
  //     minValueSpan: 10, // 在类目轴上可以设置为 n 表示至少显示 n 个类目。
  //     zoomLock: true
  //   }
  // ],
  xAxis: {
    type: 'category',
    data: [],
    axisLabel: {
      color: 'hsla(0, 0%, 0%, 0.45)',
      rotate: 0,
      formatter(value) {
        let ret = ''
        let maxLength = 7
        for (let i = 0; i < value.length; i += maxLength) {
          ret += value.substring(i, i + maxLength) + '\n'
        }
        return ret
      }
    },
    axisTick: {
      show: false
    }
  },
  yAxis: {
    type: 'value',
    axisLabel: {
      color: '#666666'
    },
    nameRotate: '0.1'
  },
  series: []
})

watchEffect(() => {
  !isEmpty(props.chartData) && initCharts()
})

function initCharts() {
  let hasUnit = false
  let hasLegend = false
  // x 轴
  let xAxisData = props.chartData.data.map((item) => {
    return item.name
  })
  option.xAxis.data = xAxisData
  // unit
  let unit
  const unitList = props.chartData.unitList
  for (const key in unitList) {
    if (unitList[key]) {
      hasUnit = true
      unit = unitList[key]
      break
    }
  }
  // 设置 y 轴单位
  if (unit) {
    option.yAxis.name = `单位：${unit}`
    option.yAxis.nameTextStyle = {
      color: 'hsla(0, 0%, 40%, 1)',
      fontSize: 16
      // align: 'right'
    }
    option.tooltip.valueFormatter = (value) => `${value ? value : '-'}${unit}`
  }
  // series
  let seriesData = []
  const list = props.chartData.column.filter((item) => item !== 'name')
  // const valuesObj = omit(props.chartData.columnName, 'name')//顺序不可控，需要用column数组控制顺序
  const columnName = props.chartData.columnName

  for (const seriesKey of list) {
    const seriesName = columnName[seriesKey]
    //有两组的需求，那么用g2开头
    let obj = { name: seriesName, type: 'bar' }
    if (seriesKey.startsWith('g2')) {
      obj.stack = 'total2'
    } else {
      obj.stack = 'total'
    }
    obj.data = props.chartData.data.map((item) => {
      return item[seriesKey]
    })
    seriesData.push(obj)
  }
  option.series = seriesData

  // 设置单位与图例显示位置
  if (hasUnit && hasLegend) {
    option.yAxis.nameGap = 50
    option.grid.top = '22%' //28
    option.legend.top = '10%'
  } else if (!hasUnit && hasLegend) {
    option.grid.top = '12%' //15
    option.legend.top = '0'
  } else if (hasUnit && !hasLegend) {
    option.grid.top = '12%' //15
    option.yAxis.nameGap = 20
  }

  setOptions(option)
}
</script>
