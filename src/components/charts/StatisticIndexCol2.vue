<template>
  <div class="statistic-index2">
    <div v-for="(item, index) in chartData.data" :key="'col2' + index" class="statistic-index2__item">
      <div class="statistic-index2__cont">
        <div :style="{ minHeight: item.UP_CODE ? '46%' : '68%' }" class="statistic-index2__title">{{ item.name }}</div>
        <div class="statistic-index2__data">
          <div class="text-data__data-value">{{ item.value }}</div>
          <div class="text-data__data-unit">{{ item.unit }}</div>
        </div>
        <div v-if="item.UP_CODE" class="statistic-index2__ratio">
          <span :class="item.UP_CODE > 0 ? 't1' : 't2'">{{ item.UP_CODE }}%</span>
          <img v-if="item.UP_CODE > 0" src="../../assets/img/echartsImg/ratio-up-icon.png" alt="" />
          <img v-else src="../../assets/img/echartsImg/ratio-down-icon.png" alt="" />
        </div>
      </div>
      <!-- 增长/降低趋势（可选） -->
      <!-- 判断是否有增长/降低趋势值 更改右侧边框height -->
      <div class="statistic-index2-border"></div>
      <!-- <van-divider class="statistic-index2-divider" dashed></van-divider> -->
    </div>
  </div>
</template>

<script>
import chart from './mixins/chart'
export default {
  name: 'StatisticIndexCol2',
  mixins: [chart],
  props: {
    chartData: {
      type: Object,
      default: () => {}
    }
  },
  mounted() {},
  methods: {
    init(chartData) {
      this.itemList = chartData.dataList?.[0]?.itemList
    }
  }
}
</script>

<style lang="scss" scoped>
.statistic-index2 {
  display: flex;
  flex-wrap: wrap;
  // .statistic-index2__item:nth-child(even) .statistic-index2__cont {
  //   border-right: none;
  // }
}
.statistic-index2__item {
  position: relative;
  width: 50%;
  padding: 10px 0;
  // display: flex;
  // flex-direction: column;
  // justify-content: space-between;
  align-items: center;
  // 奇数item右侧有竖杠分隔
  // &:nth-child(odd) {
  //   .statistic-index2-border {
  //     border-right: 0.5px dashed rgba(153, 153, 153, 0.3);
  //     margin-top: 7%;
  //     width: 42%;
  //   }
  // }
  .statistic-index2__cont {
    width: 100%;
    height: 90%;
    display: flex;
    flex-direction: column;
    // justify-items: center;
    align-items: center;
    // justify-content: space-evenly;
    // border-right: 0.5px dashed rgba(153, 153, 153, 0.3);
  }

  // 指标名称
  .statistic-index2__title {
    font-size: 28px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #333333;
    // 考虑到存在标题过长的情况 居中 设置margin和行高
    width: 80%;
    text-align: center;
    min-height: 66%;
    // line-height: 38px;
    // margin: 25px 0;
    // height: 80%;
  }
  // 指标数值
  .statistic-index2__data {
    flex: 1;

    display: flex;
    // 数值
    .text-data__data-value {
      font-size: 48px;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
      color: #3b93fb;
      line-height: 48px;
    }
    // 单位
    .text-data__data-unit {
      font-size: 28px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #666666;
      line-height: 28px;
      margin-top: 12px;
    }
  }
  // 增长降低趋势
  .statistic-index2__ratio {
    flex: 1;
    // height: 54px;
    // 上下左右居中
    // display: flex;
    // flex-direction: column;
    // justify-items: center;
    // align-items: center;
    // height: 20%;
    // margin: 0 auto;
    span {
      margin-right: 10px;
      line-height: 54px;
    }
    img {
      width: 20px;
      height: 10px;
    }
    .t1 {
      font-size: 24px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #ff3f6a;
    }
    .t2 {
      font-size: 24px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #72ddb2;
    }
  }
  // Divider 分割线
  .statistic-index2-border {
    position: absolute;
    height: 80%;
    width: 100%;
    border-right: 0.5px dashed rgba(153, 153, 153, 0.3);
    right: 0;
    top: 50%;
    transform: translateY(-50%);
  }
}
.statistic-index2__item:nth-child(even) .statistic-index2-border {
  border-right: none;
}
</style>
