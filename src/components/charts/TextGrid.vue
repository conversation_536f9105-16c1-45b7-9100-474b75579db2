<template>
  <div class="text-grid">
    <div
      v-for="(k, index) of column"
      :key="index"
      class="text-grid-item"
      :style="{ overflow: column === 3 ? 'initial' : 'hidden' }"
    >
      <div v-if="value.length > 0" class="text-grid-item-title">
        <div>{{ value[index].name }}</div>
      </div>
      <!-- 数值和单位 -->
      <div v-if="value.length > 0" class="text-grid-item-content" :class="column === 3 ? 'item-content-3' : ''">
        <div class="item-content-value">{{ value[index].value }}</div>
        <div class="item-content-unit">{{ value[index].unit }}</div>
      </div>
      <!-- 增长率 % -->
      <div v-if="value.length > 0 && value[index].UP_CODE" class="item-content-up">
        <span :class="value[index].UP_CODE > 0 ? 't1' : 't2'">{{ value[index].UP_CODE }}%</span>
        <img
          v-if="value[index].UP_CODE > 0"
          src="@/assets/img/up-arrow.png"
          alt=""
          style="transform: scale(0.5) translateY(40%)"
        />
        <img v-else src="../../assets/img/echartsImg/ratio-down-icon.png" alt="" />
      </div>
      <!-- 自定义文本 字符串 -->
      <div v-if="value.length > 0 && value[index].RATIO_CODE" class="item-content-ratio">
        <span>{{ value[index].RATIO_CODE }}</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TextDate',
  props: {
    column: {
      type: Number,
      default: 2
    },
    value: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {}
  },
  computed: {}
}
</script>

<style lang="scss" scoped>
.text-grid {
  width: 100%;
  height: 100%;
  display: flex;
  .text-grid-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    border-right: 0.5px dashed rgba(153, 153, 153, 0.3);
    overflow: hidden;
    .text-grid-item-title {
      font-size: 28px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #333333;
      width: 80%;
      text-align: center;
      height: 80px;
      display: flex;
      justify-content: center;
      align-items: center;
      div {
        text-align: center;
      }
      // min-height: 66%;
    }
    .text-grid-item-content {
      display: flex;
      // 数值
      .item-content-value {
        font-size: 48px;
        font-family: PingFangSC-Semibold, PingFang SC;
        font-weight: 600;
        color: #3b93fb;
        line-height: 48px;
        margin-right: 6px;
      }
      // 单位
      .item-content-unit {
        font-size: 28px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #666666;
        line-height: 28px;
        margin-top: 16px;
      }
    }
    .item-content-3 {
      display: block !important;
      text-align: center !important;
      .item-content-value {
        margin-right: 0 !important;
      }
    }
    .item-content-up {
      margin-top: 10px;
      span {
        margin-right: 10px;
        line-height: 54px;
      }
      // img {
      //   width: 22px;
      //   height: 12px;
      // }
      .t1 {
        font-family: D-DIN, D-DIN;
        font-weight: bold;
        font-size: 48px;
        color: #ff0000;
      }
      .t2 {
        font-size: 48px;
        font-family: D-DIN, D-DIN;
        font-weight: bold;
        color: #72ddb2;
      }
    }
    .item-content-ratio {
      margin-top: 6px;
      span {
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 600;
        color: #3b93fb;
      }
    }
  }
  :last-child {
    border-right: none !important;
  }
  // :nth-child(even) {
  //   border-right: none;
  // }
}
.noBar {
  border-right: none !important;
}
</style>
