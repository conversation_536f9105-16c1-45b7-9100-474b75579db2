<template>
  <div>
    <div
      ref="chartRef"
      class="echarts-horizontal-bar-container"
      :style="{ height: height }"
      @click="handleClickBar"
    ></div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { omit } from 'lodash'
import { colors } from '@/utils/constant'

export default {
  name: 'HorizontalBarWithNewTextData',
  props: {
    height: {
      type: String,
      default: '340px'
    },
    showLabel: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      bottomData: { xLabel: [], title: [], data: [], unit: [], xUnit: [] },
      showDetail: true,
      chartInstance: null,
      chartData: null,
      hasLegend: false,
      textHeight: 0
    }
  },
  mounted() {
    // const dom = document.querySelector('.text-1')
    // this.textHeight = dom.clientHeight
  },
  methods: {
    loadChart(chartData) {
      this.chartData = chartData
      this.bottomData = { xLabel: [], title: [], data: [], unit: [], xUnit: [] }
      const titleArr = Object.values(chartData.columnName)
      const unitArr = Object.values(chartData.unitList)
      const dataArr = Object.values(chartData.data[0])
      this.bottomData.xLabel = dataArr[0]
      this.bottomData.xUnit = unitArr[0]
      for (let i = 1; i < dataArr.length; i++) {
        this.bottomData.title.push(titleArr[i])
        this.bottomData.data.push(dataArr[i])
        this.bottomData.unit.push(unitArr[i])
      }
      if (this.chartInstance === null) this.chartInstance = echarts.init(this.$refs.chartRef)
      const that = this
      let yAxis = []
      let seriesData = []
      chartData.data &&
        chartData.data.forEach((item) => {
          yAxis.push(item.name)
          seriesData.push(item.value1)
        })

      let legendData
      const valuesObj = omit(this.chartData.columnName, 'name')
      const entries = Object.entries(valuesObj)
      if (chartData.column.length > 2) {
        legendData = entries.map(([, value]) => value)
        this.hasLegend = true
      }
      const option = {
        color: [
          {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 1,
            y2: 0,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(171,206,253,0.61)' // 0% 处的颜色
              },
              {
                offset: 1,
                color: '#73A0FA' // 100% 处的颜色
              }
            ]
          }
        ],
        grid: {
          top: '5%',
          bottom: '5%',
          left: '2.5%',
          right: '17%',
          containLabel: true
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: function (params) {
            // 有value2则显示value1和value2，否则显示唯一的value
            const index = params[0].dataIndex
            return chartData.data[index].value2
              ? that.chartData.data[index].name +
                  `<br />` +
                  `${chartData.columnName.value1}：${chartData.data[index].value1}${chartData.unitList.value1}` +
                  `<br />` +
                  `${chartData.columnName.value2}：${chartData.data[index].value2}${chartData.unitList.value2}`
              : chartData.data[index].name +
                  `<br />` +
                  `${chartData.columnName.value}：${chartData.data[index].value}${chartData.unitList.value}`
          },
          // 将 tooltip 框限制在图表的区域内
          confine: 'true',
          extraCssText: 'z-index: 9;',
          backgroundColor: 'rgba(0,0,0,0.6)',
          textStyle: {
            color: 'white'
          }
        },
        legend: {
          show: that.hasLegend,
          type: 'scroll',
          data: legendData,
          top: '0',
          left: '0',
          icon: 'rect',
          itemGap: 14,
          itemWidth: 10,
          itemHeight: 10,
          textStyle: {
            fontSize: '14px',
            color: '#8C8C8C',
            padding: [0, 0, 0, 4]
          }
        },
        yAxis: {
          nameLocation: 'start',
          type: 'category',
          data: yAxis,
          axisLabel: {
            color: '#666666',
            // width: 50,
            overflow: 'truncate',
            fontSize: '13px',
            lineHeight: 14
          },
          inverse: true,
          splitLine: {
            show: false
          },
          axisLine: {
            show: true
          },
          axisTick: {
            show: false
          }
        },
        xAxis: {
          type: 'value',
          axisLabel: {
            show: false,
            fontSize: '10px',
            color: '#666666'
          },
          splitLine: {
            show: true
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          }
        },
        series: [
          {
            data: seriesData,
            type: 'bar',
            label: {
              show: true,
              position: 'top', // 顶部显示
              // formatter: function (params) {
              //   console.log(params, 666);
              // },
              textStyle: {
                //数值样式
                color: '#fff'
              }
            }
          }
        ]
      }
      for (let i = 0; i < entries.length; i++) {
        const [seriesKey, seriesName] = entries[i]
        let obj = { name: seriesName, type: 'bar' }
        obj.data = this.chartData.data.map((item) => {
          return item[seriesKey]
        })
        if (this.hasLegend) {
          obj.itemStyle = {
            color: colors[i]
          }
        } else {
          obj.barWidth = 10
        }
        obj.label = {
          show: true,
          position: 'right',
          formatter: function (params) {
            if (params.componentIndex === 0) {
              return params.value
            } else {
              return ''
            }
          }
        }
        seriesData.push(obj)
      }

      if (this.hasLegend) option.grid.top = '10%'
      option.series = seriesData
      this.chartInstance.setOption(option)
    },
    handleClickUnit() {
      this.showDetail = !this.showDetail
    },
    handleClickBar(params) {
      let pointInPixel = [params.offsetX, params.offsetY]
      let index // 索引
      if (this.chartInstance.containPixel('grid', pointInPixel)) {
        let pointInGrid = this.chartInstance.convertFromPixel(
          {
            seriesIndex: 0
          },
          pointInPixel
        )
        index = Number(pointInGrid[1]) // 索引
      } else {
        // containPixel()方法不存在，js原生写法会遇到这种问题
        const data = this.chartData.data
        const dom = document.querySelector('.echarts-horizontal-bar-container')
        const topOffset = this.hasLegend ? 0.1 * dom.clientHeight : 0.05 * dom.clientHeight
        const bottomOffset = 0.05 * dom.clientHeight
        const offsetY = params.offsetY - topOffset
        const perHeight = (dom.clientHeight - topOffset + bottomOffset) / data.length
        index = Math.floor(offsetY / perHeight)
      }
      if (this.chartData.data[index]) {
        const dataArr = Object.values(this.chartData.data[index])
        this.bottomData.xLabel = dataArr[0]
        for (let i = 1; i < dataArr.length; i++) {
          this.bottomData.data[i - 1] = dataArr[i]
        }
      }
    }
  }
}
</script>
<style scoped>
.text-container {
  transition: height 0.3s ease;
}

.text-1 {
  width: 100%;
  margin-top: 25px;
  z-index: 1;
  position: relative;
}
</style>
