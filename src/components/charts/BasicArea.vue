<template>
  <div ref="chartRef" class="echarts-container line" />
</template>

<script>
import { ref, reactive, watchEffect } from 'vue'
import { useECharts } from '@/composables/useECharts'
import { omit, isEmpty } from 'lodash'
import { colors, gradients } from '@/utils/constant'

export default {
  name: 'BaseLine',
  props: {
    chartData: {
      type: Object,
      default: () => ({})
    }
  },
  setup(props) {
    const chartRef = ref(null)
    const { setOptions } = useECharts(chartRef)

    const option = reactive({
      grid: {
        top: '10',
        bottom: '5',
        left: '5%',
        right: '5%',
        containLabel: true
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        },
        // 将 tooltip 框限制在图表的区域内
        confine: 'true',
        extraCssText: 'z-index: 9;'
      },
      legend: {
        type: 'scroll',
        data: [],
        top: '0',
        left: '0',
        icon: 'rect',
        itemGap: 10,
        itemWidth: 10,
        itemHeight: 4,
        textStyle: {
          fontSize: 12,
          color: '#8C8C8C'
        }
      },
      xAxis: {
        type: 'category',
        data: [],
        axisLabel: {
          color: '#666666'
        },
        splitLine: {
          show: false
        },
        axisLine: {
          show: true
        },
        axisTick: {
          show: false
        }
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          color: '#666666'
        },
        splitLine: {
          show: true
        },
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        nameRotate: '0.1'
      },
      series: []
    })

    watchEffect(() => {
      !isEmpty(props.chartData) && initCharts()
    })

    function initCharts() {
      let hasUnit = false
      let hasLegend = false
      // x 轴
      let xAxisData = props.chartData.data.map((item) => {
        return item.name
      })
      option.xAxis.data = xAxisData
      // unit
      let unit
      const unitList = props.chartData.unitList
      for (const key in unitList) {
        if (unitList[key]) {
          hasUnit = true
          unit = unitList[key]
          break
        }
      }
      // 设置 y 轴单位
      if (unit) {
        option.yAxis.name = `单位：${unit}`
        option.yAxis.nameTextStyle = {
          color: 'hsla(0, 0%, 40%, 1)',
          fontSize: 16
          // align: 'right'
        }
        option.tooltip.valueFormatter = (value) => `${value ? value : '-'}${unit}`
      }
      // series
      let seriesData = []
      // 获取排除 name 后的对象
      const valuesObj = omit(props.chartData.columnName, 'name')
      const entries = Object.entries(valuesObj)
      // series 不小于两个 显示图例
      if (entries.length > 1) {
        option.legend.data = entries.map(([, value]) => value)
        hasLegend = true
      }
      for (let i = 0; i < entries.length; i++) {
        const [seriesKey, seriesName] = entries[i]
        let obj = {
          name: seriesName,
          type: 'line',
          symbol: 'circle',
          symbolSize: 5
        }
        obj.lineStyle = {
          color: colors[i]
        }
        obj.itemStyle = {
          color: colors[i],
          borderColor: '#fff',
          borderWidth: 0.6
        }
        obj.areaStyle = {
          color: gradients[i]
        }
        obj.data = props.chartData.data.map((item) => {
          return item[seriesKey]
        })
        seriesData.push(obj)
      }
      option.series = seriesData

      // 设置单位与图例显示位置
      if (hasUnit && hasLegend) {
        option.yAxis.nameGap = 50
        option.grid.top = '22%'
        option.legend.top = '10%'
      } else if (!hasUnit && hasLegend) {
        option.grid.top = '12%'
        option.legend.top = '0'
      } else if (hasUnit && !hasLegend) {
        option.grid.top = '12%'
        option.yAxis.nameGap = 20
      }

      setOptions(option)
    }

    return { chartRef }
  }
}
</script>
