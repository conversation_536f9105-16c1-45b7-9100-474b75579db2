<template>
  <div class="order-list">
    <van-row class="order-list__head">
      <van-col span="4">排行</van-col>
      <van-col span="20">名称</van-col>
    </van-row>
    <van-row v-for="(item, index) in itemList" :key="index" type="flex" align="center">
      <van-col span="4" class="order-list__item-number">
        <div v-if="index === 0">
          <img class="order-list__number-img" src="@/assets/img/topic/one.png" />
        </div>
        <div v-else-if="index === 1">
          <img class="order-list__number-img" src="@/assets/img/topic/two.png" />
        </div>
        <div v-else-if="index === 2">
          <img class="order-list__number-img" src="@/assets/img/topic/three.png" />
        </div>
        <div v-else>{{ index + 1 }}</div>
      </van-col>
      <van-col span="20" class="order-list__item-text">{{ item.name }}</van-col>
    </van-row>
  </div>
</template>

<script>
import chart from './mixins/chart'

export default {
  name: 'RankList',
  mixins: [chart],
  data() {
    return {
      itemList: []
    }
  },
  methods: {
    init(chartData) {
      this.itemList = chartData.dataList?.[0]?.itemList
    }
  }
}
</script>
