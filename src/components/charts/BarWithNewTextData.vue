<template>
  <div
    ref="chartRef"
    class="echarts-horizontal-bar-container"
    :style="{ height: height }"
    @click="handleClickBar"
  ></div>
</template>

<script>
import * as echarts from 'echarts'
import { omit } from 'lodash'
import { colors } from '@/utils/constant'

export default {
  name: 'BarWithNewTextData',
  props: {
    tooltipUnit:{
      type:String,
      defalt:'',
    },
    height: {
      type: String,
      default: '250px'
    },
    type: {
      type: Number,
      default: 0
    },
   
  },
  data() {
    return {
      bottomData: { xLabel: [], title: [], data: [], unit: [], xUnit: [] },
      showDetail: true,
      chartInstance: null,
      chartData: null,
      screenWidth: null,
      textHeight: 0
    }
  },
  mounted() {
    // const dom = document.querySelector('.text-1')
    // this.textHeight = dom.clientHeight
  },
  methods: {
    loadChart(chartData) {
      window.addEventListener('resize', this.updateScreenWidth()) // 监听窗口大小变化
      this.chartData = chartData
      this.bottomData = { xLabel: [], title: [], data: [], unit: [], xUnit: [] }
      const titleArr = Object.values(chartData.columnName)
      const unitArr = Object.values(chartData.unitList)
      const dataArr = Object.values(chartData.data[0])
      this.bottomData.xLabel = dataArr[0]
      this.bottomData.xUnit = unitArr[0]
      for (let i = 1; i < dataArr.length; i++) {
        this.bottomData.title.push(titleArr[i])
        this.bottomData.data.push(dataArr[i])
        this.bottomData.unit.push(unitArr[i])
      }
      if (this.chartInstance === null) {
        this.chartInstance = echarts.init(this.$refs.chartRef)
      }
      let that = this
      const option = {
        grid: {
          top: '26%',
          bottom: '0%',
          left: '0%',
          right: '0%',
          containLabel: true
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          // 将 tooltip 框限制在图表的区域内
          confine: 'true',
          extraCssText: 'z-index: 9;',
          backgroundColor: 'rgba(0,0,0,0.6)',
          textStyle: {
            color: 'white'
          },
          valueFormatter:item=>{
            return item + that.tooltipUnit
          }
        },
        legend: {
          type: 'scroll',
          data: [],
          icon: 'rect',
          itemGap: 14,
          itemWidth: 10,
          itemHeight: 10,
          textStyle: {
            fontSize: '11px',
            color: '#73767f'
          }
        },
        xAxis: {
          type: 'category',
          data: [],
          axisLabel: {
            fontSize: '11px',
            color: '#73767f',
            rotate: 0,
            interval: 0,
            formatter(value) {
              let ret = ''
              let maxLength = 4
              for (let i = 0; i < value.length; i += maxLength) {
                ret += value.substring(i, i + maxLength) + '\n'
              }
              return ret
            }
          },
          show: true,
          axisTick: { alignWithLabel: true }
        },
        yAxis: [
          {
            type: 'value',
            axisLabel: {
              fontSize: '11px',
              color: '#73767f'
            }
          },
          {
            name: that.type == 1 ? '单位:万吨' : '',
            nameTextStyle: {
              align: 'right',
              fontSize: '11px',
              color: '#73767f',
              padding: [0, 0, 10, 0]
            }
          }
        ],
        series: []
      }
      if (this.chartData) this.initCharts(option)
    },
    initCharts(option) {
      let hasUnit = false
      let hasLegend = false
      // x 轴
      let xAxisData = this.chartData.data.map((item) => {
        return item.name
      })
      option.xAxis.data = xAxisData
      // 柱状图只有一个单位
      let unit
      const unitList = this.chartData.unitList
      for (const key in unitList) {
        if (unitList[key]) {
          hasUnit = true
          unit = unitList[key]
          break
        }
      }
      // 设置 y 轴单位
      if (unit) {
        // option.yAxis.name = `单位：${unit}`
        // option.yAxis.nameTextStyle = {
        //   color: 'hsla(0, 0%, 40%, 1)',
        //   fontSize: 12,
        //   padding: [0, 0, 10, 10],
        //   align: 'right'
        // }
        // option.tooltip.valueFormatter = (value) => `${value ? value : '-'}${unit}`
      }
      // series
      let seriesData = []
      // 获取排除 name 后的对象
      const valuesObj = omit(this.chartData.columnName, 'name')
      const entries = Object.entries(valuesObj)
      // series 不小于两个 显示图例
      if (entries.length > 1) {
        option.legend.data = entries.map(([, value]) => value)
        hasLegend = true
      }
      const colors2 = [
        new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: 'rgba(83, 168, 242,1)' }, // 0% 处的颜色
          // { offset: 0.5, color: '#2f4554' }, // 50% 处的颜色
          { offset: 1, color: 'rgba(213, 234, 252, 0.30)' } // 100% 处的颜色
        ]),
        new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: 'rgba(113, 213, 150,1)' }, // 0% 处的颜色
          // { offset: 0.5, color: '#2f4554' }, // 50% 处的颜色
          { offset: 1, color: 'rgba(220, 245, 229, 0.30)' } // 100% 处的颜色
        ])
      ]
      for (let i = 0; i < entries.length; i++) {
        const [seriesKey, seriesName] = entries[i]
        let obj = { name: seriesName, type: 'bar' }
        obj.itemStyle = {
          color: colors2[i]
        }
        obj.data = this.chartData.data.map((item) => {
          return item[seriesKey]
        })
        obj.label = {
          show: true,
          position: 'top',
          fontSize: '12px',
          color: 'black'
        }
        obj.barWidth = 20
        seriesData.push(obj)
      }
      option.series = seriesData

      this.chartInstance.setOption(option)
    },
    handleClickUnit() {
      this.showDetail = !this.showDetail
    },
    handleClickBar(params) {
      let pointInPixel = [params.offsetX, params.offsetY]
      let index // 索引
      if (this.chartInstance.containPixel('grid', pointInPixel)) {
        let pointInGrid = this.chartInstance.convertFromPixel(
          {
            seriesIndex: 0
          },
          pointInPixel
        )
        index = Number(pointInGrid[0]) // 索引
      } else {
        // containPixel()方法不存在，js原生写法会遇到这种问题
        const data = this.chartData.data
        const dom = document.querySelector('.echarts-horizontal-bar-container')
        const topOffset = this.hasLegend ? 0.1 * dom.clientHeight : 0.05 * dom.clientHeight
        const bottomOffset = 0.05 * dom.clientHeight
        const offsetY = params.offsetY - topOffset
        const perHeight = (dom.clientHeight - topOffset + bottomOffset) / data.length
        index = Math.floor(offsetY / perHeight)
      }
      if (this.chartData.data[index]) {
        const dataArr = Object.values(this.chartData.data[index])
        this.bottomData.xLabel = dataArr[0]
        for (let i = 1; i < dataArr.length; i++) {
          this.bottomData.data[i - 1] = dataArr[i]
        }
      }
    },
    updateScreenWidth() {
      this.screenWidth = window.innerWidth
    }
  }
}
</script>
<style scoped lang="scss">
.text-container {
  transition: height 0.3s ease;
}
unit .text-1 {
  width: 100%;
  margin-top: 25px;
  z-index: 1;
  position: relative;
}
.chartbox {
  position: relative;
  .unit {
    position: absolute;
    right: 10px;
    top: 45px;
    color: rgb(144, 144, 144);
    font-size: 22px;
  }
}
</style>
