<template>
  <div class="czcustom-table-wrapper">
    <a-table
      :columns="columns"
      :scroll="{ x: true }"
      :data-source="list"
      :pagination="false"
      :bordered="true"
      class="custom-table"
      :rowKey="
        (record, index) => {
          return index
        }
      "
    />
    <div
      @click="moreClick"
      v-if="showMore"
      style="
        font-size: 12px;
        font-family: PingFang, serif;
        color: rgb(58, 117, 197);
        width: 100%;
        text-align: center;
        margin: 15px 0px 0px;
      "
    >
      <div style="display: flex; align-items: center; justify-content: center; gap: 4px; font-size: 14px">
        {{ more ? '收起' : '查看更多'
        }}<svg
          :style="{ transform: more ? 'rotate(180deg)' : 'rotate(0deg)' }"
          width="16px"
          height="12px"
          viewBox="0 0 16 16"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path d="M2 5 L8 11 L14 5" stroke="#0290F9" stroke-width="2" fill="none"></path>
        </svg>
      </div>
    </div>
  </div>
</template>

<script>
import { Table } from 'ant-design-vue'

export default {
  components: {
    Table
  },
  props: {
    columns: {
      type: Array,
      default: () => []
    },
    dataSource: {
      type: Array,
      default: () => []
    },
    showMore: {
      type: Boolean,
      default: false
    },
    size: {
      //如果是折叠的，折叠的数量
      type: Number,
      default: 4
    }
  },
  watch: {
    dataSource: {
      //拿到数据判断是不是折叠，是显示部分数据，否则显示所有
      handler(val) {
        if (this.showMore && !this.more) {
          this.list = this.dataSource.slice(0, this.size)
        } else {
          this.list = [...this.dataSource]
        }
      },
      immediate: true
    }
  },
  data() {
    return {
      more: false,
      list: [] //这个是用来显示的数据
    }
  },
  methods: {
    moreClick() {
      this.more = !this.more
      this.$emit('moreClick', this.more)
      this.dealData()
    },
    dealData() {
      if (this.more) {
        this.list = [...this.dataSource]
      } else {
        this.list = this.dataSource.slice(0, this.size)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.czcustom-table-wrapper {
  margin: 0px 14px;
  :deep(.ant-table) {
    // 表头样式
    .ant-table-thead > tr > th {
      background: #3876c5 !important;
      color: white;
      font-weight: normal;
      padding: 8px 16px;
      font-size: 14px;
    }

    // 表格内容样式
    .ant-table-tbody > tr > td {
      padding: 8px 16px;
      border: 1px solid #e8e8e8;
    }

    // 隔行变色
    .ant-table-tbody > tr:nth-child(even) {
      background-color: #f7f7f7;
    }
    .ant-table-tbody > tr:nth-child(odd) {
      background-color: #ffffff;
    }

    // 鼠标悬浮效果
    .ant-table-tbody > tr:hover > td {
      background-color: #e6f7ff;
    }

    // 调整第二行表头的上边框
    .ant-table-thead > tr:not(:first-child) > th {
      border-top: none;
    }
  }
}
</style>
