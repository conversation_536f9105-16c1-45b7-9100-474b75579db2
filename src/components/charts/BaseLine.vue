<template>
  <div>
    <div ref="chartRef" class="echarts-container line" @click="handleClick" />
    <div class="text-container" :style="{ height: !showDetail ? '60px' : chartData.column.length * 60 + 'px' }">
      <NewTextData
        class="text-1"
        single-line
        unit-color="#4188ff"
        :title="bottomData.name + chartData.unitList.name"
        :unit="!showDetail ? '展开' : '收起'"
        @clickUnit="handleClickUnit"
      ></NewTextData>
      <div v-if="showDetail">
        <NewTextData
          single-line
          class="text-2"
          :class="{ 'text-2-show': showDetail }"
          :title="chartData.columnName.value"
          :value="bottomData.value"
          :unit="chartData.unitList.value"
        ></NewTextData>
        <NewTextData
          v-if="chartData.column.length > 2"
          single-line
          class="text-3"
          :class="{ 'text-3-show': showDetail }"
          :title="chartData.columnName.value1"
          :value="bottomData.value1"
          :unit="chartData.unitList.value1"
        ></NewTextData>
        <NewTextData
          v-if="chartData.column.length > 3"
          single-line
          class="text-4"
          :class="{ 'text-4-show': showDetail }"
          :title="chartData.columnName.value2"
          :value="bottomData.value2"
          :unit="chartData.unitList.value2"
        ></NewTextData>
        <NewTextData
          v-if="chartData.column.length > 4"
          single-line
          class="text-5"
          :class="{ 'text-5-show': showDetail }"
          :title="chartData.columnName.value3"
          :value="bottomData.value3"
          :unit="chartData.unitList.value3"
        ></NewTextData>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, watchEffect } from 'vue'
import { useECharts } from '@/composables/useECharts'
import { omit, isEmpty } from 'lodash'
import { colors } from '@/utils/constant'
import NewTextData from '@/components/charts/NewTextData.vue'
export default {
  name: 'BaseLine',
  components: {
    NewTextData
  },
  props: {
    chartData: {
      type: Object,
      default: () => ({})
    }
  },
  setup(props) {
    var screenWidth = 0
    window.addEventListener('resize', updateScreenWidth()) // 监听窗口大小变化
    function updateScreenWidth() {
      screenWidth = window.innerWidth
    }
    const chartRef = ref(null)
    const { setOptions } = useECharts(chartRef)
    const option = reactive({
      grid: {
        top: '10',
        bottom: '5',
        left: '2.5%',
        right: '5%',
        containLabel: true
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        },
        // 将 tooltip 框限制在图表的区域内
        confine: 'true',
        extraCssText: 'z-index: 9;'
      },
      legend: {
        type: 'scroll',
        data: [],
        top: '0',
        left: '0',
        icon: 'rect',
        itemGap: 10,
        itemWidth: 14,
        itemHeight: 4,
        textStyle: {
          fontSize: '14px',
          color: '#8C8C8C',
          padding: [0, 0, 0, 4]
        }
      },
      xAxis: {
        type: 'category',
        data: [],
        axisLabel: {
          fontSize: '12px',
          color: '#999999'
        },
        splitLine: {
          show: false
        },
        axisLine: {
          show: true
        },
        axisTick: {
          show: false
        }
      },
      yAxis: {
        type: 'value',
        nameTextStyle: {
          fontSize: '12px'
        },
        axisLabel: {
          fontSize: '12px',
          color: '#999999'
        },
        splitLine: {
          show: true
        },
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        }
      },
      series: []
    })
    watchEffect(() => {
      !isEmpty(props.chartData) && initCharts()
      // console.log(this.instance)
    })

    function initCharts() {
      let hasUnit = false
      let hasLegend = false
      // x 轴
      let xAxisData = props.chartData.data.map((item) => {
        return item.name
      })
      option.xAxis.data = xAxisData
      // unit
      let unit
      const unitList = props.chartData.unitList
      for (const key in unitList) {
        if (unitList[key]) {
          hasUnit = true
          unit = unitList[key]
          break
        }
      }
      // 设置 y 轴单位
      if (unit) {
        option.yAxis.name = `单位：${unit}`
        option.yAxis.nameTextStyle = {
          color: 'hsla(0, 0%, 40%, 1)',
          fontSize: 12,
          padding: [0, 0, 0, screenWidth + 50]
          // align: 'right'
        }
        option.tooltip.valueFormatter = (value) => `${value ? value : '-'}${unit}`
      }
      // series
      let seriesData = []
      // 获取排除 name 后的对象
      const valuesObj = omit(props.chartData.columnName, 'name')
      const entries = Object.entries(valuesObj)
      // series 不小于两个 显示图例
      if (entries.length > 1) {
        option.legend.data = entries.map(([, value]) => value)
        hasLegend = true
      }
      for (let i = 0; i < entries.length; i++) {
        const [seriesKey, seriesName] = entries[i]
        let obj = {
          name: seriesName,
          type: 'line',
          symbol: 'circle',
          symbolSize: 4
        }
        obj.lineStyle = {
          color: colors[i]
        }
        obj.itemStyle = {
          color: colors[i],
          borderColor: '#fff',
          borderWidth: 0.5
        }
        obj.data = props.chartData.data.map((item) => {
          return item[seriesKey]
        })
        seriesData.push(obj)
      }
      option.series = seriesData

      // 设置单位与图例显示位置
      if (hasUnit && hasLegend) {
        option.grid.top = '25%'
        option.legend.top = '0'
      } else if (!hasUnit && hasLegend) {
        option.grid.top = '12%'
        option.legend.top = '0'
      } else if (hasUnit && !hasLegend) {
        option.grid.top = '12%'
      }

      setOptions(option)
    }
    return { chartRef }
  },
  data() {
    return {
      instance: null,
      showDetail: false,
      bottomData: {}
    }
  },
  watch: {
    chartData: {
      handler(newVal) {
        this.bottomData = newVal.data[0]
      },
      deep: true // 开启深度监听
    }
  },
  methods: {
    handleClick(params) {
      this.$emit('clickLine', params)
      this.handleClickLine(params)
    },
    handleClickUnit() {
      this.showDetail = !this.showDetail
    },
    handleClickLine(params) {
      // console.log(params)
      // console.log(this.chartData)
      const data = this.chartData.data
      const offsetX = params.offsetX - 30
      const dom = document.querySelector('.base-line')
      const perWidth = (0.95 * dom.clientWidth - 30) / data.length
      const index = Math.floor(offsetX / perWidth)
      this.bottomData = data[index]
    }
  }
  // beforeDestroy() {
  //   window.removeEventListener('resize', updateScreenWidth()) // 移除监听器
  // }
}
</script>
<style lang="scss" scoped>
.text-container {
  transition: height 0.3s ease;
}
.text-1 {
  width: 100%;
  margin-top: 25px;
  z-index: 2;
  position: relative;
}
.text-2 {
  width: 100%;
  transition: transform 0.3s ease;
  transform: translateY(-100%);
  z-index: 1;
}
.text-2-show {
  width: 100%;
  transform: translateY(-10%);
}
.text-3 {
  width: 100%;
  transition: transform 0.5s ease;
  transform: translateY(-100%);
  z-index: 2;
}
.text-3-show {
  width: 100%;
  transform: translateY(-20%);
}
.new-text .text-4 {
  width: 100%;
  transition: transform 0.7s ease;
  transform: translateY(-100%);
  z-index: 3;
}
.text-4-show {
  width: 100%;
  transform: translateY(-30%);
}
</style>
