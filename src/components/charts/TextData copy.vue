<template>
  <div class="text-data">
    <!-- <div v-for="item in indicatorData.data" :key="item.name" class="text-data__block">
      <div class="text-data__title">{{ item.name }}</div>
      <div class="text-data__data">
        <span class="text-data__data-value">{{ item.value }}</span>
        <span class="text-data__data-unit">{{ item.dw }}</span>
      </div>
    </div> -->
  </div>
</template>

<script>
export default {
  name: 'TextDataBak',
  props: {
    indicatorData: {
      type: Object,
      default: () => ({})
    }
  }
}
</script>
