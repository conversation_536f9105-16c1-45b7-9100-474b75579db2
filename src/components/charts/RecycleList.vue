<template>
  <div class="main">
    <RecycleScroller v-slot="{ item }" class="virtual-list" :item-height="100" key-field="id" :items="tableData">
      <div :key="item.id" class="list-item">
        <p class="text">{{ item.id }}</p>
        <p>{{ item.name }}</p>
      </div>
    </RecycleScroller>
  </div>
</template>

<script>
export default {
  data() {
    return {
      tableData: []
    }
  },
  created() {
    for (let i = 0; i < 100000; i++) {
      this.tableData.push({ id: i, name: Math.random() })
    }
  }
}
</script>
<style scoped>
.main {
  height: 60vh;
  width: 100%;
  overflow: auto;
  background-color: aqua;
}
.virtual-list {
  height: 100%;
}

.list-item {
  height: 30%;
  padding: 0 12px;
  display: flex;
  align-items: center;
}
.text {
  color: #000;
  font-size: 20;
}
</style>
