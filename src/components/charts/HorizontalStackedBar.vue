<template>
  <div :ref="id" class="echarts-horizontal-bar-container"></div>
</template>

<script>
import chart from './mixins/chart'

export default {
  name: 'HorizontalStackedBar',
  mixins: [chart],
  methods: {
    init(chartData) {
      const legends = chartData.xaxis
      let yData = null
      let seriesData = []
      let unit
      legends.forEach((legend) => {
        const dataArr = chartData.dataItemList[legend]
        const values = []
        const xAxisNames = []
        for (let point = 0; point < dataArr.length; point++) {
          if (!unit) {
            unit = dataArr[point].dw || ''
          }
          values.push(dataArr[point].value)
          xAxisNames.push(dataArr[point].name)
        }
        if (!yData) {
          yData = xAxisNames
        }
        const series = {
          name: legend,
          type: 'bar',
          stack: 'total',
          barMaxWidth: 20
        }
        series.data = values

        seriesData.push(series)
      })
      const option = {
        color: [
          '#5B8FF9',
          '#5AD8A6',
          '#FFB67A',
          '#FC8566',
          '#84D6F8',
          '#C39DFF',
          '#726930',
          '#fcf16e',
          '#f391a9',
          '#d1c7b7'
        ],
        grid: {
          top: '40',
          bottom: '10',
          left: '5%',
          right: '5%',
          containLabel: true
        },
        legend: {
          data: legends,
          left: 'left',
          padding: [0, 10],
          icon: 'rect',
          itemWidth: 12,
          itemHeight: 12,
          textStyle: {
            color: '#666666',
            lineHeight: 24
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          // 将 tooltip 框限制在图表的区域内
          confine: 'true',
          extraCssText: 'z-index: 9;'
        },
        xAxis: {
          type: 'value',
          axisLabel: {
            color: '#666666'
          },
          splitLine: {
            show: true
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          }
        },
        yAxis: {
          type: 'category',
          data: yData,
          axisLabel: {
            color: '#666666'
          },
          splitLine: {
            show: false
          },
          axisLine: {
            show: true
          },
          axisTick: {
            show: false
          }
        },
        series: seriesData
      }
      if (unit) {
        option.grid.top = '25%'
        option.yAxis.name = `单位：${unit}`
      }
      // 有的话就获取已有echarts实例的DOM节点。
      let myChart = this.$echarts.getInstanceByDom(this.$refs[this.id])
      if (myChart == null) {
        // 如果不存在，就进行初始化
        myChart = this.$echarts.init(this.$refs[this.id], null, {
          height: yData.length * 30 + 100
        })
      }
      myChart.setOption(option, true)
    }
  }
}
</script>
