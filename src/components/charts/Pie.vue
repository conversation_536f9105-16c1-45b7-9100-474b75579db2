<template>
  <div ref="chartRef" class="echarts-container pie"></div>
</template>
<script>
import { ref, reactive, watchEffect } from 'vue'
import { useECharts } from '@/composables/useECharts'
import { isEmpty } from 'lodash'
import { colors2 } from '@/utils/constant'

export default {
  name: 'Pie',
  props: {
    chartData: {
      type: Object,
      default: () => ({})
    }
  },
  setup(props) {
    const chartRef = ref(null)
    const { setOptions } = useECharts(chartRef)
    const option = reactive({
      color: colors2,
      grid: {
        left: '5%',
        bottom: '0',
        right: '5%',
        containLabel: true
      },
      tooltip: {
        trigger: 'item',
        valueFormatter: (value) => value + props.chartData.data[0].unit,
        backgroundColor: 'rgba(0,0,0,0.6)',
        textStyle: {
          color: 'white'
        }
      },
      legend: {
        show: true,
        type: 'scroll',
        top: '5%',
        left: '0%',
        data: [],
        icon: 'circle',
        itemWidth: 12,
        itemHeight: 12,
        textStyle: {
          color: '#8C8C8C'
        }
      },
      series: [
        {
          type: 'pie',
          top: '-10%',
          radius: ['0%', '45%'],
          center: ['50%', '60%'],
          label: {
            alignTo: 'edge',
            formatter: `{name|{b}}\n{percentage|{c}}{unit|${props.chartData.data[0].unit}}{percentage|{d}}{unit|%}`,
            minMargin: 5,
            edgeDistance: 10,
            lineHeight: 15,
            rich: {
              name: {
                fontFamily: 'PingFangSC, PingFang SC',
                fontWeight: '400',
                fontSize: '14px',
                color: '#666666',
                textAlign: 'left',
                fontStyle: 'normal'
              },
              percentage: {
                fontFamily: 'PingFangSC, PingFang SC',
                fontWeight: '400',
                fontSize: '14px',
                color: '#666666',
                textAlign: 'center',
                fontStyle: 'normal',
                textTransform: 'none'
              },
              unit: {
                fontFamily: 'PingFangSC, PingFang SC',
                fontWeight: '400',
                fontSize: '14px',
                color: '#666666',
                textAlign: 'center',
                fontStyle: 'normal',
                textTransform: 'none'
              }
            }
          },
          labelLine: {
            length: 15,
            length2: 10,
            maxSurfaceAngle: 80
          },
          itemStyle: {
            borderWidth: 2,
            borderColor: '#ffffff'
          },
          data: []
        }
      ]
    })

    watchEffect(() => {
      !isEmpty(props.chartData) && initCharts()
    })

    function initCharts() {
      let seriesData = props.chartData.data
      let legends = props.chartData.data.map((item) => {
        return item.name
      })
      option.legend.data = legends
      option.series[0].data = seriesData

      setOptions(option)
    }

    return { chartRef }
  }
}
</script>
