<template>
  <div class="order-list">
    <van-row class="order-list__head">
      <van-col span="4">排行</van-col>
      <van-col span="20">名称</van-col>
    </van-row>
    <van-row v-for="item in indicatorData.dataItem" :key="item.name" type="flex" align="center">
      <van-col span="4" class="order-list__item-number">
        <div v-if="item.name === '1'">
          <img class="order-list__number-img" src="@/assets/img/topic/one.png" />
        </div>
        <div v-else-if="item.name === '2'">
          <img class="order-list__number-img" src="@/assets/img/topic/two.png" />
        </div>
        <div v-else-if="item.name === '3'">
          <img class="order-list__number-img" src="@/assets/img/topic/three.png" />
        </div>
        <div v-else>{{ item.name }}</div>
      </van-col>
      <van-col span="20" class="order-list__item-text">{{ item.value }}</van-col>
    </van-row>
  </div>
</template>

<script>
export default {
  name: 'OrderList',
  props: {
    indicatorData: {
      type: Object,
      default: () => {}
    }
  }
}
</script>
