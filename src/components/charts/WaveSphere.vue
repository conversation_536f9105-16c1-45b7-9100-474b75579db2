<template>
  <div ref="chartRef" class="echarts-container"></div>
</template>

<script>
import { ref, reactive, watchEffect } from 'vue'
import 'echarts-liquidfill'
import { useECharts } from '@/composables/useECharts'
import { isEmpty } from 'lodash'
// import { colors } from '@/utils/constant'

export default {
  name: 'WaveSphere',
  props: {
    chartData: {
      type: Object,
      default: () => ({})
    }
  },
  setup(props) {
    const chartRef = ref(null)
    const { setOptions } = useECharts(chartRef)
    const option = reactive({
      title: {
        text: '',
        left: 'center',
        top: '25%',
        textStyle: {
          color: 'rgba(102, 102, 102, 1)',
          fontWeight: 'normal',
          fontSize: 30
        }
      },
      series: [
        {
          type: 'liquidFill',
          shape: 'circle',
          radius: '80%',
          center: ['50%', '50%'],
          data: [0.6, 0.6],

          // 球体配置
          outline: {
            borderDistance: 0,
            itemStyle: {
              borderWidth: 16,
              borderColor: 'rgba(115, 160, 250, 1)'
            }
          },
          color: ['rgba(50, 113, 240, 0.93)', 'rgba(75, 203, 255, 1)'],
          label: {
            formatter: function (param) {
              return (param.value * 100).toFixed(2) + '%'
            },
            show: true,
            textStyle: {
              color: 'rgba(0, 137, 255, 1)',
              fontSize: 40
            }
          },
          backgroundStyle: {
            color: 'transparent'
          }
        }
      ],
      backgroundColor: '#FFFFFF'
    })

    watchEffect(() => {
      !isEmpty(props.chartData) && initCharts()
    })

    function initCharts() {
      option.title.text = props.chartData.data[0].name
      // option.series[0].data = [props.chartData.data[0].value, props.chartData.data[0].value]
      option.series[0].data = [(props.chartData.data[0].value / 100).toFixed(4)]
      // // x 轴
      // let xAxisData = props.chartData.data.map((item) => {
      //   return item.name
      // })
      // option.yAxis[0].data = xAxisData
      // option.yAxis[1].data = xAxisData
      // // series
      // let seriesData = []
      // const valuesObj = omit(props.chartData.columnName, 'name')

      // for (var i in Object.entries(valuesObj)) {
      //   const [seriesKey, seriesName] = Object.entries(valuesObj)[i]
      //   let obj = { name: seriesName, type: 'bar' }
      //   obj.data = props.chartData.data.map((item) => {
      //     return item[seriesKey]
      //   })
      //   obj.itemStyle = {
      //     color: colorList[i]
      //   }
      //   obj.yAxisIndex = i
      //   obj.xAxisIndex = i
      //   seriesData.push(obj)

      // }
      // option.series = seriesData
      setOptions(option)
    }

    return { chartRef }
  }
}
</script>
