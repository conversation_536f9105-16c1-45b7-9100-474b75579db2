<template>
  <div
    class="new-text"
    :class="{ 'title-center-style': isText }"
    :style="{ 'background-color': backgroundColor, 'grid-column': singleLine ? '1 / -1' : '' }"
  >
    <div class="title">
      <img v-if="showIcon" src="@/assets/img/television.png" alt="" style="height: 15px; width: 15px" />
      <div :style="{ marginLeft: showIcon ? '6px' : '0' }">{{ title }}</div>
    </div>
    <div v-if="!isText" :style="{ 'margin-left': singleLine ? 'auto' : '0' }">
      <slot></slot>
      <span class="value" :style="{ color: valueColor }">{{ value }}</span>
      <span class="unit" :style="{ color: unitColor }" @click="$emit('clickUnit')">{{ unit }}</span>
    </div>
  </div>
</template>

<script>
export default {
  name: 'NewTextData'
}
</script>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  title: {
    type: String,
    default: ''
  },
  value: {
    type: [String, Number],
    default: ''
  },
  unit: {
    type: String,
    default: ''
  },
  singleLine: {
    type: Boolean,
    default: false
  },
  backgroundColor: {
    type: String,
    default: '#ecf3fd'
  },
  valueColor: {
    type: String,
    default: '#3b93fb'
  },
  unitColor: {
    type: String,
    default: '#333333'
  },
  isText: {
    type: Boolean,
    default: false
  },
  showIcon: {
    type: Boolean,
    default: false
  }
})

const flexDirection = computed(() => {
  return props.singleLine ? 'column' : 'row'
})
const fontSizeRatio = computed(() => {
  const maxLength = Math.max(String(props.value).length, props.unit.length)
  return maxLength > 6 ? 0.8 : 1
})
</script>

<style lang="scss" scoped>
.new-text {
  --font-size-ratio: v-bind(fontSizeRatio);
  padding: 32px 24px;
  display: grid;
  grid-auto-flow: v-bind(flexDirection);
  align-items: baseline;
  justify-content: space-between;
  gap: 22px;
  // background: #ecf3fd;
  background: url('@/assets/img/newtext.png') no-repeat;
  background-size: 100% 100%;
  color: #4a62e7;
  border-radius: 10px;

  .title {
    font-size: 28px;
    color: #333333;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .unit {
    // font-size: calc(28px * var(--font-size-ratio));
    font-size: 28px;
    color: #333333;
  }
  .value {
    margin-right: 10px;
    // font-size: calc(40px * var(--font-size-ratio));
    font-size: 40px;
    font-weight: 600;
  }
}
.title-center-style {
  display: flex;
  justify-content: center;
}
</style>
