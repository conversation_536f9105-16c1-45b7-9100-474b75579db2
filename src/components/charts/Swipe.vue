<template>
  <div>
    <van-swipe class="my-swipe" :autoplay="3000">
      <van-swipe-item v-for="(item, index) in chartData.data" :key="index">
        <img style="cursor: pointer; width: 340px; height: 260px" :src="urlPre + item.value" />
      </van-swipe-item>
    </van-swipe>
  </div>
</template>

<script>
import chart from './mixins/chart'
export default {
  name: 'Swipe',
  mixins: [chart],
  props: {
    chartData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      urlPre: import.meta.env.VITE_APP_SITUATION_BASE_URL + '/'
    }
  },
  created() {},
  methods: {
    init() {}
  }
}
</script>

<style lang="scss">
.my-swipe {
  height: 400px;

  .van-swipe__indicators {
    bottom: 5% !important;
  }
}
</style>
