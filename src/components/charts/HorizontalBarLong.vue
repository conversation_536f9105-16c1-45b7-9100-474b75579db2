<template>
  <div :ref="id" class="echarts-horizontal-bar-container"></div>
</template>

<script>
import chart from './mixins/chart'

export default {
  name: 'HorizontalBar',
  mixins: [chart],
  methods: {
    init(chartData) {
      const yAxis = []
      const seriesData = []
      let unit
      if (chartData.data) {
        chartData.data.forEach((item) => {
          if (!unit) {
            unit = item.dw || ''
          }
          yAxis.push(item.name)
          seriesData.push(item.value)
        })
      }
      const option = {
        color: [
          {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 1,
            y2: 0,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(171,206,253,0.61)' // 0% 处的颜色
              },
              {
                offset: 1,
                color: '#73A0FA' // 100% 处的颜色
              }
            ]
          }
        ],
        grid: {
          top: '0',
          bottom: '0',
          left: '5%',
          right: '12%',
          containLabel: true
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          // 将 tooltip 框限制在图表的区域内
          confine: 'true',
          extraCssText: 'z-index: 9;'
        },
        yAxis: {
          nameLocation: 'start',
          type: 'category',
          data: yAxis,
          axisLabel: {
            color: '#666666',
            width: 50,
            overflow: 'truncate',
            lineHeight: 14
          },
          inverse: true,
          splitLine: {
            show: false
          },
          axisLine: {
            show: true
          },
          axisTick: {
            show: false
          }
        },
        xAxis: {
          type: 'value',
          axisLabel: {
            show: false,
            color: '#666666'
          },
          splitLine: {
            show: true
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          }
        },
        dataZoom: [
          {
            show: true,
            type: 'slider',
            filterMode: 'filter',
            width: 15,
            top: 10,
            left: '95%',
            bottom: 10,
            start: 0,
            end: 30,
            orient: 'vertical',
            // 滚动的时候不显示文本
            showDetail: false
          },
          {
            // 必须添加第二个datazoom才能实现鼠标滚动
            type: 'inside',
            yAxisIndex: [0],
            zoomOnMouseWheel: false, // 滚轮是否触发缩放，默认是缩放
            moveOnMouseMove: true, // 鼠标滚轮触发滚动
            moveOnMouseWheel: true,
            // 触摸
            moveOnTouchStart: true,
            moveOnTouchEnd: true
          }
        ],
        series: [
          {
            data: seriesData,
            type: 'bar',
            label: {
              show: true,
              position: 'right'
            }
          }
        ]
      }
      // 有单位时才显示
      if (unit) {
        option.grid.top = '15%'
        option.yAxis.name = `单位：${unit}`
      }
      // 有的话就获取已有echarts实例的DOM节点。
      let myChart = this.$echarts.getInstanceByDom(this.$refs[this.id])
      if (myChart == null) {
        // 如果不存在，就进行初始化
        myChart = this.$echarts.init(this.$refs[this.id], null, {
          height: Math.max(yAxis.length * 15 + 100, 200)
        })
      }
      myChart.setOption(option, true)
    }
  }
}
</script>
