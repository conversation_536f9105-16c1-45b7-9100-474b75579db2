<template>
  <div>
    <div ref="chartRef" class="echarts-container line" @click="handleClickLine" />
    <div
      v-if="showNewTextData"
      class="text-container"
      :style="{
        height: showDetail ? textHeight + 'px' : textHeight * Object.entries(chartData.columnName).length + 'px'
      }"
    >
      <NewTextData
        class="text-1"
        single-line
        unit-color="#4188ff"
        :title="bottomData.xLabel + bottomData.xUnit"
        :unit="showDetail ? '展开' : '收起'"
        @clickUnit="handleClickUnit"
      />
      <NewTextData
        v-for="(item, index) in bottomData.data"
        :key="index"
        single-line
        :title="bottomData.title[index]"
        :unit="bottomData.unit[index]"
        :style="{
          width: '100%',
          transition: 'transform 0.3s ease',
          transform: `translateY(${showDetail ? -100 * (index + 1) + '%' : `-${10 * (index + 1)}%`})`
        }"
        :value="item"
      />
    </div>
  </div>
</template>

<script>
import { ref, reactive, watchEffect, onUnmounted } from 'vue'
import { watch } from 'vue'
import { useECharts } from '@/composables/useECharts'
import { omit, isEmpty } from 'lodash'
import { colors } from '@/utils/constant'
import NewTextData from './NewTextData.vue'

export default {
  name: 'BaseLineWithNewTextData',
  components: { NewTextData },
  props: {
    chartData: {
      type: Object,
      default: () => ({})
    },
    showNewTextData: {
      type: Boolean,
      default: false
    },
    yMin: {
      type: Number,
      default: 0
    },
    yMax: {
      type: Number,
      required: true
    },
    otherConfig: {
      type: Object,
      default: () => ({})
    }
  },
  setup(props) {
    let bottomData = ref({ xLabel: [], title: [], data: [], unit: [], xUnit: [] })
    let textHeight = ref(0)
    watch(
      () => props.chartData,
      (newValue) => {
        const titleArr = Object.values(newValue.columnName)
        const unitArr = Object.values(newValue.unitList)
        const dataArr = Object.values(newValue.data[0])
        // bottomData.value.xLabel = dataArr[0]
        // bottomData.value.xUnit = unitArr[0]
        bottomData.value = {
          xLabel: dataArr[0],
          xUnit: unitArr[0],
          title: [],
          data: [],
          unit: []
        }
        for (let i = 1; i < dataArr.length; i++) {
          bottomData.value.title.push(titleArr[i])
          bottomData.value.data.push(dataArr[i])
          bottomData.value.unit.push(unitArr[i])
        }
      },
      { deep: true }
    )
    let showDetail = ref(true)

    var screenWidth = 0
    updateScreenWidth()

    window.addEventListener('resize', updateScreenWidth) // 监听窗口大小变化

    function updateScreenWidth() {
      screenWidth = window.innerWidth
    }

    onUnmounted(() => {
      window.removeEventListener('resize', updateScreenWidth) // 移除监听器
    })

    const chartRef = ref(null)

    const { setOptions } = useECharts(chartRef)
    const option = reactive({
      grid: {
        top: '20',
        bottom: '5',
        left: props.otherConfig.urbanunemployment ? '0%' : '2.5%',
        right: props.otherConfig.urbanunemployment ? '0%' : '5%',

        containLabel: true
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        },
        // 将 tooltip 框限制在图表的区域内
        confine: 'true',
        extraCssText: 'z-index: 9;',
        backgroundColor: 'rgba(0,0,0,0.6)',
        textStyle: {
          color: 'white'
        },
        valueFormatter: function (value) {
          return value + '%'
        }
      },
      legend: {
        type: 'scroll',
        data: [],
        top: '0',
        left: '0',
        icon: 'rect',
        itemGap: 10,
        itemWidth: 14,
        itemHeight: 4,
        textStyle: {
          fontSize: '14px',
          color: '#73767f',
          padding: [0, 0, 0, 4]
        }
      },
      xAxis: {
        type: 'category',
        data: [],
        show: true,
        axisTick: { alignWithLabel: true },
        axisLabel: {
          fontSize: 11,
          interval: 0,
          width: 40,
          overflow: 'break',
          color: '#73767f'
        },
        splitLine: {
          show: false
        },
        axisLine: {
          lineStyle: {
            color: '#73767f'
          }
        }
      },
      yAxis: {
        type: 'value',
        min: props.yMin,
        nameTextStyle: {
          fontSize: '12px'
        },
        axisLabel: {
          fontSize: '11px',
          color: '#73767f'
        },
        splitLine: {
          show: true
        },
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        }
      },
      series: []
    })
    watchEffect(() => {
      let yMax
      if (props.chartData?.data?.[0]?.value) {
        const data = props.chartData.data.map((item) => {
          return item.value
        })
        yMax = Math.max(...data)
        option.yAxis.max = props.yMax ? props.yMax : yMax
      }
      !isEmpty(props.chartData) && initCharts()
      const dom = document.querySelector('.text-1')
      if (dom) textHeight.value = dom.clientHeight
    })

    function initCharts() {
      let hasUnit = false
      let hasLegend = false
      // x 轴
      let xAxisData = props.chartData.data.map((item) => {
        return item.name
      })
      option.xAxis.data = xAxisData
      // unit
      let unit
      const unitList = props.chartData.unitList
      for (const key in unitList) {
        if (unitList[key]) {
          hasUnit = true
          unit = unitList.value
          break
        }
      }
      // 设置 y 轴单位
      if (unit) {
        option.yAxis.name = `单位：${unit}`
        option.yAxis.nameTextStyle = {
          color: 'hsla(0, 0%, 40%, 1)',
          fontSize: 12,
          padding: [0, 0, 0, screenWidth + 50]
          // align: 'right'
        }
        option.tooltip.valueFormatter = (value) => `${value ? value : '-'}${unit}`
      }
      // series
      let seriesData = []
      // 获取排除 name 后的对象
      const valuesObj = omit(props.chartData.columnName, 'name')
      const entries = Object.entries(valuesObj)
      // series 不小于两个 显示图例
      if (entries.length > 1) {
        option.legend.data = entries.map(([, value]) => value)
        hasLegend = true
      }
      for (let i = 0; i < entries.length; i++) {
        const [seriesKey, seriesName] = entries[i]
        let obj = {
          name: seriesName,
          type: 'line',
          symbolSize: 8
        }
        obj.lineStyle = {
          color: colors[i]
        }
        obj.itemStyle = {
          color: colors[i],
          borderColor: '#fff',
          borderWidth: 0.5
        }
        // if (props.otherConfig.urbanunemployment) {
        //   //就业单独的样式
        //   obj.markLine = {
        //     data: [{ type: 'average', name: 'Avg' }],
        //     symbol: 'none'
        //   }
        // }
        obj.label = {
          show: true,
          position: 'top',
          fontSize: '12px',
          color: 'black'
        }
        obj.data = props.chartData.data.map((item) => {
          return item[seriesKey]
        })

        seriesData.push(obj)
      }
      option.series = seriesData

      // 设置单位与图例显示位置
      if (hasUnit && hasLegend) {
        option.grid.top = '25%'
        option.legend.top = '0'
      } else if (!hasUnit && hasLegend) {
        option.grid.top = '12%'
        option.legend.top = '0'
      } else if (hasUnit && !hasLegend) {
        option.grid.top = '18%'
      }
      setOptions(option)
    }
    function handleClickUnit() {
      showDetail.value = !showDetail.value
    }
    function handleClickLine(params) {
      const data = props.chartData.data
      const offsetX = params.offsetX - 30
      const dom = document.querySelector('.line')
      const perWidth = (0.95 * dom.clientWidth - 30) / data.length
      const index = Math.floor(offsetX / perWidth)
      if (props.chartData.data[index]) {
        const dataArr = Object.values(props.chartData.data[index])
        bottomData.value.xLabel = dataArr[0]
        for (let i = 1; i < dataArr.length; i++) {
          bottomData.value.data[i - 1] = dataArr[i]
        }
      }
    }
    return { textHeight, showDetail, bottomData, chartRef, handleClickLine, handleClickUnit }
  }
}
</script>
<style scoped>
.text-container {
  transition: height 0.3s ease;
}

.text-1 {
  width: 100%;
  margin-top: 25px;
  z-index: 1;
  position: relative;
}
</style>
