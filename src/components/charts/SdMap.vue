<template>
  <div class="map-container">
    <div ref="chartRef" class="echarts-container map"></div>
  </div>
</template>

<script setup>
import { ref, reactive, watchEffect } from 'vue'
import { useECharts } from '@/composables/useECharts'
import shanDong from '../shandong.json'
import { getPopulationList } from '@/api/populationArea'

const props = defineProps({
  inputChartData: {
    type: Array,
    default: () => null
  },
  //  type是2,表示是人口用的该组件
  type: {
    type: Number,
    default: 1
  },
  selectedOptionPopulation: {
    type: String,
    default: ''
  },
  visualMapPieces: {
    type: Array,
    default: () => [
      {
        gt: 800,
        label: '800以上',
        color: '#00539E'
      },
      {
        gte: 600,
        lte: 800,
        label: '800-600',
        color: '#0081F6'
      },
      {
        gte: 400,
        lt: 600,
        label: '600-400',
        color: '#40A4FF'
      },
      {
        gte: 200,
        lt: 400,
        label: '400-200',
        color: '#74BDFF'
      },
      {
        gte: 0,
        lt: 200,
        label: '200-0',
        color: '#C4E3FF'
      }
    ]
  },
  unit: {
    props: String,
    default: '人/平方公里'
  },
  isDistrict: {
    type: Boolean,
    default: false
  },
  showVisualMap: {
    type: Boolean,
    default: true
  }
})

const chartRef = ref(null)
// const colorList = ['#00539E', '#0081F6', '#40A4FF', '#74BDFF', '#C4E3FF']
const { setOptions, echarts } = useECharts(chartRef)
let cityData = ref([
  {
    name: '济南市',
    coords: [117.121225, 36.66466],
    value: 0
  },
  {
    name: '青岛市',
    coords: [120.3, 36.62],
    value: 0
  },
  {
    name: '淄博市',
    coords: [118.05, 36.78],
    value: 0
  },
  {
    name: '枣庄市',
    coords: [117.57, 34.86],
    value: 0
  },
  {
    name: '东营市',
    coords: [118.49, 37.46],
    value: 0
  },
  {
    name: '烟台市',
    coords: [120.9, 37.32],
    value: 0
  },
  {
    name: '潍坊市',
    coords: [119.1, 36.62],
    value: 0
  },
  {
    name: '济宁市',
    coords: [116.7, 35.42],
    value: 0
  },
  {
    name: '泰安市',
    coords: [117.13, 36.18],
    value: 0
  },
  {
    name: '威海市',
    coords: [122.1, 37.2],
    value: 0
  },
  {
    name: '日照市',
    coords: [119.1, 35.62],
    value: 0
  },
  {
    name: '莱芜市',
    coords: [117.7, 36.28],
    value: 0
  },
  {
    name: '临沂市',
    coords: [118.35, 35.05],
    value: 0
  },
  {
    name: '德州市',
    coords: [116.39, 37.45],
    value: 0
  },
  {
    name: '聊城市',
    coords: [115.97, 36.45],
    value: 0
  },
  {
    name: '滨州市',
    coords: [118.03, 37.36],
    value: 0
  },
  {
    name: '滨州市',
    coords: [117.52, 37.5],
    value: 0
  },
  {
    name: '菏泽市',
    coords: [115.480656, 35.23375],
    value: 0
  }
])

const option = ref({
  visualMap: {
    right: 10,
    bottom: 10,
    showLabel: true,
    pieces: props.visualMapPieces,
    show: props.showVisualMap
  },

  // geo: {
  //   map: 'shanDong',
  //   roam: false,
  //   // zoom: 1,
  //   // select: {
  //   //   itemStyle: { areaColor: 'rgb(3, 243, 252, 0.5)' }
  //   // },
  //   itemStyle: {
  //     borderType: 'solid',
  //     borderColor: 'white',
  //     borderWidth: 2,
  //     borderDashOffset: 2,
  //     areaColor: 'rgb(3, 243, 252, 0)'
  //   }
  // },
  tooltip: {
    triggerOn: 'click',
    confine: true,
    backgroundColor: 'rgba(0,0,0,0.6)',
    textStyle: {
      color: 'white'
    },
    formatter: function (e) {
      if (!props.isDistrict) {
        return `<div>${e.name}</div>
        <div>户籍人口: ${e.data.allData.hjrk} 万人</div>
        <div>常住人口: ${e.data.allData.czrk} 万人</div>
        <div>人口密度: ${e.data.allData.num} 人/平方公里</div>
        <div>GDP: ${e.data.allData.value} 亿元</div>
        `
        // return e.name + '：' + e.value + e.data.mapUnit
      } else {
        return `<div>${e.name}</div>
      <div>县级政区合计：${e.value}个</div>
      <div>市辖区：${e.data.districtNum1}个</div>
      <div>县级市：${e.data.districtNum2}个</div>
      <div>县：${e.data.districtNum3}个</div>`
      }
    },
    extraCssText: 'z-index: 0'
  },
  series: [
    {
      name: 'shanDong',
      type: 'map',
      map: 'shanDong',
      coordinateSystem: 'geo',
      data: cityData,
      select: {
        // disabled: true
        // itemStyle: {
        //   areaColor: 'rgb(3, 243, 252, 0)',
        //   borderWidth: 2
        // }
      },
      zoom: 1.2,
      // geoIndex: 0,
      itemStyle: {
        borderColor: 'white',
        borderWidth: 1,
        borderType: 'solid',
        borderDashOffset: 2,
        areaColor: 'rgb(3, 243, 252, 0)'
        // areaColor: (param) => {
        //   console.log(param)
        //   let index = Math.max(-Math.floor(param.data / 200) + 4, 0)
        //   return colorList[index]
        // },
        // borderType: 'solid'
      },
      emphasis: {
        // 鼠标移入颜色
        // disabled: true,
        areaColor: 'rgb(3, 243, 252, 0)',
        //  rgb(3, 243, 252, 0.5)
        borderColor: 'white',
        borderWidth: 4
      },
      label: {
        show: true,
        color: '#000',
        fontSize: '10px',
        emphasis: {
          color: '#000'
        }
      }
    }
  ]
})

watchEffect(() => {
  initCharts()
})
echarts.registerMap('shanDong', shanDong)

async function initCharts() {
  if (props.inputChartData) {
    props.inputChartData.forEach((item, index) => {
      cityData.value[index].name = item.city
      cityData.value[index].value = item.value
      if (props.isDistrict) {
        cityData.value[index].districtNum1 = item.districtNum1
        cityData.value[index].districtNum2 = item.districtNum2
        cityData.value[index].districtNum3 = item.districtNum3
      } else {
        cityData.value[index].mapUnit = item.mapUnit
        cityData.value[index].allData = item.allData
      }
      //  else {
      //   cityData.value[index].hjrk = item.hjrk
      //   cityData.value[index].czrk = item.czrk
      //   cityData.value[index].num = item.num
      //   cityData.value[index].jdzvalue = item.jdzvalue
      // }
    })
  } else {
    const res = await getPopulationList.getPopulationDensityTop()
    if (res.success) {
      const data = res.result
      data.forEach((item, index) => {
        cityData.value[index].name = item.city
        cityData.value[index].value = item.num
      })
    }
  }
  if (props.selectedOptionPopulation == 'GDP分布(2024)') {
    option.value.visualMap = {
      right: 10,
      bottom: 10,
      showLabel: true,
      pieces: [
        {
          gt: 10000,
          label: '10000以上',
          color: '#00539E'
        },
        {
          gte: 6000,
          lte: 10000,
          label: '10000-6000',
          color: '#0081F6'
        },
        {
          gte: 4000,
          lt: 6000,
          label: '6000-4000',
          color: '#40A4FF'
        },
        {
          gte: 3000,
          lt: 4000,
          label: '4000-3000',
          color: '#74BDFF'
        },
        {
          gte: 0,
          lt: 3000,
          label: '3000-0',
          color: '#C4E3FF'
        }
      ],
      selectedMode: false,
      show: props.showVisualMap
    }
  } else if (props.selectedOptionPopulation == '户籍人口分布' || props.selectedOptionPopulation == '常住人口分布') {
    option.value.visualMap = {
      right: 10,
      bottom: 10,
      showLabel: true,
      pieces: [
        {
          gt: 1000,
          label: '1000以上',
          color: '#00539E'
        },
        {
          gte: 800,
          lte: 1000,
          label: '1000-800',
          color: '#0081F6'
        },
        {
          gte: 500,
          lt: 800,
          label: '800-500',
          color: '#40A4FF'
        },
        {
          gte: 300,
          lt: 500,
          label: '500-300',
          color: '#74BDFF'
        },
        {
          gte: 0,
          lt: 300,
          label: '300-0',
          color: '#C4E3FF'
        }
      ],
      selectedMode: false,
      show: props.showVisualMap
    }
  } else {
    option.value.visualMap = {
      right: 10,
      bottom: 10,
      showLabel: true,
      pieces: [
        {
          gt: 800,
          label: '800以上',
          color: '#00539E'
        },
        {
          gte: 600,
          lte: 800,
          label: '800-600',
          color: '#0081F6'
        },
        {
          gte: 400,
          lt: 600,
          label: '600-400',
          color: '#40A4FF'
        },
        {
          gte: 200,
          lt: 400,
          label: '400-200',
          color: '#74BDFF'
        },
        {
          gte: 0,
          lt: 200,
          label: '200-0',
          color: '#C4E3FF'
        }
      ],
      selectedMode: false,
      show: props.showVisualMap
    }
  }
  setOptions(option.value)
}
</script>
<style lang="scss" scoped>
.map-container {
  width: 100%;
}
</style>
