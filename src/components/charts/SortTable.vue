<template>
  <div class="table-wrapper">
    <div class="table-container" :style="{ height: isPad ? '300px' : '100%' }">
      <div class="grid__tr t-head" style="padding-left: 0px">
        <div
          class="grid-header header-label"
          style="width: auto; flex: 1 1 0%; justify-content: flex-start; padding-left: 20px"
          @click="handleSort('region')"
        >
          <span>地区</span>
          <div class="sort-caret-group">
            <svg
              class="icon"
              viewBox="0 0 1024 1024"
              xmlns="http://www.w3.org/2000/svg"
              width="10"
              height="10"
              :fill="getSortIconColor('region', 'asc')"
            >
              <path d="M0 1024 L512 512 L1024 1024 Z"></path>
            </svg>
            <svg
              class="icon"
              viewBox="0 0 1024 1024"
              xmlns="http://www.w3.org/2000/svg"
              width="10"
              height="10"
              :fill="getSortIconColor('region', 'desc')"
            >
              <path d="M0 0 L512 512 L1024 0 Z"></path>
            </svg>
          </div>
        </div>
        <div
          class="grid-header header-label-end"
          style="width: auto; flex: 1 1 0%; justify-content: center"
          @click="handleSort('value')"
        >
          <span>同比(%)</span>
          <div class="sort-caret-group">
            <svg
              class="icon"
              viewBox="0 0 1024 1024"
              xmlns="http://www.w3.org/2000/svg"
              width="10"
              height="10"
              :fill="getSortIconColor('value', 'asc')"
            >
              <path d="M0 1024 L512 512 L1024 1024 Z"></path>
            </svg>
            <svg
              class="icon"
              viewBox="0 0 1024 1024"
              xmlns="http://www.w3.org/2000/svg"
              width="10"
              height="10"
              :fill="getSortIconColor('value', 'desc')"
            >
              <path d="M0 0 L512 512 L1024 0 Z"></path>
            </svg>
          </div>
        </div>
      </div>
      <div class="table__content">
        <div class="transition-container">
          <div class="scroll-wrapper">
            <div
              v-for="(item, index) in list"
              :key="index"
              class="grid__row"
              :style="{ backgroundColor: item.name === '山东省' ? 'rgb(254, 248, 232)' : 'rgb(245, 249, 255)' }"
              style="padding-left: 0px"
            >
              <div
                class="grid-cell row-label"
                style="
                  width: auto;
                  flex: 1 1 0%;
                  justify-content: flex-start;
                  color: rgb(18, 18, 18);
                  padding-left: 20px;
                "
              >
                <div class="max-w-100%">
                  <span>{{ item.name }}</span>
                </div>
              </div>
              <div
                class="grid-cell row-label-end"
                style="width: auto; flex: 1 1 0%; justify-content: center; font-size: 16px; font-family: DIN-Bold"
                :style="{ color: item.value > 0 ? '#ff0005' : '#23E299' }"
              >
                <div>
                  <img
                    :src="item.value > 0 ? graphIcon1 : graphIcon2"
                    alt=""
                    style="width: 8px; height: 13px; margin-right: 2px"
                  /><span>{{ Math.abs(item.value) }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      v-if="!isPad && chartList.length > 7"
      style="
        font-size: 12px;
        font-family: PingFang, serif;
        color: rgb(58, 117, 197);
        width: 100%;
        text-align: center;
        margin: 15px 0px 0px;
      "
      @click="moreClick"
    >
      <div style="display: flex; align-items: center; justify-content: center; gap: 4px; font-size: 14px">
        {{ more ? '收起' : '查看更多'
        }}<svg
          :style="{ transform: more ? 'rotate(180deg)' : 'rotate(0deg)' }"
          width="16px"
          height="12px"
          viewBox="0 0 16 16"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path d="M2 5 L8 11 L14 5" stroke="#0290F9" stroke-width="2" fill="none"></path>
        </svg>
      </div>
    </div>
  </div>
</template>

<script>
import CustomToolTip from '../tooltip/custom-tooltip.vue'
import graphIcon1 from '@/assets/img/arrow-up.png'
import graphIcon2 from '@/assets/img/arrow-down.png'

export default {
  name: 'Table',
  components: { CustomToolTip },
  props: {
    showIcon: {
      type: Boolean,
      default: false
    },
    chartList: {
      type: Array,
      default: () => []
    },
    pageHeight: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      graphIcon1,
      graphIcon2,
      more: false,
      list: [],
      sortConfig: {
        key: 'value',
        direction: 'desc' // null, 'asc', or 'desc'
      },
      isPad: window.matchMedia('(min-width: 600px)').matches
    }
  },
  watch: {
    chartList: {
      handler(newVal) {
        if (newVal) {
          this.dealData()
          this.applySorting()
        }
      },
      immediate: true
    }
  },
  methods: {
    handleSort(key) {
      if (this.sortConfig.key === key) {
        // Toggle sort direction
        if (this.sortConfig.direction === 'asc') {
          this.sortConfig.direction = 'desc'
        } else if (this.sortConfig.direction === 'desc') {
          this.sortConfig.direction = 'asc'
        }
      } else {
        // New sort key
        this.sortConfig.key = key
        this.sortConfig.direction = 'asc'
      }
      this.applySorting()
    },
    getSortIconColor(key, direction) {
      if (this.sortConfig.key === key && this.sortConfig.direction === direction) {
        return '#0290F9'
      }
      return '#BDD3F8'
    },

    applySorting() {
      if (!this.sortConfig.key || !this.sortConfig.direction) {
        this.dealData() // Reset to original order
        return
      }
      const sortedList = [...this.chartList].sort((a, b) => {
        // 如果是全国，始终放在第一位
        if (a.name === '全国') return -1
        if (b.name === '全国') return 1

        let compareA = parseFloat(a[this.sortConfig.key === 'region' ? 'region_code' : 'value'])
        let compareB = parseFloat(b[this.sortConfig.key === 'region' ? 'region_code' : 'value'])
        if (this.sortConfig.direction === 'asc') {
          return compareA > compareB ? 1 : compareA < compareB ? -1 : 0
        } else {
          return compareA < compareB ? 1 : compareA > compareB ? -1 : 0
        }
      })

      this.list = this.more || this.isPad ? [...sortedList] : sortedList.slice(0, 7)
    },

    dealData() {
      if (this.more || this.isPad) {
        this.list = [...this.chartList]
      } else {
        this.list = this.chartList.slice(0, 7)
      }
    },
    moreClick() {
      this.more = !this.more
      this.dealData()
      this.applySorting()
    }
  }
}
</script>

<style scoped lang="scss">
.table-wrapper {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
  margin: 10px 14px;
}
.table-container {
  width: 100%;
  position: relative;
}
.t-head {
  background-color: #e0f1ff;
  border-radius: 15px;
  color: #08f;
  font-family: PingFang, serif;
  font-size: 16px;
  display: flex;
  min-width: -moz-fit-content;
  min-width: fit-content;
}
.grid__tr {
  min-height: 24px;
  padding-top: 3px;
  padding-bottom: 3px;
  display: flex;
  min-width: -moz-fit-content;
  min-width: fit-content;
}
.grid-header {
  display: flex;
  gap: 4px;
  align-items: center;
  font-size: 18px;
}
.header-label {
  display: flex;
  flex: 1;
  text-align: center;
  font-family: PingFang, serif;
}
.sort-caret-group {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  gap: 5px;
}
.header-label-end {
  display: flex;
  width: 130px;
  text-align: center;
  font-family: PingFang, serif;
}
.grid__row {
  display: flex;
  background: #f5f9ff;
  border-radius: 15px;
  margin-top: 7px;
  height: 40px;
  min-width: 100%;
  width: -moz-fit-content;
  width: fit-content;
}
.grid-cell {
  display: flex;
  align-items: center;
  font-weight: 500;
  color: #121212;
  line-height: 23px;
  font-style: normal;
  font-family: PingFang;
  margin: 5px 0;
  gap: 10px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.row-label-end {
  text-align: center;
  font-family: DIN-Bold, serif;
  font-size: 16px;
}
.transition-container {
  width: 100%;
  height: auto;
  overflow: hidden;
}
.scroll-wrapper {
  overflow: hidden;
}
.row-label {
  flex: 1;
  text-align: left;
  font-size: 18px;
}
.table__content {
  overflow-x: hidden;
  flex: 1;
}

/* pad及以上尺寸的样式 */
@media (min-width: 600px) {
  .transition-container {
    width: 100%;
    height: 260px;
    overflow: hidden;
  }

  .scroll-wrapper {
    height: 100%;
    overflow-y: scroll;

    /* 隐藏滚动条但保持滚动功能 */
    &::-webkit-scrollbar {
      display: none;
    }
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  .row-label {
    font-size: 12px;
  }
  .grid__row {
    height: 32px;
  }
  .grid-header {
    font-size: 12px;
  }
}
</style>
