<template>
  <div class="statistic-index2">
    <!-- 前2个 -->
    <div v-for="item in chartData.data.slice(0, 2)" :key="item.name" class="statistic-index2__item">
      <div class="img"></div>
      <div class="statistic-index2__title">{{ item.name }}</div>
      <div class="text-data__data-value">{{ item.value }}%</div>
      <!-- 判断是否有增长/降低趋势值 更改右侧边框height -->
      <div class="statistic-index2-border" style="position: absolute; height: 70px"></div>
      <van-divider class="statistic-index2-divider" dashed></van-divider>
    </div>
    <!-- 第2个以后的n个 -->
    <div v-for="item in chartData.data.slice(2)" :key="item.name" class="statistic-index3__item">
      <div class="img"></div>
      <div class="statistic-index3__title">{{ item.name }}</div>
      <div class="text-data__data-value">{{ item.value }}%</div>
      <!-- 判断是否有增长/降低趋势值 更改右侧边框height -->
      <div
        class="statistic-index3-border"
        style="position: absolute"
        :style="{ height: item.UP_CODE ? '70px' : '40px' }"
      ></div>
    </div>
  </div>
</template>

<script>
import chart from './mixins/chart'
export default {
  name: 'TextEvaluation',
  mixins: [chart],
  props: {
    chartData: {
      type: Object,
      default: () => {}
    }
  },
  mounted() {},
  methods: {
    init(chartData) {
      this.itemList = chartData.dataList?.[0]?.itemList
    }
  }
}
</script>

<style lang="scss" scoped>
.statistic-index2 {
  display: flex;
  flex-wrap: wrap;
}
.statistic-index2__item {
  width: 50%;
  display: flex;
  flex-direction: column;
  justify-items: center;
  align-items: center;

  .img {
    width: 88px;
    height: 88px;
    margin-top: 20px;
  }
  &:nth-child(1) {
    .img {
      background: url('../../assets/img/echartsImg/satisfaction1.png');
      background-size: 100% 100%;
    }
  }
  &:nth-child(2) {
    .img {
      background: url('../../assets/img/echartsImg/satisfaction2.png');
      background-size: 100% 100%;
    }
  }

  // 奇数item右侧有竖杠分隔
  &:nth-child(odd) {
    .statistic-index2-border {
      border-right: 0.5px dashed rgba(153, 153, 153, 0.3);
      margin-top: 7%;
      width: 42%;
    }
  }

  // 指标名称
  .statistic-index2__title {
    font-size: 28px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #333333;
    // 考虑到存在标题过长的情况 居中 设置margin和行高
    width: 80%;
    text-align: center;
    line-height: 38px;
    margin: 15px 0;
  }
  // 指标数值
  .statistic-index2__data {
    display: flex;
    // 数值
    .text-data__data-value {
      font-size: 48px;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
      color: #3b93fb;
      line-height: 48px;
    }
    // 单位
    .text-data__data-unit {
      font-size: 28px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #666666;
      line-height: 28px;
      margin-top: 12px;
    }
  }
  // 增长降低趋势
  .statistic-index2__ratio {
    height: 54px;
    // 上下左右居中
    display: flex;
    // flex-direction: column;
    justify-items: center;
    align-items: center;
    span {
      margin-right: 10px;
      line-height: 54px;
    }
  }
  // Divider 分割线
  .statistic-index2-divider {
    width: 100%;
    margin: 20px 0 2px 0;
  }
}

.statistic-index3__item {
  width: 33.3%;
  display: flex;
  flex-direction: column;
  justify-items: center;
  align-items: center;

  .img {
    width: 88px;
    height: 88px;
    margin-top: 20px;
  }
  &:nth-child(3) {
    .img {
      background: url('../../assets/img/echartsImg/satisfaction3.png');
      background-size: 100% 100%;
    }
  }
  &:nth-child(4) {
    .img {
      background: url('../../assets/img/echartsImg/satisfaction4.png');
      background-size: 100% 100%;
    }
  }
  &:nth-child(5) {
    .img {
      background: url('../../assets/img/echartsImg/satisfaction5.png');
      background-size: 100% 100%;
    }
  }
  .statistic-index3-border {
    border-right: 0.5px dashed rgba(153, 153, 153, 0.3);
    margin-top: 7%;
    width: 28%;
  }

  // 奇数item右侧有竖杠分隔
  &:nth-child(5),
  &:nth-child(8),
  &:nth-child(11) {
    .statistic-index3-border {
      border-right: unset;
      margin-top: 7%;
      width: 28%;
    }
  }

  // 指标名称
  .statistic-index3__title {
    font-size: 28px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #333333;
    // 考虑到存在标题过长的情况 居中 设置margin和行高
    width: 80%;
    text-align: center;
    line-height: 38px;
    margin: 15px 0;
  }
  // 指标数值
  // 数值
  .text-data__data-value {
    font-size: 48px;
    font-family: PingFangSC-Semibold, PingFang SC;
    font-weight: 600;
    color: #3b93fb;
    line-height: 48px;
  }
  // 单位
  .text-data__data-unit {
    font-size: 28px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #666666;
    line-height: 56px;
    margin-top: 12px;
  }

  // 增长降低趋势
  .statistic-index3__ratio {
    // 上下左右居中
    display: flex;
    // flex-direction: column;
    justify-items: center;
    align-items: center;
    margin-top: 20px;
    .t1 {
      line-height: 30px;
      font-size: 28px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 500;
      color: #3b93fb;
    }
  }
  // Divider 分割线
  .statistic-index3-divider {
    width: 100%;
    margin: 20px 0 2px 0;
  }
}
</style>
