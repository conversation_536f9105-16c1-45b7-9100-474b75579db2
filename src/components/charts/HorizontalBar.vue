<template>
  <div :ref="id" class="echarts-horizontal-bar-container"></div>
</template>

<script>
import chart from './mixins/chart'

export default {
  name: 'HorizontalBar',
  mixins: [chart],
  data() {
    return {
      myChart: null
    }
  },
  // watch: {
  //   chartData: {
  //     handler (val) {
  //       console.log(val, 3333);
  //       window.addEventListener('resize', function () {

  //         this.init()
  //       })
  //     },
  //     deep: true
  //   }
  // },
  methods: {
    init(chartData) {
      this.myChart && this.myChart.dispose()
      const yAxis = []
      const seriesData = []
      let unit
      if (chartData.data) {
        chartData.data.forEach((item) => {
          if (!unit) {
            unit = item.dw || ''
          }
          yAxis.push(item.name)
          seriesData.push(item.value)
        })
      }
      const option = {
        color: [
          {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 1,
            y2: 0,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(171,206,253,0.61)' // 0% 处的颜色
              },
              {
                offset: 1,
                color: '#73A0FA' // 100% 处的颜色
              }
            ]
          }
        ],
        grid: {
          top: '5%',
          bottom: '0',
          left: '5%',
          right: '12%',
          containLabel: true
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          // 将 tooltip 框限制在图表的区域内
          confine: 'true',
          extraCssText: 'z-index: 9;'
        },
        yAxis: {
          nameLocation: 'start',
          type: 'category',
          data: yAxis,
          axisLabel: {
            color: '#666666',
            width: 50,
            overflow: 'truncate',
            fontSize: '10px',
            lineHeight: 14
          },
          inverse: true,
          splitLine: {
            show: false
          },
          axisLine: {
            show: true
          },
          axisTick: {
            show: false
          }
        },
        xAxis: {
          type: 'value',
          axisLabel: {
            show: false,
            fontSize: '10px',
            color: '#666666'
          },
          splitLine: {
            show: true
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          }
        },
        series: [
          {
            data: seriesData,
            type: 'bar',
            label: {
              show: true,
              position: 'right'
            }
          }
        ]
      }
      // 有单位时才显示
      if (unit) {
        option.grid.top = '15%'
        option.yAxis.name = `单位：${unit}`
      }
      // 有的话就获取已有echarts实例的DOM节点。
      this.myChart = this.$echarts.getInstanceByDom(this.$refs[this.id])
      if (this.myChart == null) {
        // 如果不存在，就进行初始化
        this.myChart = this.$echarts.init(this.$refs[this.id], null, null)
      }
      this.myChart.setOption(option, true)
      this.myChart.resize({ height: Math.max(yAxis.length * 15 + 100, 200) }) //这里是处理echarts是null，而且没数据，默认就是200，但是等有数据就不更新了的情况
    }
  }
}
</script>
