<!-- 图文弹框，上图下文 -->
<template>
  <div>
    <!-- 评论弹窗 -->
    <van-dialog v-model="show" class="imgTextDialog">
      <div class="dialog-right">
        <div class="title-city">{{ titleCity }}</div>
        <div class="title-close" @click="closeDialog"></div>
        <div class="right-main">
          <img :src="ytImg" />
          <p class="bian">
            <span class="neirong">{{ ytDesc }}</span>
          </p>
        </div>
      </div>
    </van-dialog>
  </div>
</template>
<script>
import { getAction } from '@/api/manage'
export default {
  name: 'ImgTextDialog',
  data() {
    return {
      show: false,
      regionCode: '',
      titleCity: '',
      ytDesc: '',
      ytImg: '',
      picturePrefixUrl: import.meta.env.VITE_APP_SITUATION_BASE_URL + '/'
    }
  },
  methods: {
    showDialog(titleCity, regionCode, show) {
      this.show = show
      this.titleCity = titleCity
      this.regionCode = regionCode + '000000'
      this.getData()
    },
    async getData() {
      const { result } = await getAction(this.picturePrefixUrl + 'visual/getXzqhInfo', {
        code: this.regionCode
      })
      this.ytImg = this.picturePrefixUrl + result.tp
      this.ytDesc = result.jj
    },
    closeDialog() {
      this.show = false
    }
  }
}
</script>
