<template>
  <div class="text-statistic">
    <div v-for="(i, index) of rowTotal" :key="i">
      <TextGrid :column="rowColCount[index]" :value="newValueArrr[index]" />
    </div>
  </div>
</template>

<script>
import chart from './mixins/chart'
import TextGrid from './TextGrid.vue'

export default {
  name: 'TextData',
  components: {
    TextGrid
  },
  mixins: [chart],
  props: {
    colLayout: {
      type: String,
      default: () => '2*2'
    },
    chartData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      newValueArrr: [],
      isSeparator: null,
      rowTotal: 0,
      rowColCount: []
    }
  },
  computed: {},
  mounted() {
    this.getSeparator()
    this.getRowTotal()
    this.getRowColCount()
  },
  methods: {
    init(chartData) {
      if (chartData.data && chartData.data.length > 0 && (this.newValueArrr.length === 0 || chartData.changeTab)) {
        if (chartData.unitList && chartData.unitList.value) {
          chartData.data.forEach((v) => {
            v.unit = v.unit !== undefined ? v.unit : chartData.unitList.value
          })
        }
        this.newValueArrr = []
        if (this.isSeparator) {
          for (let i = 0; i < this.rowColCount.length; i++) {
            this.newValueArrr.push(chartData.data.splice(0, this.rowColCount[i]))
          }
        } else {
          this.newValueArrr.push(chartData.data)
        }
      }
    },
    getSeparator() {
      this.isSeparator = this.colLayout.indexOf('*') === -1 ? false : true
    },
    getRowTotal() {
      if (this.colLayout.indexOf('*') === -1) {
        this.rowTotal = Number(this.colLayout)
      } else {
        this.rowTotal = this.colLayout.split('*').length
      }
    },
    getRowColCount() {
      this.rowColCount = []
      if (this.isSeparator) {
        this.colLayout.split('*').forEach((item) => {
          this.rowColCount.push(Number(item))
        })
      } else {
        this.rowColCount.push(Number(this.colLayout))
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.text-statistic {
  & > div {
    margin-bottom: 30px;
  }

  & > div:last-child {
    margin-bottom: 6px;
  }
}
</style>
