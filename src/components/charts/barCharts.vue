<template>
  <div ref="chartRef" class="echarts-container" @click="handleClick" />
</template>

<script>
import { ref, reactive, watchEffect } from 'vue'
import { useECharts } from '@/composables/useECharts'
import { omit, isEmpty } from 'lodash'
import { colors } from '@/utils/constant'

export default {
  name: 'BarCharts',
  props: {
    chartData: {
      type: Object,
      default: () => ({})
    }
  },
  setup(props, { emit }) {
    const chartRef = ref(null)
    const { setOptions, getInstance } = useECharts(chartRef)
    const option = reactive({
      grid: {
        top: '10',
        bottom: '5',
        left: '2.5%',
        right: '5%',
        containLabel: true
      },
      tooltip: {
        trigger: 'axis',
        formatter: function (params) {
          return (
            props.chartData.data[params[0].dataIndex].name +
            `<br />` +
            `面积：${props.chartData.data[params[0].dataIndex].value}` +
            `<br />` +
            `占比：${props.chartData.data[params[0].dataIndex].value1}%`
          )
        },
        axisPointer: {
          type: 'shadow'
        },
        // 将 tooltip 框限制在图表的区域内
        confine: 'true',
        extraCssText: 'z-index: 9;'
      },
      legend: {
        type: 'scroll',
        data: [],
        top: '0',
        left: '0',
        icon: 'rect',
        itemGap: 14,
        itemWidth: 10,
        itemHeight: 10,
        textStyle: {
          fontSize: '14px',
          color: '#8C8C8C',
          padding: [0, 0, 0, 4]
        }
      },
      // dataZoom: [
      //   {
      //     type: 'inside',
      //     start: 0,
      //     end: 11, // 数据窗口范围的结束百分比
      //     minValueSpan: 10, // 在类目轴上可以设置为 n 表示至少显示 n 个类目。
      //     zoomLock: true
      //   }
      // ],
      xAxis: {
        type: 'category',
        data: [],
        axisLabel: {
          color: 'hsla(0, 0%, 0%, 0.45)',
          rotate: 0,
          fontSize: '12px',
          // padding: [20, 0, 0, -10],
          formatter(value) {
            let ret = ''
            let maxLength = 7
            for (let i = 0; i < value.length; i += maxLength) {
              ret += value.substring(i, i + maxLength) + '\n'
            }
            return ret
          }
          // rich: {
          //   a: {
          //     verticalAlign: 'middle'
          //   }
          // }
          // width: 60,
          // overflow: 'truncate'
        },
        axisTick: {
          show: false
        }
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          fontSize: '12px',
          color: '#999999'
        },
        nameRotate: '0.1'
      },
      series: []
    })

    watchEffect(() => {
      !isEmpty(props.chartData) && initCharts()
    })

    function initCharts() {
      let hasUnit = false
      let hasLegend = false
      // x 轴
      let xAxisData = props.chartData.data.map((item) => {
        return item.name
      })
      option.xAxis.data = xAxisData
      // 柱状图只有一个单位
      let unit
      const unitList = props.chartData.unitList
      for (const key in unitList) {
        if (unitList[key]) {
          hasUnit = true
          unit = unitList[key]
          break
        }
      }
      // 设置 y 轴单位
      if (unit) {
        option.yAxis.name = `单位：${unit}`
        option.yAxis.nameTextStyle = {
          color: 'hsla(0, 0%, 40%, 1)',
          fontSize: 16
          // align: 'right'
        }
        option.tooltip.valueFormatter = (value) => `${value ? value : '-'}${unit}`
      }
      // series
      let seriesData = []
      // 获取排除 name 后的对象
      const valuesObj = omit(props.chartData.columnName, 'name')
      const entries = Object.entries(valuesObj)
      // series 不小于两个 显示图例
      if (entries.length > 1) {
        option.legend.data = entries.map(([, value]) => value)
        hasLegend = true
      }
      for (let i = 0; i < entries.length; i++) {
        const [seriesKey, seriesName] = entries[i]
        let obj = { name: seriesName, type: 'bar' }
        obj.itemStyle = {
          color: colors[i]
        }
        obj.data = props.chartData.data.map((item) => {
          return item[seriesKey]
        })
        seriesData.push(obj)
      }
      option.series = seriesData

      // 设置单位与图例显示位置
      if (hasUnit && hasLegend) {
        option.yAxis.nameGap = 50
        option.grid.top = '22%'
        option.legend.top = '10%'
      } else if (!hasUnit && hasLegend) {
        option.grid.top = '12%'
        option.legend.top = '0'
      } else if (hasUnit && !hasLegend) {
        option.grid.top = '12%'
        option.yAxis.nameGap = 20
      }

      setOptions(option)
      let chart = getInstance()
      chart?.on('click', (p) => {
        emit('choose', p)
      })
    }
    function handleClick(params) {
      emit('clickLine', params)
    }

    return { chartRef, handleClick }
  }
}
</script>
