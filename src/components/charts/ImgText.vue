<template>
  <div class="imgtext-content">
    <div v-for="(item, index) in chartData.data" :key="index" class="content">
      <div class="left-picture">
        <img :src="urlPre + item.value" alt="" />
      </div>
      <div class="right-content">
        <div class="title-name" style="font-size: 16px">{{ item.name }}</div>
        <div style="display: flex">
          <div class="title" style="font-size: 14px">{{ chartData.columnName.value1 }}：</div>
          <div class="ms" style="font-size: 14px">{{ item.value1 }}</div>
        </div>
        <div v-if="item.value2" style="display: flex">
          <div class="title" style="font-size: 14px">{{ chartData.columnName.value2 }}：</div>
          <div class="ms" style="font-size: 14px">{{ item.value2 }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import chart from './mixins/chart'
export default {
  name: 'ImgText',
  mixins: [chart],
  props: {
    chartData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      urlPre: import.meta.env.VITE_APP_SITUATION_BASE_URL + '/'
    }
  },
  created() {},
  methods: {
    init() {}
  }
}
</script>

<style lang="scss">
.imgtext-content {
  width: 100%;
  max-height: 420px;
  overflow: auto;
  color: #000000;
  font-size: 18px !important;

  .content {
    display: flex;
    align-items: center;
    margin-bottom: 2px;

    .left-picture {
      margin-right: 14px;

      img {
        width: 120px;
        height: 140px;
      }
    }

    .right-content {
      .title-name {
        color: #3b93fb;
        font-size: 20px;
        margin-bottom: 6px;
      }

      .title {
        font-size: 20px;
        font-weight: bold;
      }

      .ms {
        width: 410px;
        display: -webkit-box;
        overflow: hidden;
        -webkit-line-clamp: 3; //控制行数
        -webkit-box-orient: vertical;
        line-height: 25px;
        font-size: 18px;
      }
    }
  }
}
</style>
