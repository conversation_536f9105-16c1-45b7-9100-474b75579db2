<template>
  <div class="table-wrapper" :style="pageHeight ? 'max-height: unset' : ''">
    <header class="table__header">
      <div class="table__row">
        <span
          v-for="(key, index) in chartData.column"
          :key="index"
          :class="chartData.unitList[key] == 'orderNum' ? 'order-class' : ''"
          @click="filterData(key)"
        >
          {{ chartData.columnName[key] }}
          <van-icon v-show="!hjState && index === 2 && showarrowIcon" name="arrow-down" />
          <van-icon v-show="hjState && index === 2 && showarrowIcon" name="arrow-up" />
          <van-icon v-show="!czState && index === 3 && showarrowIcon" name="arrow-down" />
          <van-icon v-show="czState && index === 3 && showarrowIcon" name="arrow-up" />
        </span>
      </div>
    </header>
    <div v-show="isLoading" class="loading-icon"></div>
    <main v-show="!isLoading" :class="{ table__body: true, scroll: scroll }">
      <div v-for="(item, index) in chartData.data" :key="index" class="table__row">
        <div
          v-for="(key, i) in chartData.column"
          :key="i"
          :title="item[key] + chartData.unitList[key]"
          :class="chartData.unitList[key] == 'orderNum' ? 'order-class' : ''"
        >
          <template v-if="showIcon">
            <CustomToolTip
              v-if="i !== 3"
              :value="
                (item[key] ? item[key] : '') + (chartData.unitList[key] != 'orderNum' ? chartData.unitList[key] : '')
              "
              :chart-data="chartData"
              :data-index="index"
            />
            <div v-if="i === 3 && index !== 0 && index !== 1 && index !== 2" class="circleNum">
              <div>{{ item[key] }}</div>
            </div>
            <img v-if="i === 3 && index === 0" src="@/assets/img/no1.png" alt="" class="img1" />
            <img v-if="i === 3 && index === 1" src="@/assets/img/no2.png" alt="" class="img2" />
            <img v-if="i === 3 && index === 2" src="@/assets/img/no3.png" alt="" class="img3" />
          </template>
          <template v-else>
            <CustomToolTip
              :value="
                (item[key] ? item[key] : '') + (chartData.unitList[key] != 'orderNum' ? chartData.unitList[key] : '')
              "
              :chart-data="chartData"
              :data-index="index"
            />
          </template>
        </div>
      </div>
    </main>
  </div>
</template>

<script>
import CustomToolTip from '../tooltip/custom-tooltip.vue'

export default {
  name: 'Table',
  components: { CustomToolTip },
  props: {
    isFilter: {
      type: Boolean,
      default: false
    },
    showarrowIcon: {
      type: Boolean,
      default: true
    },
    scroll: {
      type: Boolean,
      default: false
    },
    isLoading: {
      type: Boolean,
      default: () => false
    },
    showIcon: {
      type: Boolean,
      default: false
    },
    chartData: {
      type: Object,
      default: () => ({})
    },
    hjState: {
      type: Boolean,
      default: () => false
    },
    czState: {
      type: Boolean,
      default: () => false
    },
    pageHeight: { type: Boolean, default: false }
  },
  watch: {
    chartData(newVal) {
      newVal && this.dealData() //newVal存在的话执行dataChild函数
    }
  },
  created() {
    this.dealData()
  },
  methods: {
    filterData(e) {
      this.$emit('filterData', e)
    },
    //这里处理序号没数据的情况，后台拿不到值，先判断是否是orderNum，是就用index值，需要+1
    dealData() {
      var orderNumKey
      if (this.chartData.column) {
        this.chartData.column.map((i) => {
          if (this.chartData.unitList[i] === 'orderNum') {
            orderNumKey = i
          }
        })
        if (orderNumKey)
          this.chartData.data.map((item, i) => {
            if (item[orderNumKey] === null) {
              item[orderNumKey] = i + 1
            }
          })
      }
    }
  }
}
</script>

<style lang="scss">
.table-wrapper {
  position: relative;
  width: 100%;
  max-height: 1260px;
  overflow: auto;

  .table__header {
    position: sticky;
    top: 0;
    left: 0;
    width: 100%;

    .table__row {
      font-weight: 600;
      background-color: hsla(208, 100%, 50%, 1);
      color: hsla(0, 0%, 100%, 1);
    }
  }
  .loading-icon {
    height: 300px;
    width: 100%;
    background: url('@/assets/img/loading.png') no-repeat center center;
    background-size: 100px 100px;
    animation: rotate 2s linear infinite;
  }
  @keyframes rotate {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  .table__body {
    margin-top: 14px;

    .img1 {
      width: 78px;
      height: 78px;
    }

    .img2 {
      width: 40px;
      height: 52px;
    }

    .img3 {
      width: 34px;
      height: 46px;
    }
  }

  .circleNum {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;

    div {
      width: 36px;
      height: 36px;
      border-radius: 50%;
      background-color: #0089ff;
      color: #fff;
      line-height: 36px;
      text-align: center;
    }
  }

  .table__row {
    width: 100%;
    min-height: 60px;
    background-color: hsla(213, 100%, 98%, 1);
    color: hsla(208, 100%, 37%, 1);
    border-radius: 5000px;
    display: flex;
    align-items: center;
    font-size: 14px;
    line-height: 36px;
    padding: 0 16px;

    > * {
      // min-width: 100px;
      text-align: center;
      line-height: 1.3;
      overflow: hidden;
      // text-overflow: ellipsis;
      // /* white-space: nowrap; */
      // display: -webkit-box;
      // -webkit-box-orient: vertical;
      // -webkit-line-clamp: 2;

      &:not(:last-of-type) {
        margin-right: 4px;
      }
    }

    > .order-class {
      width: 100px !important;
      // flex: 0;
    }

    &:not(:last-of-type) {
      margin-bottom: 14px;
    }
  }
  .scroll {
    height: 195px;
    overflow-y: auto;
  }
}
</style>
