<template>
  <div ref="chartRef" class="echarts-container pie"></div>
</template>
<script>
import { ref, reactive, watchEffect } from 'vue'
import { useECharts } from '@/composables/useECharts'
import { isEmpty } from 'lodash'
import { colors2 } from '@/utils/constant'

export default {
  name: 'Ring',
  props: {
    chartData: {
      type: Object,
      default: () => ({})
    }
  },
  setup(props) {
    const chartRef = ref(null)
    const { setOptions } = useECharts(chartRef)
    let unit = ''
    let text = ''

    const option = reactive({
      title: {
        text: '',
        left: 'center',
        top: '39%',
        textStyle: {
          fontSize: '16px',
          fontWeight: 'normal'
        }
      },
      grid: {
        left: '5%',
        bottom: '0',
        right: '5%',
        containLabel: true
      },
      tooltip: {
        trigger: 'item',
        // 将 tooltip 框限制在图表的区域内
        confine: 'true',
        extraCssText: 'z-index: 9;',
        formatter: function (params) {
          // console.log(params, 'params')
          // console.log(params.data, 'params.data.unit')
          return params.name + '    ' + params.value + unit
        }
      },
      legend: {
        type: 'scroll',
        left: 'center',
        top: 'bottom',
        data: [],
        icon: 'circle',
        itemWidth: 12,
        itemHeight: 12,
        textStyle: {
          color: '#666666',
          fontSize: '14px'
        }
      },
      series: [
        {
          type: 'pie',
          top: '-10%',
          radius: ['32%', '50%'],
          center: ['50%', '50%'],
          color: colors2,
          label: {
            alignTo: 'edge',
            formatter: '{name|{b}}\n{percentage|{d}}{unit|%}',
            minMargin: 5,
            edgeDistance: 10,
            lineHeight: 15,
            rich: {
              name: {
                fontFamily: 'PingFangSC, PingFang SC',
                fontWeight: '600',
                fontSize: '14px',
                color: '#666666',
                textAlign: 'left',
                fontStyle: 'normal',
                textTransform: 'none'
              },
              percentage: {
                fontFamily: 'PingFangSC, PingFang SC',
                fontWeight: '600',
                fontSize: '14px',
                color: '#3B93FB',
                textAlign: 'left',
                fontStyle: 'normal',
                textTransform: 'none'
              },
              unit: {
                fontFamily: 'PingFangSC, PingFang SC',
                fontWeight: '600',
                fontSize: '14px',
                color: '#666666',
                textAlign: 'left',
                fontStyle: 'normal',
                textTransform: 'none'
              }
            }
          },
          labelLine: {
            length: 15,
            length2: 10,
            maxSurfaceAngle: 80
          },
          data: [],
          minAngle: 10,
          avoidLabelOverlap: true,
          hoverOffset: 15
        }
      ]
    })

    watchEffect(() => {
      !isEmpty(props.chartData) && initCharts()
    })

    function initCharts() {
      let seriesData = props.chartData.data

      let legends = props.chartData.data.map((item) => {
        return item.name
      })
      option.legend.data = legends
      option.series[0].data = seriesData
      // 饼图中间的标题
      if (props.chartData.columnName.name) {
        text = props.chartData.columnName.name
        if (text.length > 4) {
          text = `${text.slice(0, 4)}\n${text.slice(4, text.length)}`
        }
        option.title.text = text
      }

      const unitList = props.chartData.unitList

      for (const key in unitList) {
        if (key.startsWith('value') && unitList[key]) {
          unit = unitList[key]
          break
        }
      }
      setOptions(option)
    }

    return { chartRef }
  }
}
</script>
