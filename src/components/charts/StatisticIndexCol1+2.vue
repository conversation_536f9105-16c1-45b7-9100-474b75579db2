<template>
  <div class="statistic-index2">
    <div v-for="item in chartData.data?.slice(0, 1)" :key="item.name" class="statistic-index1__item">
      <div class="statistic-index1__title">{{ item.name }}</div>
      <div class="statistic-index1__data">
        <div class="text-data__data-value">{{ item.value }}</div>
        <div class="text-data__data-unit">{{ item.unit }}</div>
      </div>
      <!-- 增长/降低趋势（可选） -->
      <div v-if="item.UP_CODE" class="statistic-index1__ratio">
        <span :class="item.UP_CODE > 0 ? 't1' : 't2'">{{ item.UP_CODE }}%</span>
        <img v-if="item.UP_CODE > 0" src="../../assets/img/echartsImg/ratio-up-icon.png" alt="" />
        <img v-else src="../../assets/img/echartsImg/ratio-down-icon.png" alt="" />
      </div>
      <!-- 判断是否有增长/降低趋势值 更改右侧边框height -->
      <div
        class="statistic-index1-border"
        style="position: absolute"
        :style="{ height: item.UP_CODE ? '70px' : '40px' }"
      ></div>
      <van-divider class="statistic-index1-divider" dashed></van-divider>
    </div>
    <div v-for="item in chartData.data?.slice(1)" :key="item.name" class="statistic-index2__item">
      <div class="statistic-index2__title">{{ item.name }}</div>
      <div class="statistic-index2__data">
        <div class="text-data__data-value">{{ item.value }}</div>
        <div class="text-data__data-unit">{{ item.unit }}</div>
      </div>
      <!-- 增长/降低趋势（可选） -->
      <div v-if="item.UP_CODE" class="statistic-index2__ratio">
        <span :class="item.UP_CODE > 0 ? 't1' : 't2'">{{ item.UP_CODE }}%</span>
        <img v-if="item.UP_CODE > 0" src="../../assets/img/echartsImg/ratio-up-icon.png" alt="" />
        <img v-else src="../../assets/img/echartsImg/ratio-down-icon.png" alt="" />
      </div>
      <!-- 判断是否有增长/降低趋势值 更改右侧边框height -->
      <div
        class="statistic-index2-border"
        style="position: absolute"
        :style="{ height: item.UP_CODE ? '70px' : '40px' }"
      ></div>
      <van-divider class="statistic-index2-divider" dashed></van-divider>
    </div>
  </div>
</template>

<script>
import chart from './mixins/chart'
export default {
  name: 'StatisticIndexCol12',
  mixins: [chart],
  props: {
    chartData: {
      type: Object,
      default: () => {
        return {
          data: []
        }
      }
    }
  },
  mounted() {},
  methods: {
    init(chartData) {
      this.itemList = chartData.dataList?.[0]?.itemList
    }
  }
}
</script>

<style lang="scss" scoped>
.statistic-index2 {
  display: flex;
  flex-wrap: wrap;
}
.statistic-index1__item {
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-items: center;
  align-items: center;

  // 奇数item右侧有竖杠分隔
  &:nth-child(odd) {
    .statistic-index1-border {
      border-right: 0.5px dashed rgba(153, 153, 153, 0.3);
      margin-top: 7%;
      width: 42%;
    }
  }

  // 指标名称
  .statistic-index1__title {
    font-size: 28px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #333333;
    line-height: 100px;
  }
  // 指标数值
  .statistic-index1__data {
    display: flex;
    // 数值
    .text-data__data-value {
      font-size: 48px;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
      color: #3b93fb;
      line-height: 48px;
    }
    // 单位
    .text-data__data-unit {
      font-size: 28px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #666666;
      line-height: 28px;
      margin-top: 12px;
    }
  }
  // 增长降低趋势
  .statistic-index1__ratio {
    height: 54px;
    // 上下左右居中
    display: flex;
    // flex-direction: column;
    justify-items: center;
    align-items: center;
    span {
      margin-right: 10px;
      line-height: 54px;
    }
    img {
      width: 20px;
      height: 10px;
    }
    .t1 {
      font-size: 24px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #ff3f6a;
    }
    .t2 {
      font-size: 24px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #72ddb2;
    }
  }
  // Divider 分割线
  .statistic-index1-divider {
    width: 100%;
    margin: 20px 0 2px 0;
  }
}
.statistic-index2__item {
  width: 50%;
  display: flex;
  flex-direction: column;
  justify-items: center;
  align-items: center;

  // 奇数item右侧有竖杠分隔
  &:nth-child(even) {
    .statistic-index2-border {
      border-right: 0.5px dashed rgba(153, 153, 153, 0.3);
      margin-top: 7%;
      width: 42%;
    }
  }

  // 指标名称
  .statistic-index2__title {
    font-size: 28px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #333333;
    line-height: 100px;
  }
  // 指标数值
  .statistic-index2__data {
    display: flex;
    // 数值
    .text-data__data-value {
      font-size: 48px;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
      color: #3b93fb;
      line-height: 48px;
    }
    // 单位
    .text-data__data-unit {
      font-size: 28px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #666666;
      line-height: 28px;
      margin-top: 12px;
    }
  }
  // 增长降低趋势
  .statistic-index2__ratio {
    height: 54px;
    // 上下左右居中
    display: flex;
    // flex-direction: column;
    justify-items: center;
    align-items: center;
    span {
      margin-right: 10px;
      line-height: 54px;
    }
    img {
      width: 20px;
      height: 10px;
    }
    .t1 {
      font-size: 24px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #ff3f6a;
    }
    .t2 {
      font-size: 24px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #72ddb2;
    }
  }
  // Divider 分割线
  .statistic-index2-divider {
    width: 100%;
    margin: 20px 0 2px 0;
  }
}
</style>
