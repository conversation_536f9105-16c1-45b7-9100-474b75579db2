<template>
  <div ref="chartRef" class="echarts-container pie" :style="{ height: height }"></div>
</template>
<script>
import { ref, reactive, watchEffect } from 'vue'
import { useECharts } from '@/composables/useECharts'
import { isEmpty } from 'lodash'

export default {
  name: 'RingUsedInST',
  props: {
    chartData: {
      type: Object,
      default: () => ({})
    },
    title: {
      type: Object,
      default: () => ({})
    },
    titleNum: {
      type: [String, Number],
      default: ''
    },
    showLabel: {
      type: Boolean,
      default: false
    },
    height: {
      type: String,
      default: '287px'
    }
  },
  setup(props) {
    const tmp = props.title.content.split('\n')
    let title
    let title2 = ''
    if (props.title) {
      let add = String.fromCharCode('a'.charCodeAt(0) - 1)
      title = tmp.map((item) => {
        add = String.fromCharCode(add.charCodeAt(0) + 1)
        return `{${add}|${item}}`
      })
      title.forEach((element) => {
        title2 += `${element}\n`
      })
      if (props.titleNum) {
        add = String.fromCharCode(add.charCodeAt(0) + 1)
        title2 += `{${add}|${props.titleNum}}`
      }
    }
    const chartRef = ref(null)
    const { setOptions } = useECharts(chartRef)
    const option = reactive({
      title: {
        text: title2,
        left: 'center',
        top: '38%',
        textStyle: {
          rich: {
            a: props.title.style ? props.title.style[0] : {},
            b: props.title.style ? props.title.style[1] : {}
          }
        }
      },
      grid: {
        left: '5%',
        bottom: '0',
        right: '5%',
        containLabel: true
      },
      tooltip: {
        trigger: 'item',
        // 将 tooltip 框限制在图表的区域内
        confine: 'true',
        valueFormatter: (value) => `${value}%`,
        backgroundColor: 'rgba(0,0,0,0.6)',
        textStyle: {
          color: 'white'
        }
        // extraCssText: 'z-index: 9;',
        // formatter: function (params) {
        //   // console.log(params, 'params')
        //   // console.log(params.data, 'params.data.unit')
        //   return params.name + '    ' + params.value + (unit)
        // }
      },
      legend: {
        show: false,
        // type: 'scroll',
        left: 'center',
        top: 'bottom',
        data: [],
        icon: 'circle',
        itemWidth: 12,
        itemHeight: 12,
        textStyle: {
          color: '#666666',
          fontSize: '14px'
        }
      },
      series: [
        {
          type: 'pie',
          top: '-10%',
          radius: ['40%', '60%'],
          center: ['50%', '50%'],
          color: ['#75a1fb', '#71deb0'],
          label: {
            show: props.showLabel,
            alignTo: 'edge',
            formatter: '{name|{b}}\n{percentage|{c}}{unit|%}',
            minMargin: 5,
            edgeDistance: 10,
            lineHeight: 15,
            rich: {
              name: {
                fontFamily: 'PingFangSC, PingFang SC',
                fontWeight: '600',
                fontSize: '14px',
                color: '#666666',
                textAlign: 'left',
                fontStyle: 'normal',
                textTransform: 'none'
              },
              percentage: {
                fontFamily: 'PingFangSC, PingFang SC',
                fontWeight: '600',
                fontSize: '14px',
                color: '#3B93FB',
                textAlign: 'left',
                fontStyle: 'normal',
                textTransform: 'none'
              },
              unit: {
                fontFamily: 'PingFangSC, PingFang SC',
                fontWeight: '600',
                fontSize: '14px',
                color: '#666666',
                textAlign: 'left',
                fontStyle: 'normal',
                textTransform: 'none'
              }
            }
          },
          labelLine: {
            length: 15,
            length2: 10,
            maxSurfaceAngle: 80
          },
          data: [],
          minAngle: 10,
          avoidLabelOverlap: true,
          hoverOffset: 15
        }
      ]
    })

    watchEffect(() => {
      !isEmpty(props.chartData) && initCharts()
    })

    function initCharts() {
      let seriesData = props.chartData.data
      let legends = props.chartData.data.map((item) => {
        return item.name
      })
      option.legend.data = legends
      option.series[0].data = seriesData

      // const unitList = props.chartData.unitList

      // for (const key in unitList) {
      //   if (key.startsWith('value') && unitList[key]) {
      //     unit = unitList[key]
      //     break
      //   }
      // }
      setOptions(option)
    }

    return { chartRef }
  }
}
</script>
