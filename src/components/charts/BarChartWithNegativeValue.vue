<template>
  <div ref="chartRef" class="echarts-container" />
</template>

<script>
import { ref, reactive, watchEffect } from 'vue'
import { useECharts } from '@/composables/useECharts'
import { omit, isEmpty } from 'lodash'
// import { colors } from '@/utils/constant'

export default {
  name: 'BarChartWithNegativeValue',
  props: {
    chartData: {
      type: Object,
      default: () => ({})
    }
  },
  setup(props) {
    const chartRef = ref(null)
    const { setOptions } = useECharts(chartRef)
    const option = reactive({
      grid: [
        {
          left: 20,
          width: '40%'
        },
        {
          left: '60%',
          width: '35%'
        }
      ],
      tooltip: {},
      legend: {
        show: true
      },
      xAxis: [
        {
          type: 'value',
          inverse: true,
          splitLine: { show: true },
          axisLabel: {
            show: true
            // rotate: -45
          }
        },
        {
          type: 'value',
          inverse: false,
          gridIndex: 1,
          splitLine: { show: true },
          axisLabel: {
            show: true
            // rotate: 45
          }
        }
      ],
      yAxis: [
        {
          position: 'right',
          data: [],
          type: 'category',
          axisLabel: {
            show: true,
            margin: 30,
            textStyle: {
              color: '#000',
              align: 'center'
            }
          },
          axisLine: { show: true },
          axisTick: { show: false },
          inverse: true
        },
        {
          type: 'category',
          data: [],
          axisLabel: { color: 'transparent' },
          axisLine: { show: true },
          axisTick: { show: false },
          inverse: true,
          gridIndex: 1
        }
      ],
      series: []
    })

    watchEffect(() => {
      !isEmpty(props.chartData) && initCharts()
    })

    function initCharts() {
      const colorList = ['rgba(91, 143, 249, 0.85)', 'rgba(247, 199, 57, 1)']
      // x 轴
      let xAxisData = props.chartData.data.map((item) => {
        return item.name
      })
      option.yAxis[0].data = xAxisData
      option.yAxis[1].data = xAxisData
      // series
      let seriesData = []
      const valuesObj = omit(props.chartData.columnName, 'name')

      for (var i in Object.entries(valuesObj)) {
        const [seriesKey, seriesName] = Object.entries(valuesObj)[i]
        let obj = { name: seriesName, type: 'bar' }
        obj.data = props.chartData.data.map((item) => {
          return item[seriesKey]
        })
        obj.itemStyle = {
          color: colorList[i]
        }
        obj.yAxisIndex = i
        obj.xAxisIndex = i
        seriesData.push(obj)

        var maxValue = 0

        obj.data.forEach((item) => {
          if (item > maxValue) {
            maxValue = item
          }
        })

        if (maxValue > 999) {
          option.xAxis[i].axisLabel.rotate = i % 2 == 0 ? -45 : 45
        }
      }
      option.series = seriesData
      setOptions(option)
    }

    return { chartRef }
  }
}
</script>
