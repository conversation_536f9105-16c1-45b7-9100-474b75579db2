<template>
  <div class="text-single-line">
    <div v-for="item in chartData.data" :key="item.name" class="single-line__item">
      <div class="single-line__content">
        <div class="single-line__name">{{ item.name }}</div>
        <div class="single-line__data">
          <div class="single-line__data-value">{{ item.value }}</div>
          <div class="single-line__data-unit">{{ item.unit }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import chart from './mixins/chart'
export default {
  name: 'StatisticIndexCol23',
  mixins: [chart],
  props: {
    chartData: {
      type: Object,
      default: () => {}
    }
  },
  mounted() {},
  methods: {
    init(chartData) {
      this.itemList = chartData.dataList?.[0]?.itemList
    }
  }
}
</script>

<style lang="scss" scoped>
.text-single-line {
  width: 100%;
  padding: 0 10px;

  .single-line__item {
    margin-top: 20px;
    margin-bottom: 10px;
    .single-line__content {
      display: flex;
      justify-content: space-between;
      width: 100%;
      line-height: 60px;
      .single-line__name {
        font-size: 32px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 500;
        color: #333333;
        margin-left: 10px;
      }
      .single-line__data {
        display: flex;
        .single-line__data-value {
          font-size: 48px;
          font-family: PingFangSC-Semibold, PingFang SC;
          font-weight: 600;
          color: #3b93fb;
        }
        .single-line__data-unit {
          font-size: 28px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #666666;
          margin: 0 10px;
        }
      }
    }
  }
}
</style>
