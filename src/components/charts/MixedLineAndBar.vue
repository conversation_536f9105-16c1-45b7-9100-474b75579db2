<template>
  <div ref="chartRef" class="echarts-container" />
</template>

<script>
import { ref, reactive, watchEffect } from 'vue'
import { useECharts } from '@/composables/useECharts'
import { omit, isEmpty } from 'lodash'
import { colors } from '@/utils/constant'

export default {
  name: 'MixedLineAndBar',
  props: {
    chartData: {
      type: Object,
      default: () => ({})
    }
  },
  setup(props) {
    const chartRef = ref(null)
    const { setOptions } = useECharts(chartRef)
    const option = reactive({
      color: colors,
      grid: {
        top: '22%',
        bottom: '5',
        left: '5%',
        right: '5%',
        containLabel: true
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        },
        // 将 tooltip 框限制在图表的区域内
        confine: 'true',
        extraCssText: 'z-index: 9;'
      },
      legend: {
        type: 'scroll',
        // data: [],
        top: '0',
        left: '0',
        icon: 'rect',
        itemGap: 10,
        itemWidth: 14,
        itemHeight: 4,
        textStyle: {
          fontSize: 14,
          color: '#8C8C8C',
          padding: [0, 0, 0, 4]
        }
      },
      // dataZoom: [
      //   {
      //     type: 'inside',
      //     start: 0,
      //     end: 11, // 数据窗口范围的结束百分比
      //     minValueSpan: 10, // 在类目轴上可以设置为 n 表示至少显示 n 个类目。
      //     zoomLock: true
      //   }
      // ],
      xAxis: {
        type: 'category',
        data: [],
        axisLabel: {
          color: '#666666',
          rotate: -45,
          width: 60,
          overflow: 'truncate'
        },
        axisTick: {
          show: false
        }
      },
      yAxis: [
        {
          type: 'value',
          axisLabel: {
            color: '#666666'
          },
          nameGap: 20,
          nameTextStyle: {
            color: 'hsla(0, 0%, 40%, 1)',
            fontSize: 12
            // align: 'right'
          }
        },
        {
          type: 'value',
          position: 'right',
          axisLabel: {
            color: '#666666'
          },
          nameGap: 20,
          nameTextStyle: {
            color: 'hsla(0, 0%, 40%, 1)',
            fontSize: 12
            // align: 'right'
          }
        }
      ],
      series: []
    })

    watchEffect(() => {
      !isEmpty(props.chartData) && initCharts()
    })

    function initCharts() {
      // 设置 y 轴单位
      const unitList = Object.entries(props.chartData.unitList)
      const unit1 = unitList.find((item) => item[0].startsWith('bar'))[1]
      if (unit1) {
        option.yAxis[0].name = `单位：${unit1}`
      }
      option.yAxis[1].name = `单位：${unitList.find((item) => item[0].startsWith('line'))[1]}`
      const nameUnit = unitList.find((item) => item[0].startsWith('name'))[1]
      // x 轴，如果有单位，需要显示
      let xAxisData = props.chartData.data.map((item) => {
        return item.name + nameUnit
      })
      option.xAxis.data = xAxisData
      // series
      let seriesData = []
      const valuesObj = omit(props.chartData.columnName, 'name')
      for (const [seriesKey, seriesName] of Object.entries(valuesObj)) {
        let obj = { name: seriesName }
        if (seriesKey.startsWith('bar')) {
          obj.type = 'bar'
          obj.barMaxWidth = 30
          obj.yAxisIndex = 0
        } else {
          obj.type = 'line'
          obj.yAxisIndex = 1
        }
        obj.data = props.chartData.data.map((item) => {
          return item[seriesKey]
        })
        // 柱状图使用左侧 y 轴
        const seriesUnit = props.chartData.unitList[seriesKey]
        obj.tooltip = {
          valueFormatter: (value) => `${value ? value : '-'}${seriesUnit}`
        }

        seriesData.push(obj)
      }
      option.series = seriesData

      setOptions(option)
    }

    return { chartRef }
  }
}
</script>
