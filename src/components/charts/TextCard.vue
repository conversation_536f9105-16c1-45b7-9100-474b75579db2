<template>
  <div class="container">
    <div v-if="data" class="SdCardPerInhabitant" style="padding-bottom: 16px" :style="backgroundStyle">
      <div class="cardTitle">{{ data.title }}</div>
      <div class="time">
        <div class="timeLeft" :style="timeLeftBackgroundStyle"></div>
        <div class="timeTitle" :style="timeBackgroundStyle">{{ data.time }}</div>
        <!-- <div class="timeRight" :style="timeRightBackgroundStyle"></div> -->
      </div>
      <div class="data">
        <div class="value">
          <span class="valueNum DIN-Regular">{{ data.num1 }}</span
          ><span class="text-1.15rem">{{ data.unit1 }}</span>
        </div>
      </div>
      <div
        style="display: flex; margin-top: 8px; padding-left: 20px"
        :style="{ marginBottom: data.bottom && data.bottom.length > 0 ? '18px' : '0px' }"
      >
        <div>
          {{ data.text }}
          <img
            src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAwCAYAAABwrHhvAAAAAXNSR0IArs4c6QAAAa9JREFUWEftlUlOw0AQRV9ZciwxrZC4AGsQJBs4AMMdgEtAOATDJRLuEOAAsGEQrLkAEismyVhyoRAnIuCh3Q279jb9q55/rNeCw6OEO/24kHRsx4htUInmIb0d5IMlIX6wmWUFoDRDuL8EmtnSa1hYEa6TuhCWAOERsPtj2bGQ7P07gBKtQ9r7+uvHH4VgU4jP6kDUakCZnIOPO2CuYMkjNBaFt0dTCGMARQWiU9C18uFyDvGGIGoCUQMgakN6YDIUgn0hPjQ5awSgTLQguQBCk6FAAuGq8H5Vdb4SQJmdhucb0PmqYeO/ywPMLAtPL2U5A4BGF3Sr3vLhaTkRPratAZSwH7bWbLZ4R0i6RRCFDXxT7ZTd249Sr2WqzgXIVNv/6FqOy4fxK1hYzVN1AUCual1ZclX9C6BEta4AuaoeAzBQrSvEL1WPADLV9kDXXbdUqPoM4s2hqr8BRHuQGunTHTBoC3H/Sh9cqRaqdWUYqVrsVevKkKlaaXRAS3Xpuqo4L93Ku+BnWAlL73khqTWz1uHB9+IBfAO+Ad+Ab8A34BvwDfgGfAO+Ad/A3zbwCWzxxPhbaNMkAAAAAElFTkSuQmCC"
            alt="icon"
            class="rate_icon"
          /><span class="previousMonth" style="color: rgb(255, 0, 5)">5.6</span
          ><span style="color: rgb(255, 0, 5)">%</span>
        </div>
        <div style="height: 15px; margin-top: 12px; margin-left: 12px">
          <span class="ranking">{{ data.rank }}</span>
        </div>
      </div>
      <div
        v-if="data.bottom && data.bottom.length > 0"
        class="region"
        style="display: flex; flex-wrap: wrap; width: 100%"
      >
        <div
          v-for="(item, index) in data.bottom"
          :key="index"
          style="flex-basis: 48%; display: flex; align-items: baseline"
        >
          <div>{{ item.value > 0 ? '高于' : item.value < 0 ? '低于' : '与' }}</div>
          <div class="gDPContrast">
            <span class="gDPText">{{ item.name }}</span>
          </div>
          <span
            class="bfdText"
            :style="{
              color: item.value == 0 ? 'rgb(255, 0, 5)' : item.value > 0 ? 'rgb(255, 0, 5)' : 'rgb(0, 255, 127)'
            }"
            ><span class="textValue">{{ item.value == 0 ? '持平' : Math.abs(item.value) }}</span
            ><span class="textUnit">{{ item.unit }}</span></span
          >
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import defaultIcon from '@/assets/img/smallcard/textcard-bg1.png'
import purpleLeftTag from '@/assets/img/smallcard/tag-purple-left.png'
import purpleCenterTag from '@/assets/img/smallcard/tag-purple-center.png'
import purpleRightTag from '@/assets/img/smallcard/tag-purple-right.png'
import yellowLeftTag from '@/assets/img/smallcard/tag-yellow-left.png'
import yellowCenterTag from '@/assets/img/smallcard/tag-yellow-center.png'
import yellowRightTag from '@/assets/img/smallcard/tag-yellow-right.png'
import greenLeftTag from '@/assets/img/smallcard/tag-green-left.png'
import greenCenterTag from '@/assets/img/smallcard/tag-green-center.png'
import greenRightTag from '@/assets/img/smallcard/tag-green-right.png'

export default {
  name: 'TextCard',
  props: {
    data: {
      type: Object,
      default: {}
    },
    icon: {
      type: String,
      default: () => defaultIcon
    },
    tagType: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      //紫色 黄色 绿色
      tagList: [
        {
          left: purpleLeftTag,
          center: purpleCenterTag,
          right: purpleRightTag
        },
        {
          left: yellowLeftTag,
          center: yellowCenterTag,
          right: yellowRightTag
        },
        {
          left: greenLeftTag,
          center: greenCenterTag,
          right: greenRightTag
        }
      ]
    }
  },
  computed: {
    backgroundStyle() {
      // 如果 icon 为 null，使用默认图片
      const imageUrl = this.icon || defaultIcon
      return {
        backgroundImage: `url(${imageUrl})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center'
      }
    },
    timeBackgroundStyle() {
      // 如果 icon 为 null，使用默认图片
      const imageUrl = this.tagList[this.tagType].center
      return {
        backgroundImage: `url("${imageUrl}")`,
        backgroundSize: 'cover',
        backgroundPosition: 'center'
      }
    },
    timeLeftBackgroundStyle() {
      // 如果 icon 为 null，使用默认图片
      const imageUrl = this.tagList[this.tagType].left
      return {
        backgroundImage: `url("${imageUrl}")`,
        backgroundSize: '16px 34px',
        backgroundPosition: 'center'
      }
    },
    timeRightBackgroundStyle() {
      // 如果 icon 为 null，使用默认图片
      const imageUrl = this.tagList[this.tagType].right
      return {
        backgroundImage: `url("${imageUrl}")`,
        backgroundSize: 'cover',
        backgroundPosition: 'center'
      }
    }
  },
  methods: {}
}
</script>
<style scoped lang="scss">
.container {
  height: auto;
  overflow: visible; /* 确保内容可以溢出 */
}
.SdCardPerInhabitant {
  position: relative;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;
  border-radius: 20px;
  color: #fff;
  overflow: visible; /* 确保内容可以溢出 */
}
.SdCardPerInhabitant .cardTitle {
  font-size: 36px;
  font-weight: 400;
  padding-top: 40px;
  font-family: PingFang, serif;
  padding-left: 40px;
  margin-bottom: 24px;
}
.time {
  position: absolute;
  height: 68px;
  margin: 30px 0;
  display: flex;
  right: 0px;
  top: 16px;
}
.timeTitle {
  position: relative;
  color: #fff;
  text-align: right;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;
  height: 68px;
  line-height: 60px;
  font-size: 24px;
}
.timeLeft {
  background-size: 100% 100%;
  width: 32px;
  height: 68px;
}
.timeRight {
  background-size: 100% 100%;
  width: 20px;
  height: 68px;
  display: flex;
}

.SdCardPerInhabitant .data {
  display: flex;
  align-items: flex-end;
  gap: 40px;
  padding-left: 40px;
}
.SdCardPerInhabitant .value {
  font-family: PingFang, Helvetica Neue, Helvetica, Arial, sans-serif;
  font-size: 24px;
}
.SdCardPerInhabitant .value .valueNum {
  font-size: 60px;
  line-height: 0.8;
  font-family: DIN-Regular, Arial, sans-serif;
}
.SdCardPerInhabitant .rate_icon {
  height: 24px;
  width: 16px;
  margin-left: 4px;
}
.SdCardPerInhabitant .previousMonth {
  font-size: 60px;
  padding-left: 10px;
  font-family: DIN-Regular, serif;
  font-weight: 700;
}
.SdCardPerInhabitant .ranking {
  font-family: PingFang, serif;
}
.region {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  padding-left: 40px;
  grid-row-gap: 28px;
  row-gap: 28px;
  font-family: PingFang, serif;
}
._gDPContrast_1tvt8_477,
.gDPContrast {
  width: 70px;
  height: 32px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 24px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  margin-left: 4px;
  text-align: center;
}
.gDPText {
  padding: 0 4px;
  font-size: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.textValue {
  font-size: 30px;
  font-family: DIN-Bold, Arial, sans-serif;
}
.textUnit {
  font-family: PingFang-SC-Bold, serif;
}
@media screen and (min-width: 600px) {
  .SdCardPerInhabitant .cardTitle {
    font-size: 20px;
    font-weight: 400;
    padding-top: 20px;
    font-family: PingFang, serif;
    padding-left: 20px;
    margin-bottom: 12px;
  }
  .time {
    height: 34px;
    margin: 15px 0;
    top: 8px;
  }
  .timeTitle {
    height: 34px;
    line-height: 30px;
    font-size: 12px;
  }
  .timeLeft {
    background-size: 100% 100%;
    width: 16px;
    height: 34px;
  }
  .timeRight {
    width: 20px;
    height: 34px;
  }

  .SdCardPerInhabitant .data {
    gap: 20px;
    padding-left: 20px;
  }
  .SdCardPerInhabitant .value {
    font-size: 12px;
  }
  .SdCardPerInhabitant .value .valueNum {
    font-size: 30px;
  }
  .SdCardPerInhabitant .rate_icon {
    height: 12px;
    width: 8px;
    margin-left: 4px;
  }
  .SdCardPerInhabitant .previousMonth {
    font-size: 30px;
    padding-left: 5px;
  }
  .region {
    padding-left: 20px;
    grid-row-gap: 14px;
    row-gap: 14px;
  }
  ._gDPContrast_1tvt8_477,
  .gDPContrast {
    width: 35px;
    height: 16px;
    border-radius: 12px;
  }
  .gDPText {
    padding: 0 2px;
    font-size: 12px;
  }
  .textValue {
    font-size: 15px;
  }
}
</style>
