<template>
  <div class="statistic-index2">
    <div v-for="item in chartData.data" :key="item.name" class="statistic-index2__item">
      <div class="statistic-index2__title">
        <div>{{ item.name }}</div>
      </div>
      <div class="text-data__data-value">{{ item.value }}</div>
      <div class="text-data__data-unit">{{ item.unit }}</div>
      <!-- 增长/降低趋势（可选） -->
      <div v-if="item.RATIO_CODE" class="statistic-index2__ratio">
        <span class="t1">{{ item.RATIO_CODE }}%</span>
      </div>
      <!-- 判断是否有完成率 更改右侧边框height -->
      <div
        class="statistic-index2-border"
        style="position: absolute"
        :style="{ height: item.UP_CODE ? '70px' : '40px' }"
      ></div>
      <!-- <van-divider class="statistic-index2-divider" dashed></van-divider> -->
    </div>
  </div>
</template>

<script>
// 该组件作为textData组件的3列n行文本公共组件的补充
// 单位 另起一行
import chart from './mixins/chart'
export default {
  name: 'StatisticIndexCol3',
  mixins: [chart],
  props: {
    chartData: {
      type: Object,
      default: () => {}
    }
  },
  methods: {
    init(chartData) {
      this.itemList = chartData.dataList?.[0]?.itemList
    }
  }
}
</script>

<style lang="scss" scoped>
.statistic-index2 {
  display: flex;
  flex-wrap: wrap;
}
.statistic-index2__item {
  width: 33.3%;
  display: flex;
  flex-direction: column;
  justify-items: center;
  align-items: center;
  margin-bottom: 15px;

  // 奇数item右侧有竖杠分隔
  &:nth-child(1),
  &:nth-child(2),
  &:nth-child(4),
  &:nth-child(5) {
    .statistic-index2-border {
      border-right: 0.5px dashed rgba(153, 153, 153, 0.3);
      margin-top: 7%;
      width: 28%;
    }
  }

  // 指标名称
  .statistic-index2__title {
    font-size: 28px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #333333;
    // line-height: 100px;
    width: 90%;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    div {
      text-align: center;
    }
  }
  // 指标数值
  // 数值
  .text-data__data-value {
    font-size: 48px;
    font-family: PingFangSC-Semibold, PingFang SC;
    font-weight: 600;
    color: #3b93fb;
    line-height: 55px;
  }
  // 单位
  .text-data__data-unit {
    font-size: 28px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #666666;
    line-height: 56px;
  }

  // 增长降低趋势
  .statistic-index2__ratio {
    // 上下左右居中
    display: flex;
    // flex-direction: column;
    justify-items: center;
    align-items: center;
    .t1 {
      line-height: 30px;
      font-size: 28px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 500;
      color: #3b93fb;
    }
  }
  // Divider 分割线
  .statistic-index2-divider {
    width: 100%;
    margin: 20px 0 2px 0;
  }
}
</style>
