<template>
  <div class="header relative">
    <img
      class="bg1"
      src="./img/left-bg.png"
      alt=""
      :style="{
        width: middleWidth ? '190px' : longWidth ? '230px' : longLongWidth ? '320px' : longerWidth ? '290px' : '155px'
      }"
    />
    <img
      class="bg2"
      src="data:image/png;base64,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"
      alt=""
    /><img class="bg bg-end" src="./img/right-bg.png" alt="" /><img class="_header-icon_1blh3_99" :src="icon" alt="" />
    <div class="flexEmploymentCard">
      {{ title }}
    </div>
  </div>
</template>

<script>
export default {
  name: 'CardTop',
  props: {
    title: {
      type: String,
      default: ''
    },
    icon: {
      type: String,
      default: ''
    },
    middleWidth: {
      type: Boolean,
      default: false
    },
    longWidth: {
      type: Boolean,
      default: false
    },
    longLongWidth: {
      type: Boolean,
      default: false
    },
    longerWidth: {
      type: Boolean,
      default: false
    },
    styles: Object
  },
  methods: {}
}
</script>
<style scoped lang="scss">
.header {
  display: flex;
  height: 56px;
  background: #f7f9fc;
  border-bottom: 12px solid #ffffff;
  position: relative;
  border-radius: 10px 10px 0 0;
  overflow: hidden;
}
.relative {
  position: relative;
}
.bg1,
.bg2 {
  position: relative;
  z-index: 2;
  height: 44px;
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
.bg-end {
  height: 44px;
  position: absolute;
  right: 0;
  top: 0;
}
._header-icon_1blh3_99 {
  z-index: 3;
  position: absolute;
  top: 14px;
  left: 14px;
  width: 28px;
  height: 28px;
}
.flexEmploymentCard {
  z-index: 3;
  position: absolute;
  left: 46px;
  font-size: 20px;
  line-height: 56px;
  letter-spacing: 0;
  font-weight: 800;
  font-family: JiangChengXieHei, serif;
  color: rgb(52, 168, 255);
}

@media (max-width: 600px) {
  .flexEmploymentCard {
    font-size: 18px;
  }
}
</style>
