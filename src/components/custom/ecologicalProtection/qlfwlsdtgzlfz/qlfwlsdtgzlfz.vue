<template>
  <div class="productbox">
    <div class="title">全⼒服务绿⾊低碳⾼质量发展</div>
    <div class="contBox">
      <div class="text">
        <img src="../img/dot.png" class="icon" alt="" />
        建立重大项目服务清单，建立污染物指标保障清单，实行碳排放指标省级收储调剂，千方百计做好环境要素保障。
      </div>
      <div class="text">
        <img src="../img/dot.png" class="icon" alt="" />
        建设省级生态环境科技成果转化和生态环保产业发展综合服务平台（二期），推进生态环保产业高质量发展“311”工程，打造绿色增长新引擎。
      </div>
      <div class="text">
        <img src="../img/dot.png" class="icon" alt="" />
        健全完善省环保金融项目库，力争：
      </div>

      <div class="f_cont">
        <div v-for="(item, i) in datalist" :key="i" class="f_contitem">
          <div class="row1">
            <div class="num">{{ item.value }}</div>
            <div class="unit">{{ item.unit }}</div>
          </div>
          <div class="row2">
            {{ item.name }}
          </div>
        </div>
      </div>
      <div class="text">
        <img src="../img/dot.png" class="icon" alt="" />
        在<span>城市、产业园区、企业</span>开展试点，探索打造减污降碳协同创新山东模式。
      </div>
      <div class="f_cont">
        <div v-for="(item, i) in datalist2" :key="i" class="f_contitem">
          <div class="row1">
            <div class="num">{{ item.value }}</div>
            <div class="unit">{{ item.unit }}</div>
          </div>
          <div class="row2">
            {{ item.name }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      datalist: [
        {
          name: '新⼊库项⽬',
          value: '100',
          unit: '个以上'
        },
        {
          name: '新增银⾏授信',
          value: '100',
          unit: '亿元以上'
        },
        {
          name: '新增EOD项目',
          value: '10',
          unit: '个以上'
        }
      ],
      datalist2: [
        {
          name: '开展试点城市',
          value: '10',
          unit: '个'
        },
        {
          name: '开展试点产业园区',
          value: '20',
          unit: '个'
        },
        {
          name: '开展试点企业',
          value: '60',
          unit: '个'
        }
      ],
    }
  }
}
</script>
<style lang="scss" scoped>
.productbox {
  position: relative;
  margin-top: 50px;
  background-color: white;
  box-shadow: 0px 5px 15px rgba(222, 222, 222, 1);
  border-radius: 16px;
  .title {
    z-index: 2;
    top: -30px;
    width: 602px;
    height: 85px;
    background: url('../img/titltbg.png') center / 100% 100% no-repeat;
    text-align: center;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 32px;
    color: #ffffff;
    line-height: 90px;
    text-shadow: 0px 1px 11px rgba(49, 107, 179, 0.48);
    font-style: normal;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
  }
  .contBox {
    margin-top: 24px;
    padding: 85px 36px 36px;
    position: relative;
    .text {
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 28px;
      color: #212121;
      line-height: 48px;
      text-align: left;
      font-style: normal;
      margin-bottom: 30px;
      .book {
        width: 220px;
        height: 244px;
        float: right;
        transform: translateY(34px);
      }
      .deco {
        width: 220px;
        height: 244px;
        float: right;
      }
      .icon {
        width: 28px;
        height: 20px;
        margin-right: 10px;
      }

      span {
        color: #3096ef;
      }
    }
    .f_cont {
      display: grid;
      gap: 24px;
      margin-top: 23px;
      grid-template-columns: repeat(2, 1fr);
      .f_contitem {
        width: 100%;
        display: flex;
        height: 227px;
        flex-direction: column;
        justify-content: space-evenly;
        align-items: center;
        background: rgb(232, 243, 255);
        border-radius: 15px;
        .row1 {
          display: flex;
          align-items: baseline;
          .num {
            font-family: D-DIN, D-DIN;
            font-weight: bold;
            font-size: 68px;
            color: #ff7b05;
            line-height: 74px;
            text-align: right;
            font-style: normal;
          }
          .unit {
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 24px;
            color: #666666;
            line-height: 33px;
            text-align: left;
            font-style: normal;
            margin: 0 0 0 5px;
          }
        }
        .row2 {
          width: 278px;
          //   height: 63px;
          padding: 10px 0px;
          background: linear-gradient(to right, rgb(69, 183, 253) 0%, rgb(91, 220, 254) 100%);
          font-family: PingFangSC, PingFang SC;
          border-radius: 32px;
          font-weight: 600;
          font-size: 28px;
          color: #ffffff;
          line-height: 40px;
          text-align: center;
          font-style: normal;
          display: flex;
          align-items: center;
          justify-content: center;
          span {
            display: inline-block;
            background: #ffffff;
            border-radius: 16px;
            opacity: 0.8;
            padding: 2px 10px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            margin-right: 5px;
            font-size: 24px;
            color: #666666;
            line-height: 33px;
            text-align: left;
            font-style: normal;
          }
        }
      }
      .f_contitem:last-of-type {
        grid-column: span 2;
        display: flex;
        align-items: center;
        height: 118px;
        padding: 0px 30px 0 15px;
        justify-content: space-between;
        flex-direction: row;
        .row1 {
          order: 2;
        }
        .row2 {
          order: 1;
        }
      }
    }
  }
}
</style>
