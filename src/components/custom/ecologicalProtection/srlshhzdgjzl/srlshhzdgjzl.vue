<template>
  <div class="productbox">
    <div class="title">深⼊落实⻩河重⼤国家战略</div>
    <div class="contBox">
      <div class="btmcont">
        <div class="btmcontval">
          <div class="yf">印发实施</div>
          <div class="file">《⼭东省⻩河流域⽣态环境问题⼤排查⼤整治攻坚⾏动⽅案》</div>
          <div class="name">
            <div>· 围绕<span class="span1">9个</span>方面开展全面排查整治</div>
            <div>· 推动黄河流域国控断面水质<span class="span2">100%优良</span></div>
            <div>· 入海断面总氮浓度<span class="span2">好于</span>邻省入鲁断面</div>
            <div>· 黄河流域水环境质量<span class="span2">优于</span>全省平均水平</div>
          </div>
        </div>
      </div>
      <div class="text">
        <img src="../img/dot.png" class="icon" alt="" />
        开展黄河流域生态环境保护2024年<span>“十大行动”</span>。
      </div>
      <div class="text">
        <img src="../img/dot.png" class="icon" alt="" />
        开展黄河三角洲生物多样性本底调查，发布了<span>《黄河三角洲的生物多样性保护》</span>白皮书，启动第二批9个生物多样性养护观测站建设。
      </div>
      <div class="text">
        <img src="../img/dot.png" class="icon" alt="" />
        开展黄河流域重点区域历史遗留矿山环境污染状况详查。
      </div>
      <div class="text">
        <img src="../img/dot.png" class="icon" alt="" />
        签订第二轮黄河流域（豫鲁段）横向生态保护补偿协议，完成2023年度资金核算兑付。
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.productbox {
  position: relative;
  margin-top: 50px;
  background-color: white;
  box-shadow: 0px 5px 15px rgba(222, 222, 222, 1);
  border-radius: 16px;
  .title {
    z-index: 2;
    top: -30px;
    width: 602px;
    height: 85px;
    background: url('../img/titltbg.png') center / 100% 100% no-repeat;
    text-align: center;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 32px;
    color: #ffffff;
    line-height: 90px;
    text-shadow: 0px 1px 11px rgba(49, 107, 179, 0.48);
    font-style: normal;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
  }
  .contBox {
    margin-top: 24px;
    padding: 85px 30px 36px;
    position: relative;
    .btmcont {
      width: 100%;
      height: 652px;
      padding: 86px 70px;
      background: url('../img/valbg.png') center/100% 100% no-repeat;
      .btmcontval {
        display: flex;
        align-items: center;
        flex-direction: column;
        .yf {
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 30px;
          color: #212121;
          line-height: 42px;
          text-align: center;
          font-style: normal;
          display: flex;
          align-items: center;
        }
        .yf::after,
        .yf::before {
          content: '';
          display: block;
          width: 31px;
          height: 29px;
          background: url('../img/star.png') center/ 100% 100% no-repeat;
          vertical-align: middle;
        }

        .file {
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 30px;
          color: #3096ef;
          line-height: 42px;
          text-align: center;
          font-style: normal;
        }
        .name {
          margin-top: 36px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 28px;
          color: #212121;
          line-height: 60px;
          text-align: left;
          font-style: normal;
          .span1 {
            color: #48a1ee;
          }
          .span2 {
            color: #de252d;
          }
        }
      }
    }
    .text {
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 28px;
      color: #212121;
      line-height: 48px;
      text-align: left;
      font-style: normal;
      margin-bottom: 30px;
      .book {
        width: 220px;
        height: 244px;
        float: right;
        transform: translateY(34px);
      }
      .deco {
        width: 220px;
        height: 244px;
        float: right;
      }
      .icon {
        width: 28px;
        height: 20px;
        margin-right: 10px;
      }

      span {
        color: #3096ef;
      }
    }
  }
}
</style>
