<template>
  <div class="productbox">
    <div class="title">全⼒服务经济⾼质量发展</div>
    <div class="contBox">
      <div class="text">
        <img src="../img/dot.png" class="icon" alt="" />
        建立2024年部省市三级环评审批管理台账，对鲁油鲁炼等重点项目环评逐个建立专班服务推进。
      </div>
      <div class="baseCard">
        <div class="title2">年度重点项⽬</div>
        <div class="valbox">
          <div class="ltval">
            <div class="num">1.5</div>
            <div class="unit">万个</div>
          </div>
          <div class="ctval">
            <div class="desc"><span>环评完成率</span></div>
            <!-- <img src="@/assets/img/arrow-up.png" alt="" /> -->
            <div class="value">78.9<span>%</span></div>
          </div>
        </div>
      </div>
      <div class="text">
        <img src="../img/dot.png" class="icon" alt="" />
        优化重大项目污染物排放总量指标管理，以煤电行业为试点，打破市域调剂限制，实行全省统筹调配。
      </div>
      <div class="text">
        <img src="../img/dot.png" class="icon" alt="" />
        完成全省2023年度生态环境分区管控动态更新。
      </div>
      <div class="text">
        <img src="../img/dot.png" class="icon" alt="" />
        开展服务型执法，综合运用正面清单、信用分级等方式，全面推进差异化监管，对守法企业<span>“无事不扰”</span>。
      </div>
      <div class="baseCard baseCard2">
        <div class="time">2024年以来</div>
        <div class="valbox">
          <div class="ctval">
            <div class="desc"><span>⾮现场执法检查占⽐</span></div>
          </div>
          <div class="ltval">
            <div class="num">41.2</div>
            <div class="unit">%</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { getAction } from '@/api/manage'
import barChart from '../common/barChart2.vue'
import sourceData from '../common/sourceData.vue'

export default {
  components: {
    barChart,
    sourceData
  },
  props: {
    sourceData: {
      type: Object,
      default: () => {}
    },
    category: {
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      chartData: [],
      stdNum: [],
      ratio: [],
      totalRatio: [],
      name3: [],
      year: [],
      isPad: window.matchMedia('(min-width: 600px)').matches,
      textData: {}
    }
  },
  created() {
    this.loadData()
  },
  methods: {
    async loadData() {
      await getAction(
        import.meta.env.VITE_APP_SITUATION_BASE_URL + '/topic/data/listAllBySql/ydd_sthj_shj_ylstbl_jbqk'
      ).then((res) => {
        if (res.success) {
          this.textData = {
            name: res.result[0].field_name,
            value: res.result[0].field_value,
            unit: '%'
          }
        }
      })
      await getAction(
        import.meta.env.VITE_APP_SITUATION_BASE_URL + '/topic/data/listAllBySql/ydd_sthj_shj_ylstbl_month'
      ).then((res) => {
        if (res.success) {
          this.chartData = res.result
          this.stdNum = []
          this.ratio = []
          this.name3 = []
          this.year = []
          res.result.forEach((item) => {
            this.stdNum.push(item.std_num)
            this.ratio.push(item.ratio)
            this.name3.push(item.name3)
            this.year.push(item.year)
          })

          this.totalRatio = this.ratio.map((item, index) => item - (this.stdNum[index] || 0))
          // console.log(this.name3);
          // console.log(res.result);
          // console.log('++++++');
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.productbox {
  position: relative;
  margin-top: 50px;
  background-color: white;
  box-shadow: 0px 5px 15px rgba(222, 222, 222, 1);
  border-radius: 16px;
  .title {
    z-index: 2;
    top: -30px;
    width: 602px;
    height: 85px;
    background: url('../img/titltbg.png') center / 100% 100% no-repeat;
    text-align: center;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 32px;
    color: #ffffff;
    line-height: 90px;
    text-shadow: 0px 1px 11px rgba(49, 107, 179, 0.48);
    font-style: normal;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
  }
  .contBox {
    margin-top: 24px;
    padding: 85px 36px 36px;
    position: relative;

    .baseCard {
      width: 100%;
      height: 180px;
      background: linear-gradient(to right, #4e93ff 0%, #5ec0ff 100%);
      //   background: url('../img/baseCardbg.png') center/100% 100% no-repeat;
      border-radius: 10px;
      padding: 35px 30px;
      position: relative;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      margin-bottom: 24px;
      .time {
        background: url('../img/timebg.png') center/100% 100% no-repeat;
        width: 140px;
        height: 75px;
        position: absolute;
        right: -4px;
        top: -8px;
        z-index: 3;
        text-align: center;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 24px;
        color: #ffffff;
        line-height: 65px;
        font-style: normal;
      }
      .title2 {
        font-family: PingFangSC, PingFang SC;
        font-weight: 600;
        font-size: 28px;
        color: #ffffff;
        line-height: 20px;
        text-align: left;
        font-style: normal;
      }
      .valbox {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .ltval {
          display: flex;
          align-items: baseline;
          .num {
            font-family: D-DIN, D-DIN;
            font-weight: bold;
            font-size: 68px;
            color: #ffffff;
            line-height: 27px;
            text-align: justify;
            font-style: normal;
          }
          .unit {
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 24px;
            color: #ffffff;
            line-height: 27px;
            text-align: left;
            font-style: normal;
          }
        }

        .rtval {
          display: flex;
          align-items: baseline;
          margin-top: 10px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 24px;
          color: #ffffff;
          line-height: 27px;
          text-align: left;
          font-style: normal;
          span {
            display: inline-block;
            width: 50px;
            height: 48px;
            background: url('../img/numbg.png') center/100% 100% no-repeat;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 32px;
            color: #ffffff;
            line-height: 45px;
            text-align: center;
            font-style: normal;
          }
        }
        .ctval {
          display: flex;
          align-items: baseline;
          margin-right: 10px;
          .desc {
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 28px;
            color: #ffffff;
            line-height: 40px;
            text-align: left;
            font-style: normal;
            span {
              color: white;
            }
          }

          .value {
            font-family: D-DIN, D-DIN;
            font-weight: bold;
            font-size: 48px;
            color: white;
            line-height: 27px;
            text-align: justify;
            font-style: normal;
            span {
              color: white;
              font-size: 24px;
              font-weight: 500;
            }
          }
        }
      }
    }
    .baseCard2 {
      padding: 88px 30px 30px;
      height: unset;
    }
    .text {
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 28px;
      color: #212121;
      line-height: 48px;
      text-align: left;
      font-style: normal;
      margin-bottom: 40px;
      .book {
        width: 220px;
        height: 244px;
        float: right;
        transform: translateY(34px);
      }
      .deco {
        width: 220px;
        height: 244px;
        float: right;
      }
      .icon {
        width: 28px;
        height: 20px;
        margin-right: 10px;
      }

      span {
        color: #3096ef;
      }
    }
  }
}
</style>
