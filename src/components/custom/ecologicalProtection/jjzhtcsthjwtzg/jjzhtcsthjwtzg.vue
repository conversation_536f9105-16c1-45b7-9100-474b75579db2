<template>
  <div class="productbox">
    <div class="title">坚决抓好突出⽣态环境问题整改</div>
    <div class="contBox">
      <div class="basecard">
        <div class="title2">两轮次中央⽣态环境保护督察指出问题</div>
        <div class="line">
          <div class="current"></div>
        </div>
        <div class="val">
          <div class="lt">
            <div class="dot"></div>
            <div class="name">问题总数</div>
            <div class="num">164</div>
            <div class="unit">项</div>
          </div>
          <div class="rt">
            <div class="dot"></div>
            <div class="name">已整改完成</div>
            <div class="num">137</div>
            <div class="unit">项</div>
          </div>
        </div>
        <!-- <div class="desc">
          对青岛、淄博等10市开展了第三轮省级生态环境保护督察
        </div> -->
      </div>
      <div class="basecard basecard2">
        <div class="title2">省级督察指出问题</div>
        <div class="line">
          <div class="current"></div>
        </div>
        <div class="val">
          <div class="lt">
            <div class="dot"></div>
            <div class="name">问题总数</div>
            <div class="num">3125</div>
            <div class="unit">项</div>
          </div>
          <div class="rt">
            <div class="dot"></div>
            <div class="name">已整改完成</div>
            <div class="num">2785</div>
            <div class="unit">项</div>
          </div>
        </div>
        <div class="desc">对青岛、淄博等<span>10市</span>开展了第三轮省级生态环境保护督察</div>
      </div>
      <div class="basecard basecard2">
        <div class="title2">前两批次⻩河警示⽚问题</div>
        <div class="line">
          <div class="current"></div>
        </div>
        <div class="val">
          <div class="lt">
            <div class="dot"></div>
            <div class="name">问题总数</div>
            <div class="num">3125</div>
            <div class="unit">项</div>
          </div>
          <div class="rt">
            <div class="dot"></div>
            <div class="name">已整改完成</div>
            <div class="num">2785</div>
            <div class="unit">项</div>
          </div>
        </div>
        <div class="desc">正在扎实推进2023年黄河警示片披露问题整改工作</div>
      </div>
      <div class="text">
        <img src="../img/dot.png" class="icon" alt="" />
        涉危废违法犯罪和在线监测弄虚作假“两打”工作获生态环境部主要负责同志批示肯定。
      </div>
      <div class="text">
        <img src="../img/dot.png" class="icon" alt="" />
        开展重型柴油货车OBD弄虚作假排查整治百日攻坚。
      </div>
      <div class="f_cont">
        <div v-for="(item, i) in datalist" :key="i" class="f_contitem">
          <div class="row1">
            <div class="num">{{ item.value }}</div>
            <div class="unit">{{ item.unit }}</div>
          </div>
          <div class="row2">
            {{ item.name }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      datalist: [
        {
          name: '发现并整改问题',
          value: '1210',
          unit: '个'
        },
        {
          name: '⽴案',
          value: '268',
          unit: '起'
        }
      ]
    }
  }
}
</script>
<style lang="scss" scoped>
.productbox {
  position: relative;
  margin-top: 50px;
  background-color: white;
  box-shadow: 0px 5px 15px rgba(222, 222, 222, 1);
  border-radius: 16px;
  .title {
    z-index: 2;
    top: -30px;
    width: 608px;
    height: 85px;
    background: url('../img/titltbg2.png') center / 100% 100% no-repeat;
    text-align: center;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 32px;
    color: #ffffff;
    line-height: 90px;
    text-shadow: 0px 1px 11px rgba(49, 107, 179, 0.48);
    font-style: normal;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
  }
  .contBox {
    margin-top: 24px;
    padding: 85px 36px 36px;
    position: relative;
    .text {
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 28px;
      color: #212121;
      line-height: 48px;
      text-align: left;
      font-style: normal;
      margin-bottom: 30px;
      .book {
        width: 220px;
        height: 244px;
        float: right;
        transform: translateY(34px);
      }
      .deco {
        width: 220px;
        height: 244px;
        float: right;
      }
      .icon {
        width: 28px;
        height: 20px;
        margin-right: 10px;
      }

      span {
        color: #3096ef;
      }
    }
    .basecard {
      position: relative;
      margin-bottom: 30px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      padding: 30px;
      border-radius: 16px;
      background: linear-gradient(#4890ff 0%, #5dbeff 100%);
      .title2 {
        font-family: PingFangSC, PingFang SC;
        font-weight: 600;
        font-size: 28px;
        color: #ffffff;
        line-height: 40px;
        text-align: left;
        font-style: normal;
      }
      .line {
        width: 100%;
        height: 20px;
        background: rgba(255, 255, 255, 0.5);
        border-radius: 10px;
        margin: 20px 0;
        .current {
          background: rgba(255, 255, 255, 0.5);
          border-radius: 10px;
          height: 20px;
          width: 70%;
          background-color: #ffe37c;
        }
      }
      .val {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .lt,
        .rt {
          display: flex;
          align-items: baseline;
          .dot {
            width: 12px;
            height: 20px;
            background: rgba(255, 255, 255, 0.5);
            border-radius: 10px;
            margin-right: 5px;
          }
          .name {
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 24px;
            color: #ffffff;
            line-height: 33px;
            text-align: left;
            font-style: normal;
          }
          .num {
            font-family: D-DIN, D-DIN;
            font-weight: bold;
            font-size: 48px;
            color: #ffffff;
            line-height: 52px;
            text-align: right;
            font-style: normal;
          }
          .unit {
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 24px;
            color: #ffffff;
            line-height: 33px;
            text-align: left;
            font-style: normal;
          }
        }
        .rt {
          .dot {
            background-color: #ffe37c;
          }
        }
      }
      .desc {
        position: absolute;
        bottom: 0;
        left: 0;
        border-radius: 0 0 16px 16px;
        width: 100%;
        height: 62px;
        background: #3780ff;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 24px;
        color: #ffffff;
        line-height: 62px;
        text-align: center;
        font-style: normal;
        span {
          color: #ffe37c;
        }
      }
    }
    .basecard2 {
      padding: 30px 30px 72px 30px;
    }
    .f_cont {
      display: grid;
      gap: 24px;
      margin-top: 23px;
      grid-template-columns: repeat(2, 1fr);
      .f_contitem {
        width: 100%;
        display: flex;
        height: 227px;
        flex-direction: column;
        justify-content: space-evenly;
        align-items: center;
        background: rgb(232, 243, 255);
        border-radius: 15px;
        .row1 {
          display: flex;
          align-items: baseline;
          .num {
            font-family: D-DIN, D-DIN;
            font-weight: bold;
            font-size: 68px;
            color: #ff7b05;
            line-height: 74px;
            text-align: right;
            font-style: normal;
          }
          .unit {
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 24px;
            color: #666666;
            line-height: 33px;
            text-align: left;
            font-style: normal;
            margin: 0 0 0 5px;
          }
        }
        .row2 {
          width: 278px;
          //   height: 63px;
          padding: 10px 0px;
          background: linear-gradient(to right, rgb(69, 183, 253) 0%, rgb(91, 220, 254) 100%);
          font-family: PingFangSC, PingFang SC;
          border-radius: 32px;
          font-weight: 600;
          font-size: 28px;
          color: #ffffff;
          line-height: 40px;
          text-align: center;
          font-style: normal;
          display: flex;
          align-items: center;
          justify-content: center;
          span {
            display: inline-block;
            background: #ffffff;
            border-radius: 16px;
            opacity: 0.8;
            padding: 2px 10px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            margin-right: 5px;
            font-size: 24px;
            color: #666666;
            line-height: 33px;
            text-align: left;
            font-style: normal;
          }
        }
      }
    }
  }
}
</style>
