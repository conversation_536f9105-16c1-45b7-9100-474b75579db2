<template>
  <div class="productbox">
    <div class="title">切实保障⽣态环境安全</div>
    <div class="contBox">
      <div class="text">
        <img src="../img/dot.png" class="icon" alt="" />
        组织实施生态环境系统安全生产治本攻坚三年行动，开展新一轮<span>危险废物“万企全员”警示教育、“黄河清废”</span>等行动。
      </div>
      <div class="text">
        <img src="../img/dot.png" class="icon" alt="" />
        举办全省生态环境应急实兵演练暨生态环境监管技术比武竞赛。
      </div>
      <div class="text">
        <img src="../img/dot.png" class="icon" alt="" />
        完善黄河流域横向生态补偿制度，深化实施县际流域横向生态补偿机制。
      </div>
      <div class="text">
        <img src="../img/dot.png" class="icon" alt="" />
        全力筹建山东省海洋生态环境监测与应急处置中心。
      </div>
      <div class="text">
        <img src="../img/dot.png" class="icon" alt="" />
        切实做好日本核污染水排海风险防范。
      </div>

      <div class="imglist">
        <div v-for="(item, i) in imglist" :key="i" class="imgitem">
          <img :src="item.img" alt="" />
          <div>{{ item.name }}</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import img1 from '../img/img10.png'
import img2 from '../img/img11.png'
export default {
  data() {
    return {
      imglist: [
        {
          img: img1,
          name: '危险废物“万企全员”警示教育'
        },
        {
          img: img2,
          name: '日本核污染水排海风险防范'
        }
      ]
    }
  }
}
</script>
<style lang="scss" scoped>
.productbox {
  position: relative;
  margin-top: 50px;
  background-color: white;
  box-shadow: 0px 5px 15px rgba(222, 222, 222, 1);
  border-radius: 16px;
  .title {
    z-index: 2;
    top: -30px;
    width: 498px;
    height: 85px;
    background: url('../img/titltbg2.png') center / 100% 100% no-repeat;
    text-align: center;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 32px;
    color: #ffffff;
    line-height: 90px;
    text-shadow: 0px 1px 11px rgba(49, 107, 179, 0.48);
    font-style: normal;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
  }
  .contBox {
    margin-top: 24px;
    padding: 85px 36px 36px;
    position: relative;
    .text {
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 28px;
      color: #212121;
      line-height: 48px;
      text-align: left;
      font-style: normal;
      margin-bottom: 30px;
      .book {
        width: 220px;
        height: 244px;
        float: right;
        transform: translateY(34px);
      }
      .deco {
        width: 220px;
        height: 244px;
        float: right;
      }
      .icon {
        width: 28px;
        height: 20px;
        margin-right: 10px;
      }

      span {
        color: #3096ef;
      }
    }

    .imglist {
      width: 100%;
      display: grid;
      gap: 12px;
      margin: 35px 0;
      grid-template-columns: repeat(2, 1fr);
      .imgitem {
        display: flex;
        flex-direction: column;
        align-items: center;
        img {
          width: 294px;
          height: 196px;
        }
        div {
          padding: 0 10px;
          margin-top: -50px;
          width: 100%;
          height: 83px;
          display: flex;
          align-items: center;
          justify-content: center;
          text-align: center;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 22px;
          color: #ffffff;
          line-height: 28px;
          font-style: normal;
          background: url('../img/textbg2.png') center / 100% 100% no-repeat;
        }
      }
    }
  }
}
</style>
