<template>
  <div class="productbox">
    <div class="title">强⼒推进美丽⼭东建设</div>
    <div class="contBox">
      <div class="btmcont">
        <div class="btmcontval">
          <div class="yf">省委省政府出台了</div>
          <div class="file">《关于全⾯推进美丽⼭东建设的实施意⻅》</div>
          <div class="name">
            明确了美丽山东建设的目标任务和实施 路径，并将有关任务纳入省委、省政府“1+3+N”重点工作督查，强力推进落实。
          </div>
        </div>
      </div>
      <div class="line"></div>

      <div class="title2">⽣态⽂明示范创建成果显著</div>
      <div class="baseCard">
        <div class="title3"><span>山东</span>已建成国家级生态文明建设示范区</div>
        <div class="valbox2">
          <div class="ltval">
            <div class="num">32</div>
            <div class="unit">个</div>
          </div>
          <div class="rtval">全国排名第<span>5</span></div>
        </div>
      </div>
      <div class="baseCard baseCard2">
        <div class="title3"><span>山东</span>“绿水青山就是金山银山”实践创新基地</div>
        <div class="valbox2">
          <div class="ltval">
            <div class="num">11</div>
            <div class="unit">个</div>
          </div>
          <div class="rtval">全国排名第<span>2</span></div>
        </div>
      </div>
      <div class="baseCard baseCard3">
        <div class="title3"><span>山东</span>美丽河湖</div>
        <div class="valbox2">
          <div class="ltval">
            <div class="num">4</div>
            <div class="unit">个</div>
          </div>
          <div class="rtval">全国排名第<span>2</span></div>
        </div>
      </div>
      <div class="baseCard baseCard4">
        <div class="title3"><span>山东</span>美丽海湾</div>
        <div class="valbox2">
          <div class="ltval">
            <div class="num">4</div>
            <div class="unit">个</div>
          </div>
          <div class="rtval">全国排名第<span>1</span></div>
        </div>
      </div>
      <div class="mapchart"><mapchart /></div>
      <div class="line"></div>
      <div class="ctval">
        <div class="name">新命名、建设省级⽣态⼯业园区</div>
        <div class="num">12<span>个</span></div>
      </div>
    </div>
    <img v-if="isPad" src="../img/deco.png" alt="" class="deco">
  </div>
</template>
<script>
import mapchart from '../common/mapchart.vue'

export default {
  components: {
    mapchart
  },
  data() {
    return {
      isPad: window.matchMedia('(min-width: 600px)').matches
    }
  }
}
</script>
<style lang="scss" scoped>
.productbox {
  position: relative;
  margin-top: 50px;
  background-color: white;
  box-shadow: 0px 5px 15px rgba(222, 222, 222, 1);
  border-radius: 16px;
  .title {
    z-index: 2;
    top: -30px;
    width: 602px;
    height: 85px;
    background: url('../img/titltbg.png') center / 100% 100% no-repeat;
    text-align: center;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 32px;
    color: #ffffff;
    line-height: 90px;
    text-shadow: 0px 1px 11px rgba(49, 107, 179, 0.48);
    font-style: normal;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
  }
  .contBox {
    margin-top: 24px;
    padding: 65px 36px 36px;
    position: relative;
    .btmcont {
      width: 100%;
      height: 564px;
      padding: 86px 70px;
      background: url('../img/valbg.png') center/100% 100% no-repeat;
      .btmcontval {
        display: flex;
        align-items: center;
        flex-direction: column;
        .yf {
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 30px;
          color: #212121;
          line-height: 42px;
          text-align: center;
          font-style: normal;
          display: flex;
          align-items: center;
        }
        .yf::after,
        .yf::before {
          content: '';
          display: block;
          width: 31px;
          height: 29px;
          background: url('../img/star.png') center/ 100% 100% no-repeat;
          vertical-align: middle;
        }

        .file {
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 30px;
          color: #3096ef;
          line-height: 42px;
          text-align: center;
          font-style: normal;
        }
        .name {
          margin-top: 36px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 30px;
          color: #212121;
          line-height: 42px;
          text-align: center;
          font-style: normal;
        }
      }
    }
    .line {
      width: 100%;
      height: 5px;
      margin: 20px 0;
      background: url('../img/line.png') center center/604px 100% no-repeat;
    }
    .title2 {
      font-family: PingFangSC, PingFang SC;
      font-weight: 600;
      font-size: 32px;
      color: #000000;
      line-height: 45px;
      text-align: center;
      font-style: normal;
      background: linear-gradient(270deg, #0284ff 0%, #1282ff 39%, #00bbff 100%);
      text-align: center;
      background-clip: text;
      color: transparent;
    }
    .baseCard {
      width: 100%;
      height: 200px;
      background: linear-gradient(to right, #4e93ff 0%, #5ec0ff 100%);
      //   background: url('../img/baseCardbg3.png') center/100% 100% no-repeat;
      border-radius: 10px;
      padding: 35px 30px;
      position: relative;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      margin: 24px 0;
      .title3 {
        white-space: nowrap;
        position: relative;
        font-family: PingFangSC, PingFang SC;
        font-weight: 600;
        font-size: 28px;
        color: #ffffff;
        line-height: 20px;
        text-align: left;
        font-style: normal;
        span {
          display: inline-block;
          padding: 0px 20px;
          height: 40px;
          line-height: 40px;
          border-radius: 0 20px 20px 0;
          font-family: PingFangSC, PingFang SC;
          font-weight: 600;
          font-size: 26px;
          color: #8e682f;
          background: #ffe7bc;
          text-align: left;
          font-style: normal;
          margin: 0 10px 0 -30px;
        }
      }
      .valbox2 {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .ltval {
          display: flex;
          align-items: baseline;
          .num {
            font-family: D-DIN, D-DIN;
            font-weight: bold;
            font-size: 68px;
            color: #ffffff;
            line-height: 27px;
            text-align: justify;
            font-style: normal;
          }
          .unit {
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 24px;
            color: #ffffff;
            line-height: 27px;
            text-align: left;
            font-style: normal;
          }
        }

        .rtval {
          display: flex;
          align-items: baseline;
          margin-top: 10px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 24px;
          color: #ffffff;
          line-height: 27px;
          text-align: left;
          font-style: normal;
          span {
            display: inline-block;
            width: 50px;
            height: 48px;
            background: url('../img/gold.png') center/100% 100% no-repeat;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 32px;
            color: #ffffff;
            line-height: 45px;
            text-align: center;
            font-style: normal;
          }
        }
        .ctval {
          display: flex;
          align-items: baseline;
          margin-right: 10px;
          .desc {
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 24px;
            color: #ffffff;
            line-height: 27px;
            text-align: left;
            font-style: normal;
          }
          img {
            width: 25px;
            height: 35px;
            margin: 0 5px;
          }
          .value {
            font-family: D-DIN, D-DIN;
            font-weight: bold;
            font-size: 48px;
            color: #de242c;
            line-height: 27px;
            text-align: justify;
            font-style: normal;
            span {
              font-size: 24px;
            }
          }
        }
      }
    }
    .baseCard3 {
      background: url('../img/baseCardbg3.png') center/100% 100% no-repeat;
    }
    .baseCard4 {
      background: url('../img/baseCardbg4.png') center/100% 100% no-repeat;
    }
    .mapchart {
      width: 100%;
      height: 480px;
    }
    .ctval {
      width: 100%;
      padding: 30px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      background: linear-gradient(45deg, #4e93ff 0%, #5ebfff 100%);
      border-radius: 16px;
      margin-top: 30px;
      .name {
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 28px;
        color: #ffffff;
        line-height: 40px;
        text-align: left;
        font-style: normal;
      }
      .num {
        font-family: D-DIN, D-DIN;
        font-weight: bold;
        font-size: 68px;
        color: #ffffff;
        line-height: 27px;
        text-align: right;
        font-style: normal;
        span {
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 24px;
          color: #ffffff;
          line-height: 16px;
          text-align: left;
          font-style: normal;
        }
      }
    }
  }
}
</style>
