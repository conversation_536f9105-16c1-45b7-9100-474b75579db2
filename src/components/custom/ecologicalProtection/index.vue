<template>
  <div class="developmentBox">
    <img src="./img/topimg.png" alt="" class="topimg" />
    <div class="contbox">
      <!-- PM2.5平均浓度 -->
      <PM_25 />
      <!-- 优良天数比例 -->
      <yltsbl />
      <!-- 地表水国控断面优良水体比例 -->
      <dbsgkdmylstbl />
    </div>
    <div class="topTitle">工作情况</div>
    <div class="contbox">
      <!-- 强⼒推进美丽⼭东建设 -->
      <qltjmlsdjs />
      <!-- 全⼒服务经济⾼质量发展 -->
      <qlfwjjgzlfz />
      <!-- 扎实推进环境污染治理 -->
      <zstjhjwrzl />
      <!-- 深⼊落实⻩河重⼤国家战略 -->
      <srlshhzdgjzl />
      <!-- 坚决抓好突出⽣态环境问题整改 -->
      <jjzhtcsthjwtzg />
      <!-- 坚决守牢⽣态环境安全底线 -->
      <jjslsthjaqdx />
    </div>
    <div class="topTitle">存在问题</div>
    <div class="contbox">
      <!-- 环境质量持续向好的基础脆弱 -->
      <hjzlcxxh />
      <!-- 水环境⽅⾯ -->
      <shjfm />
    </div>
    <div class="topTitle">下步打算</div>
    <div class="contbox">
      <!-- 积极推进美丽⼭东建设实践 -->
      <jjtjmlsdjssj />
      <!-- 全⼒服务绿⾊低碳⾼质量发展 -->
      <qlfwlsdtgzlfz />
      <!-- 着⼒推动环境质量稳中向好 -->
      <zltdhjzlwzxh />
      <!-- 聚⼒打造⻩河流域⽣态保护样板区 -->
      <jldzhhlystbhybq />
      <!-- 稳步推进碳达峰碳中和 -->
      <wbtjtdftzh />
      <!-- 扎实推进⽣态环境保护督察执法 -->
      <zstjsthjbhdczf />
      <!-- 切实保障⽣态环境安全 -->
      <qsbzsthjaq />
    </div>
  </div>
</template>

<script>
import { getAction } from '@/api/manage'
import PM_25 from './PM_25/PM_25.vue'
import yltsbl from './yltsbl/yltsbl.vue'
import dbsgkdmylstbl from './dbsgkdmylstbl/dbsgkdmylstbl.vue'
import qltjmlsdjs from './qltjmlsdjs/qltjmlsdjs.vue'
import zstjhjwrzl from './zstjhjwrzl/zstjhjwrzl.vue'
import srlshhzdgjzl from './srlshhzdgjzl/srlshhzdgjzl.vue'
import qlfwjjgzlfz from './qlfwjjgzlfz/qlfwjjgzlfz.vue'
import qsbzsthjaq from './qsbzsthjaq/qsbzsthjaq.vue'
import zstjsthjbhdczf from './zstjsthjbhdczf/zstjsthjbhdczf.vue'
import wbtjtdftzh from './wbtjtdftzh/wbtjtdftzh.vue'
import jldzhhlystbhybq from './jldzhhlystbhybq/jldzhhlystbhybq.vue'
import zltdhjzlwzxh from './zltdhjzlwzxh/zltdhjzlwzxh.vue'
import hjzlcxxh from './hjzlcxxh/hjzlcxxh.vue'
import shjfm from './shjfm/shjfm.vue'
import qlfwlsdtgzlfz from './qlfwlsdtgzlfz/qlfwlsdtgzlfz.vue'
import jjtjmlsdjssj from './jjtjmlsdjssj/jjtjmlsdjssj.vue'
import jjslsthjaqdx from './jjslsthjaqdx/jjslsthjaqdx.vue'
import jjzhtcsthjwtzg from './jjzhtcsthjwtzg/jjzhtcsthjwtzg.vue'

import { topicMixin } from '@/mixins/topicMixin'

export default {
  components: {
    PM_25,
    yltsbl,
    qltjmlsdjs,
    hjzlcxxh,
    zstjhjwrzl,
    shjfm,
    wbtjtdftzh,
    qsbzsthjaq,
    qlfwjjgzlfz,
    jjslsthjaqdx,
    zstjsthjbhdczf,
    srlshhzdgjzl,
    zltdhjzlwzxh,
    jjtjmlsdjssj,
    jldzhhlystbhybq,
    jjzhtcsthjwtzg,
    qlfwlsdtgzlfz,
    dbsgkdmylstbl
  },
  mixins: [topicMixin],

  data() {
    return {
      count_type: null,
      dataArr: {}
    }
  },
  mounted() {
    // this.loadData()
  },

  methods: {
    loadData() {
      getAction(import.meta.env.VITE_APP_SITUATION_BASE_URL + 'topic/data/listAllBySql/ydd_mzgz_jbqk_mkmc').then(
        (res) => {}
      )
    }
  }
}
</script>
<style lang="scss" scoped>
.developmentBox {
  background-color: #ddf4fe;
  padding-bottom: 50px;
  .topimg {
    width: 100%;
    height: 355px;
    margin-bottom: -80px;
  }
  .topTitle {
    width: 100%;
    height: 309px;
    background: url('./img/titlebg.png') center / 100% 100% no-repeat;
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    font-size: 36px;
    padding: 25px 0 0 35px;
    color: #ffffff;
    line-height: 50px;
    text-align: left;
    margin-bottom: -200px;
    font-style: normal;
    position: relative;
  }
  .contbox {
    padding: 0 24px;
  }
}
</style>
