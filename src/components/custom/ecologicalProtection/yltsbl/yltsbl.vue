<template>
  <div class="productbox">
    <div class="title">优良天数比例</div>
    <div class="contBox">
      <div class="baseCard">
        <img src="../img/baseCardbg2.png" alt="" class="baseCardbg2" />
        <div class="title2">1-11月优良天数比例</div>
        <div class="valbox">
          <div class="ltval">
            <div class="num">71.1</div>
            <div class="unit">%</div>
          </div>
          <div class="ctval">
            <div class="desc">同比</div>
            <img src="@/assets/img/arrow-up.png" alt="" />
            <div class="value">4.6<span>百分点</span></div>
          </div>
        </div>
      </div>
      <div class="btmBar">
        <barChart :chart-data="chartData" :chart-config="chartConfig" type="2" :show-data-zoom="false" />
      </div>
      <sourceData :source-data="sourceData" class="sourcedata" :dp-type="false" :category="category" />
    </div>
  </div>
</template>
<script>
import { getAction } from '@/api/manage'
import barChart from '../common/barChart.vue'
import sourceData from '../common/sourceData.vue'

export default {
  components: {
    barChart,
    sourceData
  },
  props: {
    sourceData: {
      type: Object,
      default: () => {}
    },
    category: {
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      chartData: [
        { year: '2024年1月', ratio: 45, growth_yoy: 0.68 },
        { year: '2024年1-2月', ratio: 25, growth_yoy: 1.23 },
        { year: '2024年1-3月', ratio: 30, growth_yoy: 1.33 },
        { year: '2024年1-4月', ratio: 30, growth_yoy: 3.44 },
        { year: '2024年1-5月', ratio: 45, growth_yoy: 2.17 }
      ],
      chartConfig: {
        yAxis0_name: '单位：%',
        yAxis1_name: '单位：%',
        tooltip_formatter: (e) => {
          return `${e[0].axisValue}:</br>${e[1].seriesName}: ${e[1].value}%</br>
          ${e[3].seriesName}: ${e[3].value}%
          `
        }
      },
      isPad: window.matchMedia('(min-width: 600px)').matches,
      textData: null
    }
  }
}
</script>

<style lang="scss" scoped>
.productbox {
  position: relative;
  margin-top: 50px;
  background-color: white;
  box-shadow: 0px 5px 15px rgba(222, 222, 222, 1);
  border-radius: 16px;
  .title {
    z-index: 2;
    top: -30px;
    width: 402px;
    height: 85px;
    background: url('../img/titltbg.png') center / 100% 100% no-repeat;
    text-align: center;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 32px;
    color: #ffffff;
    line-height: 90px;
    text-shadow: 0px 1px 11px rgba(49, 107, 179, 0.48);
    font-style: normal;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
  }
  .contBox {
    margin-top: 24px;
    padding: 85px 36px 36px;
    position: relative;
    .btmBar {
      width: 100%;
      height: 450px;
      margin-top: 30px;
    }
    .baseCard {
      width: 100%;
      height: 200px;
      background: linear-gradient(to right, #4e93ff 0%, #5ec0ff 100%);
      //   background: url('../img/baseCardbg.png') center/100% 100% no-repeat;
      border-radius: 10px;
      padding: 35px 30px;
      position: relative;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      margin-bottom: 24px;
      .baseCardbg2 {
        position: absolute;
        right: 0;
        bottom: 0;
        width: 270px;
      }
      .title2 {
        font-family: PingFangSC, PingFang SC;
        font-weight: 600;
        font-size: 28px;
        color: #ffffff;
        line-height: 20px;
        text-align: left;
        font-style: normal;
      }
      .valbox {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .ltval {
          display: flex;
          align-items: baseline;
          .num {
            font-family: D-DIN, D-DIN;
            font-weight: bold;
            font-size: 68px;
            color: #ffffff;
            line-height: 27px;
            text-align: justify;
            font-style: normal;
          }
          .unit {
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 24px;
            color: #ffffff;
            line-height: 27px;
            text-align: left;
            font-style: normal;
          }
        }

        .rtval {
          display: flex;
          align-items: baseline;
          margin-top: 10px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 24px;
          color: #ffffff;
          line-height: 27px;
          text-align: left;
          font-style: normal;
          span {
            display: inline-block;
            width: 50px;
            height: 48px;
            background: url('../img/numbg.png') center/100% 100% no-repeat;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 32px;
            color: #ffffff;
            line-height: 45px;
            text-align: center;
            font-style: normal;
          }
        }
        .ctval {
          display: flex;
          align-items: baseline;
          margin-right: 10px;
          .desc {
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 24px;
            color: #ffffff;
            line-height: 27px;
            text-align: left;
            font-style: normal;
          }
          img {
            width: 25px;
            height: 35px;
            margin: 0 5px;
          }
          .value {
            font-family: D-DIN, D-DIN;
            font-weight: bold;
            font-size: 48px;
            color: #de242c;
            line-height: 27px;
            text-align: justify;
            font-style: normal;
            span {
              color: white;
              font-size: 24px;
              font-weight: 500;
            }
          }
        }
      }
    }
  }
  .sourcedata {
    margin: 30px auto 0;
    width: 100%;
  }
}
</style>
