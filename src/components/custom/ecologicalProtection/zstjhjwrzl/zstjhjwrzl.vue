<template>
  <div class="productbox">
    <div class="title">扎实推进环境污染治理</div>
    <div class="contBox">
      <div class="text">
        <img src="../img/dot.png" class="icon" alt="" />
        梳理近年来全省大气颗粒物源解析成果，逐市形成PM2.5来源解析综合结果，抓住“大头”，精准施策。
      </div>
      <div class="text">
        <img src="../img/dot.png" class="icon" alt="" />
        压茬开展冬春季水质保障和汛期河湖水质超 标隐患排查整治专项行动，开展<span>全省化工园区水 污染整治专项检查、城市黑臭水体整治环保行动、美丽海湾建设“回头看”</span>等，着力<span>“保水质、增颜值”</span>。
      </div>
      <div class="imglist">
        <div v-for="(item, i) in imglist" :key="i" class="imgitem">
          <img :src="item.img" alt="" />
          <div>{{ item.name }}</div>
        </div>
      </div>
      <div class="text">
        <img src="../img/dot.png" class="icon" alt="" />
        强化土壤污染源头防控，更新优先监管地块清单，全省受污染耕地和重点建设用地安全利用得到有效保障。
      </div>
      <div class="btmcont">
        <div class="btmcontval">
          <div class="yf">出台了</div>
          <div class="file">《山东省农村生活污水处理设施运行维护管理办法》</div>
          <div class="name">保障农村生活污水处理设施长效运行。</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import img1 from '../img/img1.png'
import img2 from '../img/img2.png'
import img3 from '../img/img3.png'
export default {
  data() {
    return {
      imglist: [
        {
          img: img1,
          name: '化⼯园区⽔污染整治专项检查'
        },
        {
          img: img2,
          name: '城市⿊臭⽔体整治环保⾏动'
        },
        {
          img: img3,
          name: '美丽海湾建设“回头看”'
        }
      ]
    }
  }
}
</script>
<style lang="scss" scoped>
.productbox {
  position: relative;
  margin-top: 50px;
  background-color: white;
  box-shadow: 0px 5px 15px rgba(222, 222, 222, 1);
  border-radius: 16px;
  .title {
    z-index: 2;
    top: -30px;
    width: 602px;
    height: 85px;
    background: url('../img/titltbg.png') center / 100% 100% no-repeat;
    text-align: center;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 32px;
    color: #ffffff;
    line-height: 90px;
    text-shadow: 0px 1px 11px rgba(49, 107, 179, 0.48);
    font-style: normal;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
  }
  .contBox {
    margin-top: 24px;
    padding: 85px 30px 36px;
    position: relative;
    .text {
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 28px;
      color: #212121;
      line-height: 48px;
      text-align: left;
      font-style: normal;
      margin-bottom: 30px;
      .book {
        width: 220px;
        height: 244px;
        float: right;
        transform: translateY(34px);
      }
      .deco {
        width: 220px;
        height: 244px;
        float: right;
      }
      .icon {
        width: 28px;
        height: 20px;
        margin-right: 10px;
      }

      span {
        color: #3096ef;
      }
    }
    .btmcont {
      width: 100%;
      height: 460px;
      padding: 86px 70px;
      background: url('../img/valbg2.png') center/100% 100% no-repeat;
      .btmcontval {
        display: flex;
        align-items: center;
        flex-direction: column;
        .yf {
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 30px;
          color: #212121;
          line-height: 42px;
          text-align: center;
          font-style: normal;
          display: flex;
          align-items: center;
        }
        .yf::after,
        .yf::before {
          content: '';
          display: block;
          width: 31px;
          height: 29px;
          background: url('../img/star.png') center/ 100% 100% no-repeat;
          vertical-align: middle;
        }

        .file {
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 30px;
          color: #3096ef;
          line-height: 42px;
          text-align: center;
          font-style: normal;
        }
        .name {
          margin-top: 36px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 30px;
          color: #212121;
          line-height: 42px;
          text-align: center;
          font-style: normal;
        }
      }
    }
    .imglist {
      width: 100%;
      display: grid;
      gap: 12px;
      margin: 35px 0;
      grid-template-columns: repeat(2, 1fr);
      .imgitem {
        display: flex;
        flex-direction: column;
        align-items: center;
        img {
          width: 294px;
          height: 196px;
        }
        div {
          margin-top: -50px;
          width: 100%;
          height: 58px;
          display: flex;
          align-items: center;
          justify-content: center;
          text-align: center;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 22px;
          color: #ffffff;
          line-height: 28px;
          font-style: normal;
          background: url('../img/textbg.png') center / 100% 100% no-repeat;
        }
      }
      .imgitem:last-child {
        grid-column: span 2;
        img {
          width: 626px;
          height: 196px;
        }
        div {
          width: 102%;
        }
      }
    }
  }
}
</style>
