<template>
  <div class="productbox">
    <div class="title">聚⼒打造⻩河流域⽣态保护样板区</div>
    <div class="contBox">
      <div class="text">
        <img src="../img/dot.png" class="icon" alt="" />
        继续开展全省黄河流域生态环境问题大排查大整治攻坚行动。
      </div>
      <div class="text">
        <img src="../img/dot.png" class="icon" alt="" />
        聚力推进黄河流域生态环境保护2024年<span>“十大行动”</span>。
      </div>
      <div class="text">
        <img src="../img/dot.png" class="icon" alt="" />
        完善黄河流域横向生态补偿制度，深化实施县际流域横向生态补偿机制。
      </div>
      <div class="text">
        <img src="../img/dot.png" class="icon" alt="" />
        继续开展<span>“绿盾”</span>自然保护地强化监督。
      </div>
      <div class="text">
        <img src="../img/dot.png" class="icon" alt="" />
        建好<span>“智慧生态黄河”数字化监管平台</span>，推动生态环境监测网络向新污染物等领域拓展、向智能化方向提升。
      </div>

      <div class="imglist">
        <div v-for="(item, i) in imglist" :key="i" class="imgitem">
          <img :src="item.img" alt="" />
          <div>{{ item.name }}</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import img1 from '../img/img8.png'
import img2 from '../img/img9.png'
export default {
  data() {
    return {
      datalist: [
        {
          name: '国家级美丽海湾',
          value: '1-2',
          unit: '个'
        },
        {
          name: '国家级美丽河湖',
          value: '2-3',
          unit: '个'
        }
      ],
      imglist: [
        {
          img: img1,
          name: '黄河流域生态环境保护2024年“十大行动”'
        },
        {
          img: img2,
          name: '“智慧生态黄河”数字化监管平台'
        }
      ]
    }
  }
}
</script>
<style lang="scss" scoped>
.productbox {
  position: relative;
  margin-top: 50px;
  background-color: white;
  box-shadow: 0px 5px 15px rgba(222, 222, 222, 1);
  border-radius: 16px;
  .title {
    z-index: 2;
    top: -30px;
    width: 636px;
    height: 85px;
    background: url('../img/titltbg2.png') center / 100% 100% no-repeat;
    text-align: center;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 32px;
    color: #ffffff;
    line-height: 90px;
    text-shadow: 0px 1px 11px rgba(49, 107, 179, 0.48);
    font-style: normal;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
  }
  .contBox {
    margin-top: 24px;
    padding: 85px 36px 36px;
    position: relative;
    .text {
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 28px;
      color: #212121;
      line-height: 48px;
      text-align: left;
      font-style: normal;
      margin-bottom: 30px;
      .book {
        width: 220px;
        height: 244px;
        float: right;
        transform: translateY(34px);
      }
      .deco {
        width: 220px;
        height: 244px;
        float: right;
      }
      .icon {
        width: 28px;
        height: 20px;
        margin-right: 10px;
      }

      span {
        color: #3096ef;
      }
    }

    .imglist {
      width: 100%;
      display: grid;
      gap: 12px;
      margin: 35px 0;
      grid-template-columns: repeat(2, 1fr);
      .imgitem {
        display: flex;
        flex-direction: column;
        align-items: center;
        img {
          width: 294px;
          height: 196px;
        }
        div {
          padding: 0 10px;
          margin-top: -50px;
          width: 100%;
          height: 83px;
          display: flex;
          align-items: center;
          justify-content: center;
          text-align: center;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 22px;
          color: #ffffff;
          line-height: 28px;
          font-style: normal;
          background: url('../img/textbg2.png') center / 100% 100% no-repeat;
        }
      }
    }
  }
}
</style>
