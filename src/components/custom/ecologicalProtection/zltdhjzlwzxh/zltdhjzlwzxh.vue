<template>
  <div class="box">
    <div class="title">着⼒推动环境质量稳中向好</div>
    <div class="box_title box_title1">
      <img src="../img/titlebg4.png" alt="" />
      <div>蓝天⽅⾯</div>
      <img src="../img/titlebg4.png" alt="" />
    </div>
    <div class="contBox contBox1">
      <div class="toptext">年内推动：</div>
      <div class="ctval">
        <div class="name">关停低效小煤电机组</div>
        <div class="num">200<span>万千瓦以上</span></div>
      </div>
      <div class="ctval">
        <div class="name">关停退出通道城市焦化产能</div>
        <div class="num">450<span>万吨左右</span></div>
      </div>
    </div>
    <div class="box_title box_title2">
      <img src="../img/titlebg4.png" alt="" />
      <div>碧⽔⽅⾯</div>
      <img src="../img/titlebg4.png" alt="" />
    </div>

    <div class="contBox contBox2">
      <div class="toptext">以美丽河湖、美丽海湾创建为引领，系统推进“三水统筹”，年内：</div>
      <div class="ctval">
        <div class="name">新增县（市、区）整县制雨污合 流管网清零</div>
        <div class="num">25<span>个</span></div>
      </div>
      <div class="ctval">
        <div class="name">改造市政雨污合流管网</div>
        <div class="num">600<span>公里</span></div>
      </div>
      <div class="ctval">
        <div class="name">城市生活污水处理厂提标改造率</div>
        <div class="num">54<span>%</span></div>
      </div>
    </div>
    <div class="box_title box_title2">
      <img src="../img/titlebg4.png" alt="" />
      <div>净土⽅⾯</div>
      <img src="../img/titlebg4.png" alt="" />
    </div>

    <div class="contBox contBox3">
      <div class="toptext">严格落实建设用地土壤污染风险管控和修复名录制度，确保重点建设用地安全利用得到有效保障。</div>
      <div class="imglist">
        <div v-for="(item, i) in imglist" :key="i" class="imgitem">
          <img :src="item.img" alt="" />
          <div>{{ item.name }}</div>
        </div>
      </div>
      <div class="line"></div>
      <div class="ctval">
        <div class="name">新增完成⾏政村环境整治</div>
        <div class="num">2350<span>个</span></div>
      </div>
      <div class="ctval">
        <div class="name">新增完成行政村生活污水治理 任务</div>
        <div class="num">2034<span>个</span></div>
      </div>
      <div class="line"></div>
      <div class="toptext">指导各市划定地下水污染防治重点区。</div>
      <div class="imglist">
        <div v-for="(item, i) in imglist2" :key="i" class="imgitem">
          <img :src="item.img" alt="" />
          <div>{{ item.name }}</div>
        </div>
      </div>
    </div>
    <img v-if="isPad" src="../img/deco3.png" alt="" />
  </div>
</template>
<script>
import img1 from '../img/img6.png'
import img2 from '../img/img7.png'
export default {
  data() {
    return {
      isPad: window.matchMedia('(min-width: 600px)').matches,

      imglist: [
        {
          img: img1,
          name: '⼟壤污染⻛险管控'
        }
      ],
      imglist2: [
        {
          img: img2,
          name: '地下⽔污染防治'
        }
      ]
    }
  }
}
</script>
<style lang="scss" scoped>
.box {
  background: linear-gradient(to bottom, #e6f8ff 0%, #ffffff 20%, #ffffff 100%) center / 100% no-repeat;
  border-radius: 15px;
  position: relative;
  margin-top: 55px;
  .title {
    z-index: 2;
    top: -30px;
    width: 602px;
    height: 85px;
    background: url('../img/titltbg.png') center / 100% 100% no-repeat;
    text-align: center;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 32px;
    color: #ffffff;
    line-height: 90px;
    text-shadow: 0px 1px 11px rgba(49, 107, 179, 0.48);
    font-style: normal;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
  }
  .box_title {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    top: 0px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    font-size: 32px;
    color: #000000;
    line-height: 45px;
    text-align: center;
    font-style: normal;
    width: 100%;
    background: linear-gradient(0deg, #0284ff 0%, #1282ff 39%, #00bbff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    div {
      margin: 0 10px;
    }
    img {
      width: 59px;
      height: 17px;
    }
  }
  .box_title1 {
    top: 70px;
  }
  .box_title2 {
    top: 0;
  }
  .contBox {
    padding: 36px;
    position: relative;
    .toptext {
      width: 100%;

      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 28px;
      color: #212121;
      line-height: 48px;
      text-align: left;
      font-style: normal;
      text-indent: 2em;
      margin: 0px auto 20px;
      span {
        color: #3096ef;
      }
    }
  }
  .ctval {
    width: 100%;
    padding: 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(45deg, #4e93ff 0%, #5ebfff 100%);
    border-radius: 16px;
    margin-top: 30px;
    .name {
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 28px;
      color: #ffffff;
      line-height: 40px;
      width: 12em;
      text-align: left;
      font-style: normal;
    }
    .num {
      font-family: D-DIN, D-DIN;
      font-weight: bold;
      font-size: 68px;
      color: #ffffff;
      line-height: 27px;
      text-align: right;
      font-style: normal;
      span {
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 24px;
        color: #ffffff;
        line-height: 16px;
        text-align: left;
        font-style: normal;
      }
    }
  }
  .contBox1 {
    padding: 95px 36px 36px;
  }
  .imglist {
    width: 100%;
    display: grid;
    gap: 12px;
    margin: 35px 0;
    grid-template-columns: repeat(1, 1fr);
    .imgitem {
      display: flex;
      flex-direction: column;
      align-items: center;
      img {
        width: 610px;
        height: 196px;
      }
      div {
        padding: 0 10px;
        margin-top: -50px;
        width: 102%;
        height: 58px;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 22px;
        color: #ffffff;
        line-height: 28px;
        font-style: normal;
        background: url('../img/textbg.png') center / 100% 100% no-repeat;
      }
    }
  }
  .line {
    width: 100%;
    height: 5px;
    margin: 20px 0;
    background: url('../img/line.png') center center/604px 100% no-repeat;
  }
}
</style>
