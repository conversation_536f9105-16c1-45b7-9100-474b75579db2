<template>
  <div class="productbox">
    <div class="title">积极推进美丽⼭东建设实践</div>
    <div class="contBox">
      <div class="text">
        <img src="../img/dot.png" class="icon" alt="" />
        深入学习贯彻习近平总书记重要贺信精神，不断放大上海合作组织国家绿色发展论坛成功举办的溢出效应，推进我省绿色发展领域对外合作交流。
      </div>
      <div class="text">
        <img src="../img/dot.png" class="icon" alt="" />
        启动全省<span>“十五五”</span>生态环保规划编制研究工作，带头落实并统筹推进美丽山东建设重点任务落地。
      </div>
      <div class="text">
        <img src="../img/dot.png" class="icon" alt="" />
        分不同领域，着力打造美丽山东建设示范样板，力争年内再争创：
      </div>
      <div class="f_cont">
        <div v-for="(item, i) in datalist" :key="i" class="f_contitem">
          <div class="row1">
            <div class="num">{{ item.value }}</div>
            <div class="unit">{{ item.unit }}</div>
          </div>
          <div class="row2">
            {{ item.name }}
          </div>
        </div>
      </div>
      <div class="text" style="text-indent: 2em">
        同时对已<span>创建满5年的国家级生态文明示范区和“绿水青山就是金山银山”实践创新基地</span>开展复核。
      </div>

      <div class="imglist">
        <div v-for="(item, i) in imglist" :key="i" class="imgitem">
          <img :src="item.img" alt="" />
          <div>{{ item.name }}</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import img1 from '../img/img3.png'
import img2 from '../img/img4.png'
export default {
  data() {
    return {
      datalist: [
        {
          name: '国家级美丽海湾',
          value: '1-2',
          unit: '个'
        },
        {
          name: '国家级美丽河湖',
          value: '2-3',
          unit: '个'
        }
      ],
      imglist: [
        {
          img: img1,
          name: '国家级⽣态⽂明示范区'
        },
        {
          img: img2,
          name: '“绿水青山就是金山银山”实践创新基地'
        }
      ]
    }
  }
}
</script>
<style lang="scss" scoped>
.productbox {
  position: relative;
  margin-top: 50px;
  background-color: white;
  box-shadow: 0px 5px 15px rgba(222, 222, 222, 1);
  border-radius: 16px;
  .title {
    z-index: 2;
    top: -30px;
    width: 602px;
    height: 85px;
    background: url('../img/titltbg.png') center / 100% 100% no-repeat;
    text-align: center;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 32px;
    color: #ffffff;
    line-height: 90px;
    text-shadow: 0px 1px 11px rgba(49, 107, 179, 0.48);
    font-style: normal;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
  }
  .contBox {
    margin-top: 24px;
    padding: 85px 36px 36px;
    position: relative;
    .text {
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 28px;
      color: #212121;
      line-height: 48px;
      text-align: left;
      font-style: normal;
      margin-bottom: 30px;
      .book {
        width: 220px;
        height: 244px;
        float: right;
        transform: translateY(34px);
      }
      .deco {
        width: 220px;
        height: 244px;
        float: right;
      }
      .icon {
        width: 28px;
        height: 20px;
        margin-right: 10px;
      }

      span {
        color: #3096ef;
      }
    }
    .f_cont {
      display: grid;
      gap: 24px;
      margin-top: 23px;
      grid-template-columns: repeat(2, 1fr);
      .f_contitem {
        width: 100%;
        display: flex;
        height: 227px;
        flex-direction: column;
        justify-content: space-evenly;
        align-items: center;
        background: rgb(232, 243, 255);
        border-radius: 15px;
        .row1 {
          display: flex;
          align-items: baseline;
          .num {
            font-family: D-DIN, D-DIN;
            font-weight: bold;
            font-size: 68px;
            color: #ff7b05;
            line-height: 74px;
            text-align: right;
            font-style: normal;
          }
          .unit {
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 24px;
            color: #666666;
            line-height: 33px;
            text-align: left;
            font-style: normal;
            margin: 0 0 0 5px;
          }
        }
        .row2 {
          width: 278px;
          //   height: 63px;
          padding: 10px 0px;
          background: linear-gradient(to right, rgb(69, 183, 253) 0%, rgb(91, 220, 254) 100%);
          font-family: PingFangSC, PingFang SC;
          border-radius: 32px;
          font-weight: 600;
          font-size: 28px;
          color: #ffffff;
          line-height: 40px;
          text-align: center;
          font-style: normal;
          display: flex;
          align-items: center;
          justify-content: center;
          span {
            display: inline-block;
            background: #ffffff;
            border-radius: 16px;
            opacity: 0.8;
            padding: 2px 10px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            margin-right: 5px;
            font-size: 24px;
            color: #666666;
            line-height: 33px;
            text-align: left;
            font-style: normal;
          }
        }
      }
    }
    .imglist {
      width: 100%;
      display: grid;
      gap: 12px;
      margin: 35px 0;
      grid-template-columns: repeat(2, 1fr);
      .imgitem {
        display: flex;
        flex-direction: column;
        align-items: center;
        img {
          width: 294px;
          height: 196px;
        }
        div {
          padding: 0 10px;
          margin-top: -50px;
          width: 100%;
          height: 83px;
          display: flex;
          align-items: center;
          justify-content: center;
          text-align: center;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 22px;
          color: #ffffff;
          line-height: 28px;
          font-style: normal;
          background: url('../img/textbg2.png') center / 100% 100% no-repeat;
        }
      }
    }
  }
}
</style>
