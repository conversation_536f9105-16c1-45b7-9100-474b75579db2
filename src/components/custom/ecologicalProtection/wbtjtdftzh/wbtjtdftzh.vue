<template>
  <div class="productbox">
    <div class="title">稳步推进碳达峰碳中和</div>
    <div class="contBox">
      <div class="text">
        <img src="../img/dot.png" class="icon" alt="" />
        继续做好碳市场履约相关工作，力求应纳尽纳、应履尽履。
      </div>
      <div class="text">
        <img src="../img/dot.png" class="icon" alt="" />
        高质量推进消耗臭氧层物质履约。
      </div>
      <div class="ctval">
        <div class="name">推动纳⼊国家试点低碳城市建设</div>
        <div class="num">4<span>个</span></div>
      </div>
      <div class="ctval">
        <div class="name">推动纳⼊国家试点⽓候投融资试 点建设</div>
        <div class="num">1<span>个</span></div>
      </div>
      <div class="text">
        <img src="../img/dot.png" class="icon" alt="" />
        扎实推进威海市、济南新旧动能转换起步区国家气候适应型城市试点建设。
      </div>
      <div class="text">
        <img src="../img/dot.png" class="icon" alt="" />
        逐步完善碳普惠体系，推进海草床、地热资源利用等碳普惠方法学的编制实施。
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
  .productbox {
    position: relative;
    margin-top: 50px;
    background-color: white;
    box-shadow: 0px 5px 15px rgba(222, 222, 222, 1);
    border-radius: 16px;
    .title {
      z-index: 2;
      top: -30px;
      width: 602px;
      height: 85px;
      background: url('../img/titltbg.png') center / 100% 100% no-repeat;
      text-align: center;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 32px;
      color: #ffffff;
      line-height: 90px;
      text-shadow: 0px 1px 11px rgba(49, 107, 179, 0.48);
      font-style: normal;
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
    }
    .contBox {
      margin-top: 24px;
      padding: 85px 36px 36px;
      position: relative;
      .text {
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 28px;
        color: #212121;
        line-height: 48px;
        text-align: left;
        font-style: normal;
        margin-bottom: 40px;
        .book {
          width: 220px;
          height: 244px;
          float: right;
          transform: translateY(34px);
        }
        .deco {
          width: 220px;
          height: 244px;
          float: right;
        }
        .icon {
          width: 28px;
          height: 20px;
          margin-right: 10px;
        }

        span {
          color: #3096ef;
        }
      }
      .ctval {
        width: 100%;
        padding: 30px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        background: linear-gradient(45deg, #4e93ff 0%, #5ebfff 100%);
        border-radius: 16px;
        margin: 30px 0;
        .name {
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 28px;
          color: #ffffff;
          line-height: 40px;
          text-align: left;
          width: 12em;
          font-style: normal;
        }
        .num {
          font-family: D-DIN, D-DIN;
          font-weight: bold;
          font-size: 68px;
          color: #ffffff;
          line-height: 27px;
          text-align: right;
          font-style: normal;
          span {
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 24px;
            color: #ffffff;
            line-height: 16px;
            text-align: left;
            font-style: normal;
          }
        }
      }
    }
  }
</style>
