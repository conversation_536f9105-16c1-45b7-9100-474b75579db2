<template>
  <div class="productbox">
    <div class="title">坚决守牢⽣态环境安全底线</div>
    <div class="contBox">
      <div class="text">
        <img src="../img/dot.png" class="icon" alt="" />
        成功举办第三届山东省生态环境监测专业技术人员大比武活动。
      </div>
      <div class="text">
        <img src="../img/dot.png" class="icon" alt="" />
        黄海海洋辐射监测基地已挂牌运行，正在筹建山东省海洋生态环境监测与应急处置中心。
      </div>
      <div class="text">
        <img src="../img/dot.png" class="icon" alt="" />
        在全省范围开展危险废物<span>“万企全员”</span>警示教育，组织开展辐射安全警示教育<span>“开工第一课”</span>活动，部署开展全省环境安全风险隐患排查整治和安全生产督导检查，坚决守住守牢安全底线。
      </div>
      <div class="text">
        <img src="../img/dot.png" class="icon" alt="" />
        今年以来，全省未发生重大及以上突发环境事件，生态环境领域舆情总体平稳。
      </div>
     
    </div>
  </div>
</template>

<style lang="scss" scoped>
.productbox {
  position: relative;
  margin-top: 50px;
  background-color: white;
  box-shadow: 0px 5px 15px rgba(222, 222, 222, 1);
  border-radius: 16px;
  .title {
    z-index: 2;
    top: -30px;
    width: 602px;
    height: 85px;
    background: url('../img/titltbg.png') center / 100% 100% no-repeat;
    text-align: center;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 32px;
    color: #ffffff;
    line-height: 90px;
    text-shadow: 0px 1px 11px rgba(49, 107, 179, 0.48);
    font-style: normal;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
  }
  .contBox {
    margin-top: 24px;
    padding: 85px 36px 36px;
    position: relative;
    .text {
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 28px;
      color: #212121;
      line-height: 48px;
      text-align: left;
      font-style: normal;
      margin-bottom: 30px;
      .book {
        width: 220px;
        height: 244px;
        float: right;
        transform: translateY(34px);
      }
      .deco {
        width: 220px;
        height: 244px;
        float: right;
      }
      .icon {
        width: 28px;
        height: 20px;
        margin-right: 10px;
      }

      span {
        color: #3096ef;
      }
    }
  }
}
</style>
