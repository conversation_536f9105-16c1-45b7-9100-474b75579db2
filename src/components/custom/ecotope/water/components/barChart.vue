<template>
  <div ref="chart" style="width: 100%; height: 100%" />
</template>

<script>
import * as echarts from 'echarts'
export default {
  name: 'BarChart',
  props: {
    //接收的数据
    chartData: {
      type: Array,
      required: true,
      default() {
        return [
          { product: 'Matcha Latte', 2015: 43.3, 2016: 85.8, 2017: 93.7 },
          { product: 'Milk Tea', 2015: 83.1, 2016: 73.4, 2017: 55.1 },
          { product: 'Cheese Cocoa', 2015: 86.4, 2016: 65.2, 2017: 82.5 },
          { product: 'Cheese Ghsll', 2015: 52.3, 2016: 45.2, 2017: 32.5 },
          { product: 'Ghsll Cocoa', 2015: 26.4, 2016: 69.2, 2017: 72.5 },
          { product: 'Walnut Brownie', 2015: 72.4, 2016: 53.9, 2017: 39.1 }
        ]
      }
    },
    stdNum: {
      type: Array,
      default: () => []
    },
    ratio: {
      type: Array,
      default: () => []
    },
    name3: {
      type: Array,
      default: () => []
    },
    year: {
      type: Array,
      default: () => []
    },
    totalRatio: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      myChart: null,
      allNum: 0,
      colorArr: ['#0090ff', '#BD83FF', '#48E0E8', '#FFA26F', '#3CE2B0', '#66A0FF'],
      isPad: window.matchMedia('(min-width: 600px)').matches,
      // isInRange: window.matchMedia('(min-width: 600px) and (max-width: 630px)').matches
      isInRange: true,
      elWidth: ''
    }
  },
  watch: {
    chartData: {
      handler(newVal) {
        this.initChart(newVal)
      },
      immediate: true
    }
  },
  mounted() {
    window.addEventListener('resize', this.handleResize)
  },

  beforeUnmount() {
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    initChart(chartData) {
      //因为父组件使用的时候有可能该组件还没有dom.比如父组件在setup中请求了数据或者created中
      this.$nextTick(() => {
        const echarts = this.$echarts || echarts
        this.myChart = this.$shallowRef
          ? this.$shallowRef(echarts.init(this.$refs.chart, null, { renderer: 'canvas' }))
          : echarts.init(this.$refs.chart, null, { renderer: 'canvas' })
        this.getChart(chartData)
      })
    },
    getChart(chartData) {
      //数据获取到了,初始化图表
      const length = this.stdNum.length
      const stdNumvalue = this.stdNum[this.stdNum.length - 1]
      this.elWidth = this.$refs.chart.offsetWidth

      const newValue = this.stdNum.map((item) => {
        return {
          name: '',
          value: item,
          symbolPosition: 'end'
        }
      })
      // const totalRatio = this.totalRatio
      // const ratio = this.tatio
      const that = this

      if (!stdNumvalue) return
      const option = {
        color: this.colorArr,
        dataset: {
          source: chartData
        },
        dataZoom: [
          {
            bottom: 0,
            type: 'slider',
            showDetail: false,
            show: true,
            xAxisIndex: [0],
            start: 70,
            end: 100,
            height: 17, // 高度
            handleSize: '100%', // 手柄的大小
            handleIcon:
              'path://M50 0 C22.4 0 0 22.4 0 50 C0 77.6 22.4 100 50 100 C77.6 100 100 77.6 100 50 C100 22.4 77.6 0 50 0 Z', // 圆形手柄
            handleStyle: {
              color: '#447bcf', // 手柄颜色
              borderColor: '#fff', // 手柄边框颜色
              borderWidth: 1
            },
            fillerColor: 'rgba(85, 147, 253,0.6)', // 选中范围的填充颜色
            backgroundColor: 'rgba(47, 69, 84, 0.1)', // 背景色
            borderColor: '#ddd', // 边框颜色
            brushSelect: false,
            zoomLock: true
          }
        ],
        legend: {
          show: true,
          itemGap: 14,
          itemWidth: 10,
          itemHeight: 10,
          textStyle: {
            color: '#73767f',
            fontSize: 11
          },
          data: [
            {
              name: '地表水国控断面优良水体比例'
            }
          ]
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          confine: 'true',
          extraCssText: 'z-index: 9;',
          backgroundColor: 'rgba(0,0,0,0.6)',
          textStyle: {
            color: 'white'
          },
          formatter: function (params) {
            return params[0].axisValue + '<br/>' + '优于国家下达目标' + params[2].data + '个百分点'
          }
        },
        grid: {
          left: '0%',
          right: '3.5%',
          top: '20%',
          bottom: '10%',
          containLabel: true
        },

        xAxis: {
          type: 'category',
          data: this.year,
          show: true,
          axisTick: { alignWithLabel: true },
          axisLabel: {
            fontSize: 11,
            interval: 0,
            width: 40,
            overflow: 'break',
            color: '#73767f'
          }
        },
        yAxis: [
          {
            type: 'value',
            min: 60,
            max: 84
          },
          {
            name: '单位: %',
            nameTextStyle: { fontSize: 11, align: 'right', color: '#73767f' },
            type: 'value'
          }
        ],
        series: [
          {
            name: '',
            type: 'bar',
            stack: 'Total',
            yAxisIndex: 0,
            itemStyle: {
              borderColor: 'transparent',
              color: 'transparent'
            },
            data: this.stdNum
          },
          {
            name: '地表水国控断面优良水体比例',
            type: 'pictorialBar',
            symbolSize: [18, 6],
            symbolOffset: [0, -2],
            z: 8,
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: 'rgb(42, 123, 255)'
                  },
                  {
                    offset: 1,
                    color: 'rgb(42, 123, 255)'
                  }
                ])
              }
            },
            data: newValue
            // value: this.stdNum,
            // symbolPosition: 'end'
          },
          {
            name: '地表水国控断面优良水体比例',
            type: 'bar',
            stack: 'Total',
            barWidth: 18,
            yAxisIndex: 0,
            z: 5,
            data: this.name3,
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: 'rgba(116, 174, 255,.9)'
                },
                {
                  offset: 0.9,
                  color: 'rgba(39, 114, 251,.9)'
                },
                {
                  offset: 1,
                  color: 'rgba(39, 114, 251,.9)'
                }
              ])
            }
          },
          {
            name: '地表水国控断面优良水体比例',
            type: 'pictorialBar',
            symbolSize: [18, 6],
            symbolOffset: [0, -4],
            yAxisIndex: 0,
            z: 12,
            symbolPosition: 'end',
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [
                    {
                      offset: 0,
                      color: '#437bd5'
                    },
                    {
                      offset: 1,
                      color: '#437bd5'
                    }
                  ],
                  false
                )
              }
            },
            data: this.ratio
          },
          {
            name: '',
            type: 'line',
            yAxisIndex: 0,
            label: {
              show: true,
              position: 'top',
              fontSize: 12,
              width: 35,
              height: 15,
              backgroundColor: '#ffab67',
              borderRadius: 2,
              color: 'white'
            },
            // itemStyle: {
            //   color: '#ffab67'
            // },
            itemStyle: {
              color: 'rgb(118, 223, 181)',
              shadowColor: 'rgba(0, 0, 0, 0.4)',
              shadowBlur: 8,
              shadowOffsetX: 7,
              shadowOffsetY: 7
            },
            lineStyle: {
              color: 'rgb(118, 223, 181)',
              shadowColor: 'rgba(0, 0, 0, 0.5)',
              shadowBlur: 8,
              shadowOffsetX: 7,
              shadowOffsetY: 7
            },
            z: 13,
            symbolSize: 5,
            data: this.ratio
          },
          {
            name: '',
            type: 'line',
            // itemStyle: {
            //   color: '#ffab67'
            // },
            // itemStyle: {
            //   color: 'rgb(118, 223, 181)',
            //   shadowColor: 'rgba(0, 0, 0, 0.4)',
            //   shadowBlur: 8,
            //   shadowOffsetX: 7,
            //   shadowOffsetY: 7
            // },
            lineStyle: {
              color: 'rgb(118, 223, 181)',
              shadowColor: 'rgba(0, 0, 0, 0.5)',
              shadowBlur: 8,
              shadowOffsetX: 7,
              shadowOffsetY: 7
            },
            markPoint: {
              symbol:
                'path://M200.5,0 L209.25,6 L236,6 C238.209139,6 240,7.790861 240,10 L240,36 C240,38.209139 238.209139,40 236,40 L4,40 C1.790861,40 1.3527075e-16,38.209139 0,36 L0,10 C-2.705415e-16,7.790861 1.790861,6 4,6 L191.75,6 L200.5,0 Z',
              // elwidth宽度为516的时候是-25,是556的时候是-22,以此类推
              symbolOffset: that.isPad ? ['-33%', 20] : ['-33%', 20],
              // symbolRotate: 180,
              data: [
                {
                  name: '国家下达目标',
                  value: '国家下达目标' + stdNumvalue + '%',
                  coord: [length - 1, stdNumvalue],
                  symbolSize: [120, 30],
                  itemStyle: {
                    color: '#73deb3'
                  },
                  label: {
                    show: true,
                    color: 'white',
                    fontSize: 11,
                    lineHeight: 15,
                    offset: that.isPad ? [0, 3] : [0, 5]
                  }
                }
              ]
            },
            z: 1,
            symbolSize: 0,
            data: this.stdNum
          }
          // {
          //   name: '国家下达目标',
          //   type: 'line',
          //   color: '#73deb3',
          //   data: this.ratio.map((item) => item + 10)

          //   // markLine: {
          //   //   lineStyle: {
          //   //     color: '#73deb3',
          //   //     type: 'solid'
          //   //   },
          //   //   label: {
          //   //     show: false
          //   //   },
          //   //   symbolSize: [0, 0],
          //   //   data: [
          //   //     {
          //   //       name: '国家下达目标',
          //   //       yAxis: stdNumvalue
          //   //     }
          //   //   ]
          //   // },
          //   // markPoint: {
          //   //   symbol:
          //   //     'path://M200.5,0 L209.25,6 L236,6 C238.209139,6 240,7.790861 240,10 L240,36 C240,38.209139 238.209139,40 236,40 L4,40 C1.790861,40 1.3527075e-16,38.209139 0,36 L0,10 C-2.705415e-16,7.790861 1.790861,6 4,6 L191.75,6 L200.5,0 Z',
          //   //   // elwidth宽度为516的时候是-25,是556的时候是-22,以此类推
          //   //   symbolOffset: that.isPad
          //   //     ? [-3 + ((this.elWidth - 506) / 50) * 2.5, 16]
          //   //     : [-32 + ((this.elWidth - 250) / 50) * 6, 15],
          //   //   // symbolRotate: 180,
          //   //   data: [
          //   //     {
          //   //       name: '国家下达目标',
          //   //       value: '国家下达目标' + stdNumvalue + '%',
          //   //       coord: [length - 1, stdNumvalue],
          //   //       symbolSize: [120, 30],
          //   //       itemStyle: {
          //   //         color: '#73deb3'
          //   //       },
          //   //       label: {
          //   //         show: true,
          //   //         color: 'white',
          //   //         fontSize: 11,
          //   //         lineHeight: 15,
          //   //         offset: that.isPad ? [0, 3] : [0, 5]
          //   //       }
          //   //     }
          //   //   ]
          //   // }
          // }
        ]
      }
      this.myChart.setOption(option)
    },
    handleResize() {
      if (this.myChart) {
        this.myChart.resize()
      }
    }
  }
}
</script>
