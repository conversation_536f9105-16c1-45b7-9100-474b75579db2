<template>
  <div class="waterBox">
    <div class="title">
      <span class="span1">全省水环境概况</span>
      <img src="../img/boxbg_top_icon2.png" alt="" />
      <!-- <div style="color: red; font-size: 24px">{{ hasUnreadMsgBol }}</div> -->
      <HasUpdata2 v-if="hasUnreadMsgBol || false" class="HasUpdata" />
    </div>
    <div class="contentBox">
      <div v-if="textData1" class="left">
        <div class="time">{{ year }}</div>
        <div class="padNeed">
          <div class="topText">{{ textData1.field_name }}</div>
          <div class="centerText">
            <span class="value1">{{ textData1.field_value }}</span>
            <span class="unit1">{{ textData1.unit }}</span>
          </div>
        </div>

        <div v-if="textData2" class="btmText">
          <div class="row1">
            <span class="span1">{{ textData2.field_name.slice(0, 2) }}</span>
            <span class="span2">{{ textData2.field_name.slice(2) }}</span>
          </div>
          <div class="row2">
            <span class="value2">{{ textData2.field_value }}</span>
            <span class="unit2">{{ textData2.unit }}</span>
          </div>
        </div>
      </div>
      <div v-if="cardData.length > 0" class="right">
        <!-- <div class="time">2022</div> -->
        <popover
          :container="'.popover-8'"
          :actions="options"
          :default-option="selectedOption"
          class="toppopover"
          @selectedOption="handleSelectedTime"
        ></popover>
        <div class="topText">
          <span class="span1">{{ cardData[1].field_name }}</span>
          <span class="span2">{{ cardData[1].field_value }}</span>
          <span class="span3">{{ cardData[1].unit }}</span>
        </div>
        <img v-if="!isPad" src="../img/yuanhuan2.png" alt="" />
        <img v-else src="../img/yuanhuan3.png" alt="" />
        <div class="btmText">
          <span class="span4">{{ cardData[0].field_name }}</span>
          <span class="span5">{{ cardData[0].field_value }}</span>
          <span class="span6">{{ cardData[0].unit }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getAction } from '@/api/manage'
import popover from '@/components/selector.vue'

export default {
  components: {
    popover
  },
  data() {
    return {
      hasUnreadMsg: 0,
      hasUnreadMsgBol: false,
      textData1: null,
      textData2: null,
      options: [],
      selectedOption: '',
      cardData: [],
      isPad: window.matchMedia('(min-width: 600px)').matches,
      year: ''
    }
  },
  created() {
    this.getData()
  },
  mounted() {
    const urlParams = new URLSearchParams(window.location.search)
    this.hasUnreadMsg = Number(urlParams.get('hasUnreadMsg')) || 0
    this.$eventBus.$on('isUpdata', (params) => {
      //一个段落卡片引领多个指标,只要有一个指标是true,那么返回true && params.isUpdata,否则如果指标是false,数组有值不更新,数组没值追加false
      if (params.isUpdata && params.category == 2) {
        this.hasUnreadMsgBol = Boolean(this.hasUnreadMsg) && params.isUpdata
      }
    })
  },
  methods: {
    getData() {
      // getAction(import.meta.env.VITE_APP_SITUATION_BASE_URL + '/topic/data/listAllBySql/ydd_sthj_shj_mksy').then(
      //   (res) => {
      //     if (res.success) {
      //       this.year = res.result[0].year
      //       this.textData = res.result[0]
      //     }
      //   }
      // )
      getAction(import.meta.env.VITE_APP_SITUATION_BASE_URL + 'topic/data/listAllBySql/ydd_sthj_shj_ylstbl_jbqk').then(
        (res) => {
          if (res.success) {
            this.year = res.result[0].year
            this.textData1 = res.result[0]
            this.textData2 = res.result[1]
          }
        }
      )
      getAction(
        import.meta.env.VITE_APP_SITUATION_BASE_URL + '/topic/data/listAllBySql/ydd_sthj_shj_mksy_dmqk_item_year'
      ).then((res1) => {
        if (res1.success) {
          this.options = res1.result.map((item) => {
            return {
              text: item.year
            }
          })
          this.selectedOption = this.options[0].text
          this.getselectdata()
        }
      })
    },
    getselectdata() {
      getAction(import.meta.env.VITE_APP_SITUATION_BASE_URL + '/topic/data/listAllBySql/ydd_sthj_shj_mksy_dmqk', {
        year: this.selectedOption
      }).then((res2) => {
        this.cardData = res2.result
      })
    },
    handleSelectedTime(time) {
      this.selectedOption = time
      this.getselectdata()
    }
  }
}
</script>

<style lang="scss" scoped>
.HasUpdata {
  position: absolute;
  right: -40px;
  top: -25px;
}
.waterBox {
  width: 100%;
  position: relative;
  z-index: 2;
  margin-top: 110px;
  .toppopover {
    position: absolute;
    right: 4px;
    top: -3px;
    width: 240px;
    height: 78px;
  }
  .title {
    width: 411px;
    height: 220px;
    background: url('../img/boxbg_top2.png') center / 100% 100% no-repeat;
    position: absolute;
    z-index: 1;
    top: -70px;
    left: 10px;
    span {
      font-family: PingFangSC, PingFang SC;
      font-weight: 600;
      font-size: 34px;
      color: #ffffff;
      line-height: 48px;
      text-align: center;
      font-style: normal;
      position: absolute;
      top: 12px;
      left: 50px;
    }
    img {
      position: absolute;
      top: 20px;
      right: 20px;
      width: 49px;
      height: 38px;
    }
  }
  .contentBox {
    display: flex;
    position: relative;
    z-index: 2;
    .time {
      background: url('../img/timebg2.png') center/100% 100% no-repeat;
      width: 149px;
      height: 75px;
      position: absolute;
      right: 4px;
      top: -2px;
      z-index: 3;
      text-align: center;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 24px;
      color: #ffffff;
      line-height: 65px;
      font-style: normal;
    }
    .left {
      position: relative;
      backdrop-filter: blur(2px);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 350px;
      height: 377px;
      padding-top: 55px;
      background: url('../img/water_leftbg.png') center / 100% 100% no-repeat;
      .topText {
        font-family: PingFangSC, PingFang SC;
        font-weight: 600;
        font-size: 28px;
        color: #212121;
        line-height: 40px;
        text-align: center;
        font-style: normal;
        width: 9em;
      }
      .centerText {
        margin: 0px 0 25px 0;
        text-align: center;
        .value1 {
          font-family: D-DIN, D-DIN;
          font-weight: bold;
          font-size: 68px;
          color: #ff7b05;
          line-height: 74px;
          text-align: right;
          font-style: normal;
        }
        .unit1 {
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 24px;
          color: #666666;
          line-height: 33px;
          text-align: left;
          font-style: normal;
        }
      }
      .btmText {
        .row1 {
          font-family: PingFangSC, PingFang SC;
          font-weight: 600;
          font-size: 24px;
          color: #de242c;
          line-height: 33px;
          text-align: center;
          font-style: normal;
          .span1 {
            color: #de242c;
          }
          .span2 {
            color: #212121;
          }
        }
        .row2 {
          width: 240px;
          height: 70px;
          margin-top: 10px;
          text-align: center;
          line-height: 85px;
          background: url('../img/zhadai.png') center center/ 100% 100% no-repeat;
          .value2 {
            font-family: D-DIN, D-DIN;
            font-weight: bold;
            font-size: 48px;
            color: #de242c;
            line-height: 52px;
            text-align: right;
            font-style: normal;
          }
          .unit2 {
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 24px;
            color: #666666;
            line-height: 33px;
            text-align: left;
            font-style: normal;
          }
        }
      }
    }
    .right {
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 366px;
      height: 377px;
      backdrop-filter: blur(2px);
      padding-top: 55px;
      background: url('../img/water_rightbg.png') center / 100% 100% no-repeat;

      .topText {
        .span1 {
          font-family: PingFangSC, PingFang SC;
          font-weight: 600;
          font-size: 28px;
          color: #ff7b05;
          line-height: 40px;
          text-align: center;
          font-style: normal;
        }
        .span2 {
          font-family: D-DIN, D-DIN;
          font-weight: bold;
          font-size: 68px;
          color: #ff7b05;
          line-height: 74px;
          text-align: right;
          font-style: normal;
        }
        .span3 {
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 24px;
          color: #ff7b05;
          line-height: 33px;
          text-align: left;
          font-style: normal;
        }
      }
      img {
        width: 211px;
        height: 146px;
      }
      .btmText {
        transform: translate(-16px, -13px);
        .span4 {
          font-family: PingFangSC, PingFang SC;
          font-weight: 600;
          font-size: 28px;
          color: #3096ef;
          line-height: 40px;
          text-align: center;
          font-style: normal;
        }
        .span5 {
          font-family: D-DIN, D-DIN;
          font-weight: bold;
          font-size: 68px;
          color: #3096ef;
          line-height: 74px;
          text-align: right;
          font-style: normal;
        }
        .span6 {
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 24px;
          color: #3096ef;
          line-height: 33px;
          text-align: left;
          font-style: normal;
        }
      }
    }
  }
}
::v-deep {
  .el-input {
    width: 100%;
    height: 100%;
  }
  .el-input__inner {
    width: 100%;
    height: 100%;
    // background-color: transparent;
    background: url('../img/timebg2.png') no-repeat center/100% 100%;
    color: white;
    border: 0;
    text-align: center;
    font-size: 26px;
    padding-bottom: 15px;
  }
  .el-input__icon {
    color: #3096ef !important;
    line-height: 78px;
  }
  .el-input__prefix,
  .el-input__suffix {
    top: -5px;
  }
}
</style>
