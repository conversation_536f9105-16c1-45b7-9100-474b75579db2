<template>
  <div v-if="textData" class="ratioSituationBox">
    <div class="title">地表水国控断面优良水体比例</div>
    <div class="contentbox">
      <div class="btmBar">
        <barChart
          :chart-data="chartData"
          :std-num="stdNum"
          :ratio="ratio"
          :name3="name3"
          :year="year"
          :total-ratio="totalRatio"
        />
      </div>
      <sourceData :source-data="sourceData" class="sourcedata" :dp-type="false" :one-line="isPad" :category="category" />
    </div>
  </div>
</template>
<script>
import { getAction } from '@/api/manage'
import barChart from './components/barChart.vue'
import sourceData from '../common/sourceData.vue'

export default {
  components: {
    barChart,
    sourceData
  },
  props: {
    sourceData: {
      type: Object,
      default: () => {}
    },
    category: {
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      chartData: [],
      stdNum: [],
      ratio: [],
      totalRatio: [],
      name3: [],
      year: [],
      isPad: window.matchMedia('(min-width: 600px)').matches,
      textData: {}
    }
  },
  created() {
    this.loadData()
  },
  methods: {
    async loadData() {
      await getAction(
        import.meta.env.VITE_APP_SITUATION_BASE_URL + '/topic/data/listAllBySql/ydd_sthj_shj_ylstbl_jbqk'
      ).then((res) => {
        if (res.success) {
          this.textData = {
            name: res.result[0].field_name,
            value: res.result[0].field_value,
            unit: '%'
          }
        }
      })
      await getAction(
        import.meta.env.VITE_APP_SITUATION_BASE_URL + '/topic/data/listAllBySql/ydd_sthj_shj_ylstbl_month'
      ).then((res) => {
        if (res.success) {
          this.chartData = res.result
          this.stdNum = []
          this.ratio = []
          this.name3 = []
          this.year = []
          res.result.forEach((item) => {
            this.stdNum.push(item.std_num)
            this.ratio.push(item.ratio)
            this.name3.push(item.name3)
            this.year.push(item.year)
          })
      
          this.totalRatio = this.ratio.map((item, index) => item - (this.stdNum[index] || 0))
          // console.log(this.name3);
          // console.log(res.result);
          // console.log('++++++');
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.ratioSituationBox {
  position: relative;
  margin-top: 35px;
  padding-top: 20px;
  .title {
    top: -10px;
    width: 642px;
    height: 85px;
    background: url('../img/titltbg2.png') center / 100% 100% no-repeat;
    text-align: center;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 32px;
    color: #ffffff;
    line-height: 90px;
    text-shadow: 0px 1px 11px rgba(49, 107, 179, 0.48);
    font-style: normal;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
  }
  .contentbox {
    width: 100%;
    box-shadow: 0px 5px 15px rgba(222, 222, 222, 1);
    border-radius: 16px;
    background: linear-gradient(to bottom, #ddf4fe 0%, #ffffff 15%, #ffffff 100%);
    padding-top: 100px;
    .btmBar {
      padding: 38px 32px;
      background-color: white;
      box-shadow: 0px 5px 15px rgba(222, 222, 222, 1);
      border-radius: 16px;
      width: 650px;
      height: 720px;
      margin: 0 auto;
      transform: translateY(-28px);
    }
    .sourcedata {
      margin: 0 auto;
      width: 650px;
      padding-bottom: 40px;
    }
  }
}
</style>
