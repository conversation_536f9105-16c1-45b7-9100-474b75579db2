<template>
  <div v-if="echartList.length" class="ecotopeBox">
    <Info v-if="level" class="INFO" />

    <writtenInstructions v-if="$showInstruction" @showScreenShot="showScreenShot" />
    <ScreenShot v-if="$showInstruction" ref="ScreenShot" />
    <img v-if="!isPad" src="./img/topimg_ecotope_pad.png" alt="" class="img1" />
    <img v-if="!isPad" src="./img/topimgText_ecotope.png" alt="" class="img2" />
    <img v-if="isPad" src="./img/topimg_ecotope_pad.png" alt="" class="img1" />
    <img v-if="isPad" src="./img/topimgText_ecotope_pad.png" alt="" class="img2" />
    <div class="contentBox">
      <atmospheric />
      <PM2_5 :source-data="sourceData && sourceData[0]" :category="1" />
      <goodWeather :source-data="sourceData && sourceData[1]" :category="1" />
      <waterSituation />
      <ratioSituation :source-data="sourceData && sourceData[2]" :category="2" />
      <soilSituation :source-data="sourceData && sourceData[3]" :category="3" />
    </div>
  </div>
</template>

<script>
import atmospheric from './atmospheric/provinceAtmospheric.vue'
import Info from '@/components/globalComponent/Info2.vue'
import PM2_5 from './atmospheric/PM2_5.vue'
import goodWeather from './atmospheric/goodWeather.vue'
import waterSituation from './water/waterSituation.vue'
import ratioSituation from './water/ratioSituation.vue'
import soilSituation from './soil/soilSituation.vue'
import { topicMixin } from '@/mixins/topicMixin'

export default {
  components: {
    atmospheric,
    Info,
    PM2_5,
    goodWeather,
    ratioSituation,
    waterSituation,
    soilSituation
  },
  mixins: [topicMixin],

  data() {
    return {
      topicIdArr: ['A42A01A02', 'A42A01A04', 'A42A02A01', 'A42A03A02'],
      sourceData: null,
      level: 0,
      isPad: window.matchMedia('(min-width: 600px)').matches
    }
  },
  watch: {
    newEchartList: {
      handler(newvalue) {
        this.flattenEchartList(newvalue)
      }
    },
    deep: true,
    immediate: true
  },
  mounted() {
    const urlParams = new URLSearchParams(window.location.search)
    this.level = Number(urlParams.get('level'))
  },
  methods: {
    showScreenShot() {
      this.$refs.ScreenShot.showSreenShot = true
    },
    parseDataSource(e) {
      const arr = []
      e.forEach((element) => {
        if (this.topicIdArr.includes(element.topicCode)) {
          arr.push(element)
        }
      })
      const newarr = arr.map((el) => {
        return {
          value1: el.dataSource,
          value2: el.stSyncTime,
          value3: el.frequency,
          value4: el.dpDataTime
        }
      })
      return newarr
    },
    // 定义一个函数来提取所有的 echartList 对象
    flattenEchartList(data) {
      let result = []
      data.forEach((item) => {
        if (item.echartList) {
          item.echartList.forEach((element) => {
            result = result.concat(element)
          })
        }
      })
      this.sourceData = this.parseDataSource(result)
    }
  }
}
</script>

<style lang="scss" scoped>
.ecotopeBox {
  width: 100%;
  position: relative;
  background-color: #ddf4fe;
  padding-bottom: 50px;
  .INFO {
    top: 15px;
  }
  // margin-top: -30px;
  .img1 {
    margin-top: -40px;
  }
  .img2 {
    position: absolute;
    top: 60px;
    left: 40px;
    width: 324px;
    height: 129px;
  }
  .contentBox {
    padding: 0 24px;
    margin-top: -145px;
  }
}
</style>
