<template>
  <div v-if="sourceData" class="cpnBox_container">
    <!-- 底部数据来源 -->
    <div v-if="isShow" :class="oneLine ? 'bottom' : 'bottom-grid'">
      <div>数据来源: {{ sourceData.value1 }}</div>
      <div>更新时间: {{ sourceData.value2 }}</div>
      <div>更新周期: {{ sourceData.value3 }}</div>
      <div v-if="dpType">数据期别: {{ sourceData.value4 }}</div>
      <div v-else>最新期别: {{ sourceData.value4 }}</div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    dpType: {
      type: Boolean,
      default: true
    },
    oneLine: {
      type: Boolean,
      default: false
    },
    //把这个值返回出去,用来确定是哪个段落卡片下面的.修改有无更新用
    category: {
      type: Number,
      default: 3
    },
    sourceData: {
      type: Object,
      default: null
    },
    isShow: {
      type: Boolean,
      default: true
    }
  },
  watch: {
    sourceData(newvalue) {
      this.isNextMonth(newvalue)
    }
  },
  methods: {
    isNextMonth(sourceData) {
      // 解析传入的时间参数中的月份
      const yearMonth = sourceData.value2.match(/(\d{1,2})月/)
      if (!yearMonth) {
        console.log('时间不正确' + yearMonth)
      }
      const monthFromParam = parseInt(yearMonth[1], 10)
      const currentMonth = new Date().getMonth() + 1
      if ((currentMonth === 1 && monthFromParam === 12) || currentMonth === monthFromParam + 1||currentMonth === monthFromParam) {
        console.log('===========更新过了')
        // this.$event('isUpdata', { isUpdata: true, data: sourceData })
        this.$eventBus.$emit('isUpdata', { isUpdata: true, data: sourceData, category: this.category })
      } else {
        console.log('===========还没有更新')
        // this.$emit('isUpdata', { isUpdata: false, data: sourceData })
        this.$eventBus.$emit('isUpdata', { isUpdata: false, data: sourceData, category: this.category })
      }
    }
  }
}
</script>

<style scoped lang="scss">
.cpnBox_container {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 24px;
  color: #999999;
  line-height: 33px;
  text-align: left;
  font-style: normal;
  border-top: 1px dotted #999999;
  .bottom {
    margin-top: 12px;
    padding-top: 9px;
    height: 25px;
    border-top: var(--pad-card-border-top);
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: var(--pad-card-text-font-size);
    color: #999999;

    & > div {
      min-width: 138px;
      white-space: nowrap;
      text-align: center;
    }
  }
  .bottom-grid {
    margin-top: 14px;
    padding-top: 9px;
    display: grid;
    justify-content: space-between;
    grid-template-columns: repeat(2, auto);
    gap: 15px;
    white-space: wrap;
  }
}
// 用于pad端数据属性仅在右侧展示的情况
</style>
