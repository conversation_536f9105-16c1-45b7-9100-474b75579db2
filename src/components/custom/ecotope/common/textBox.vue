<template>
  <!-- 暴露多个插槽,如slotTop0  slotTop2  slotBottom0  slotBottom2 -->
  <div class="box">
    <div
      v-for="(item, index) in textData"
      :key="index"
      :class="{ textBox: true, border: index !== textData.length - 1 }"
      :style="boxStyle"
      class="boxdiv"
    >
      <div class="value1">{{ item.value1 }}</div>
      <slot :name="`slotTop${index}`" />
      <div :class="[`value2_${index}`, 'value2']">
        <slot :name="`arrowIcon${index}`" />{{ item.value2
        }}<span :class="['unit', `unit_${index}`]"><span v-html="item.unit"></span></span>
      </div>
      <slot :name="`slotBottom${index}`" />
    </div>
  </div>
</template>

<script>
export default {
  props: {
    textData: {
      type: Array,
      default() {
        return [
          {
            value1: '适龄劳动力',
            value2: '6546'
          },
          {
            value1: '适龄劳动力222',
            value2: '6546'
          }
        ]
      }
    },
    boxStyle: {
      type: Object,
      default() {
        return {}
      }
    }
  }
}
</script>

<style scoped lang="scss">
.box {
  .boxdiv {
    height: 125px;
    padding: '18px 0';
  }
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: space-between;
  .textBox {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    flex: 1;
    white-space: pre-wrap;
  }
  .border {
    border-right: 2px solid;
    border-image: linear-gradient(to bottom, white, rgba(93, 140, 212, 0.5), white) 1;
  }
  .value1 {
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 28px;
    color: #333333;
    line-height: 32px;
    text-align: center;
    font-style: normal;
    max-width: 8em;
  }
  .value2 {
    font-family: D-DIN, D-DIN;
    font-weight: bold;
    font-size: 48px;
    color: #3b93fb;
    text-align: left;
    font-style: normal;
  }
  .unit {
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 24px;
    color: #666666;
    text-align: left;
    font-style: normal;
  }
}
</style>
