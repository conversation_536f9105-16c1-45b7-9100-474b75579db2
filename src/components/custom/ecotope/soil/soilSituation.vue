<template>
  <div class="soilSituationBox">
    <div class="title">
      <span class="span1">全省土壤环境概况</span>
      <img src="../img/boxbg_top_icon3.png" alt="" />
      <!-- <div style="color: red; font-size: 24px">{{ hasUnreadMsgBol }}</div> -->
      <HasUpdata2 v-if="hasUnreadMsgBol || false" class="HasUpdata" />
    </div>
    <div class="time">{{ year }}</div>
    <div class="contentBox">
      <div class="topContent">
        <div class="topContent_title">全省土壤环境安全利用率</div>
        <div class="ratioContentBox">
          <div v-for="(item, i) in cardData" :key="i" class="ratioContent">
            <div class="ratio">
              <span class="span1">{{ item.field_value }}</span>
              <span class="span2">%</span>
            </div>
            <div class="name">{{ item.field_name }}</div>
            <img v-if="isPad && i === 0" src="@/components/custom/ecotope/img/line.png" alt="" class="line2" />
          </div>
        </div>
      </div>
      <img src="@/components/custom/ecotope/img/line.png" alt="" class="line" />
      <div class="btmContent">
        <barChart :chart-data="chartData" :chart-config="chartConfig" type="3" class="barchart" />
        <sourceData v-if="isPad" :source-data="sourceData" class="sourcedata" :dp-type="false" :category="category" />
      </div>
      <sourceData v-if="!isPad" :source-data="sourceData" class="sourcedata" :dp-type="false" :category="category" />
    </div>
  </div>
</template>

<script>
import { getAction } from '@/api/manage'
import barChart from '../common/barChart.vue'
import sourceData from '../common/sourceData.vue'

export default {
  components: {
    barChart,
    sourceData
  },
  props: {
    sourceData: {
      type: Object,
      default: () => {}
    },
    category: {
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      hasUnreadMsg: 0,
      hasUnreadMsgBol: false,
      chartData: [],
      chartConfig: {},
      textData: {},
      year: '',
      isPad: window.matchMedia('(min-width: 600px)').matches,
      cardData: null
    }
  },
  created() {
    this.loadData()
    this.getData()
  },
  mounted() {
    const urlParams = new URLSearchParams(window.location.search)
    this.hasUnreadMsg = Number(urlParams.get('hasUnreadMsg')) || 0
    this.$eventBus.$on('isUpdata', (params) => {
      console.log(params)
      //一个段落卡片引领多个指标,只要有一个指标是true,那么返回true && params.isUpdata,否则如果指标是false,数组有值不更新,数组没值追加false
      if (params.isUpdata && params.category == 3) {
        this.hasUnreadMsgBol = Boolean(this.hasUnreadMsg) && params.isUpdata
      }
    })
  },
  methods: {
    getData() {
      getAction(import.meta.env.VITE_APP_SITUATION_BASE_URL + '/topic/data/listAllBySql/ydd_sthj_trhj_mksy').then(
        (res) => {
          if (res.success) {
            this.cardData = res.result
            this.year = this.cardData[0].year
          }
        }
      )
    },

    async loadData() {
      await getAction(
        import.meta.env.VITE_APP_SITUATION_BASE_URL + '/topic/data/listAllBySql/ydd_sthj_trhj_trhjanlyl_year'
      ).then((res) => {
        if (res.success) {
          this.chartData = res.result
          this.chartConfig = {
            grid_top: this.isPad ? '33%' : '30%',
            yAxis1_name: '单位：%',
            tooltip_valueFormatter: (value) => `${value}%`
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.HasUpdata {
  position: absolute;
  right: -40px;
  top: -25px;
}
.soilSituationBox {
  width: 100%;
  position: relative;
  margin-top: 110px;
  z-index: 2;
  .time {
    background: url('../img/timebg.png') center/100% 100% no-repeat;
    width: 110px;
    height: 75px;
    position: absolute;
    right: -4px;
    top: -8px;
    z-index: 3;
    text-align: center;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 24px;
    color: #ffffff;
    line-height: 65px;
    font-style: normal;
  }
  .title {
    width: 411px;
    height: 220px;
    background: url('../img/boxbg_top.png') center / 100% 100% no-repeat;
    position: absolute;
    z-index: 1;
    top: -70px;
    left: 10px;
    span {
      font-family: PingFangSC, PingFang SC;
      font-weight: 600;
      font-size: 34px;
      color: #ffffff;
      line-height: 48px;
      text-align: center;
      font-style: normal;
      position: absolute;
      top: 12px;
      left: 50px;
    }
    img {
      position: absolute;
      top: 20px;
      right: 20px;
      width: 49px;
      height: 38px;
    }
  }
  .contentBox {
    // height: 620px;
    position: relative;
    background-color: rgba(255, 255, 255, 0.87);
    backdrop-filter: blur(10px);
    box-shadow: 0px 5px 15px rgba(222, 222, 222, 1);
    z-index: 2;
    border-radius: 16px;
    padding: 35px 20px 0;
    .topContent {
      .topContent_title {
        font-family: PingFangSC, PingFang SC;
        font-weight: 600;
        font-size: 28px;
        color: #212121;
        line-height: 40px;
        text-align: left;
        font-style: normal;
      }
      .ratioContentBox {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 15px;

        .ratioContent {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: space-between;
        }
        .ratio {
          text-align: center;
          .span1 {
            font-family: D-DIN, D-DIN;
            font-weight: bold;
            font-size: 68px;
            color: #ff7b05;
            line-height: 74px;
            text-align: right;
            font-style: normal;
          }
          .span2 {
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 24px;
            color: #666666;
            line-height: 33px;
            text-align: left;
            font-style: normal;
          }
        }
        .name {
          margin-top: 10px;
          width: 328px;
          height: 59px;
          line-height: 59px;
          background: url('../img/soilbg.png') center / 100% 100% no-repeat;
          font-family: PingFangSC, PingFang SC;
          font-weight: 600;
          font-size: 26px;
          color: #ffffff;
          text-align: center;
          font-style: normal;
        }
      }
    }
    .line {
      margin: 20px 0;
    }
    .btmContent {
      padding: 38px 32px;
      background-color: white;
      box-shadow: 0px 5px 15px rgba(222, 222, 222, 1);
      border-radius: 16px;
      width: 650px;
      height: 520px;
      margin: 0 auto;
    }
    .sourcedata {
      margin: 28px auto 0;
      width: 650px;
      padding-bottom: 40px;
    }
  }
}
</style>
