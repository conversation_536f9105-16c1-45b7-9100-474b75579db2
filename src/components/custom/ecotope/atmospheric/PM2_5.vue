<template>
  <div v-if="textData" class="pm2_5Box">
    <div class="title">PM2.5平均浓度</div>
    <div class="contentbox">
      <div class="topDesc">
        <div class="left">
          <div class="row1">
            <span class="span1">{{ textData.time1 }}</span>
            <span class="span2">{{ textData.name1 }}</span>
          </div>
          <div class="row2">
            <span class="span3">{{ textData.value1 }}</span>
            <span class="span4">{{ textData.unit1 }}</span>
          </div>
        </div>
        <div class="right">
          <img src="../img/yuanhu.png" alt="" />
          <div class="text">
            <div class="text1">{{ textData.name2 }}</div>
            <div class="text2">
              <!-- <img src="@/assets/img/arrow-up.png" class="text2_img" /> -->
              <span class="text2_desc">改善</span>
              <span class="text2_vlaue">{{ textData.value2 }}</span>
              <span class="text2_unit">%</span>
              <!-- <span class="unit">%</span> -->
            </div>
          </div>
        </div>
      </div>
      <div class="btmBar">
        <barChart :chart-data="chartData" :chart-config="chartConfig" type="1" :show-data-zoom="true" />
      </div>
      <sourceData :source-data="sourceData" class="sourcedata" :dp-type="false" :category="category" />
    </div>
  </div>
</template>

<script>
import { getAction } from '@/api/manage'
import barChart from '../common/barChart.vue'
import sourceData from '../common/sourceData.vue'

export default {
  components: {
    barChart,
    sourceData
  },
  props: {
    sourceData: {
      type: Object,
      default: () => {}
    },
    category: {
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      chartData: [],
      chartConfig: {},
      isPad: window.matchMedia('(min-width: 600px)').matches,
      textData: null
    }
  },
  created() {
    this.loadData()
  },
  methods: {
    async loadData() {
      await getAction(
        import.meta.env.VITE_APP_SITUATION_BASE_URL + '/topic/data/listAllBySql/ydd_sthj_dqhj_gkqkbh_month'
      ).then((res) => {
        if (res.success) {
          this.textData = {
            name1: res.result[0].field_name,
            value1: res.result[0].field_value,
            unit1: res.result[0].unit,
            time1: res.result[0].year,
            name2: '同比',
            // value2: res.result[0].field_value1,
            value2: res.result[0].field_value1,
            unit2: res.result[0].unit1
          }
        }
      })
      await getAction(
        import.meta.env.VITE_APP_SITUATION_BASE_URL + '/topic/data/listAllBySql/ydd_sthj_dqhj_PM2d5bh_month'
      ).then((res) => {
        if (res.success) {
          this.chartData = res.result
          this.chartConfig = {
            yAxis0_name: '单位：微克/立方米',
            yAxis1_name: '单位：%',
            tooltip_formatter: (e) => {
              let content = ''
              if (e[3].value > 0) {
                content = '同比提升' + e[3].value + '个百分点'
              } else if (e[3].value == 0) {
                content = '同比持平'
              } else {
                content = '同比降低' + e[3].value + '个百分点'
              }
              return `${e[0].axisValue}:</br>${e[1].seriesName}: ${e[1].value}微克/立方米</br>${content}`
            }
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.pm2_5Box {
  position: relative;
  margin-top: 35px;
  padding-top: 20px;
  .title {
    top: -10px;
    width: 402px;
    height: 85px;
    background: url('../img/titltbg.png') center / 100% 100% no-repeat;
    text-align: center;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 32px;
    color: #ffffff;
    line-height: 90px;
    text-shadow: 0px 1px 11px rgba(49, 107, 179, 0.48);
    font-style: normal;
    position: absolute;
    left: 50%;

    transform: translateX(-50%);
  }
  .contentbox {
    width: 100%;
    box-shadow: 0px 5px 15px rgba(222, 222, 222, 1);
    border-radius: 16px;
    background: linear-gradient(to bottom, #ddf4fe 0%, #ffffff 15%, #ffffff 100%);
    padding-top: 58px;
    .topDesc {
      width: 616px;
      height: 199px;
      background: url('../img/topDesc.png') center / 100% 100% no-repeat;
      margin: 0 auto;
      display: flex;
      align-items: center;
      justify-content: space-around;
      .left {
        display: flex;
        flex-direction: column;
        align-items: start;
        height: 150px;
        justify-content: space-evenly;
        .row1 {
          span {
            font-family: PingFangSC, PingFang SC;
            font-weight: 600;
            font-size: 28px;
            color: black;
            line-height: 20px;
            text-align: left;
            font-style: normal;
          }
          .span1 {
            color: #3b93fb;
          }
        }
        .row2 {
          .span3 {
            font-family: D-DIN, D-DIN;
            font-weight: bold;
            font-size: 68px;
            color: #ff7b05;
            line-height: 27px;
            text-align: justify;
            font-style: normal;
          }
          .span4 {
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 24px;
            color: #666666;
            line-height: 16px;
            text-align: left;
            font-style: normal;
          }
        }
      }
      .right {
        position: relative;
        img {
          width: 196px;
          height: 111px;
        }
        .text {
          position: absolute;
          top: 25px;
          left: 50%;
          transform: translateX(-50%);
          text-align: center;
          .text1 {
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 24px;
            color: #3b93fb;
            line-height: 45px;
            text-align: center;
            font-style: normal;
          }
          .text2 {
            display: flex;
            align-items: bottom;
          }
          .text2_desc {
            font-family: D-DIN, D-DIN;
            font-weight: bold;
            font-size: 32px;
            color: #de242c;
            line-height: 27px;
            text-align: center;
            font-style: normal;
            white-space: nowrap;
          }
          .text2_unit {
            font-family: D-DIN, D-DIN;
            font-weight: bold;
            line-height: 42px;
            font-size: 18px;
            color: #de242c;
            font-style: normal;
          }
          .text2_vlaue {
            font-family: D-DIN, D-DIN;
            font-weight: bold;
            font-size: 38px;
            // font-size: 32px;
            // color: #3b93fb;
            color: #de242c;
            line-height: 27px;
            font-style: normal;
          }
          .unit {
            font-family: D-DIN, D-DIN;
            font-weight: bold;
            font-size: 24px;
            color: #de242c;
            line-height: 27px;
            text-align: center;
            font-style: normal;
          }
        }
      }
    }
    .btmBar {
      padding: 38px 32px;
      background-color: white;
      box-shadow: 0px 5px 15px rgba(222, 222, 222, 1);
      border-radius: 16px;
      width: 650px;
      height: 620px;
      margin: 0 auto;
      transform: translateY(-28px);
    }
    .sourcedata {
      margin: 0 auto;
      width: 650px;
      padding-bottom: 40px;
    }
  }
}
</style>
