<template>
  <div class="atmosphericBox">
    <div class="title">
      <span class="span1">全省大气环境概况</span>
      <img src="../img/boxbg_top_icon.png" alt="" />
      <!-- <div style="color: red; font-size: 24px">{{ hasUnreadMsgBol }}</div> -->
      <HasUpdata2 v-if="hasUnreadMsgBol || false" class="HasUpdata" />
    </div>
    <div class="time">{{ year }}</div>
    <div class="contentBox">
      <div v-for="(item, i) in textData" :key="i" class="content">
        <div class="valueBox">
          <div class="value">{{ item.value2 }}</div>
          <div class="unit">{{ item.unit }}</div>
        </div>
        <div class="descBox">{{ item.value1 }}</div>
      </div>
    </div>
  </div>
</template>

<script>
import { getAction } from '@/api/manage'

export default {
  name: 'TourismAndCultureSmallCard1',
  data() {
    return {
      cardData: [],
      textData: [],
      hasUnreadMsg: 0,
      hasUnreadMsgBol: false,
      year: '',
      isPad: window.matchMedia('(min-width: 600px)').matches
    }
  },
  created() {
    this.getData()
  },
  mounted() {
    const urlParams = new URLSearchParams(window.location.search)
    this.hasUnreadMsg = Number(urlParams.get('hasUnreadMsg')) || 0
    this.$eventBus.$on('isUpdata', (params) => {
      //一个段落卡片引领多个指标,只要有一个指标是true,那么返回true && params.isUpdata,否则如果指标是false,数组有值不更新,数组没值追加false
      if (params.isUpdata && params.category == 1) {
        this.hasUnreadMsgBol = Boolean(this.hasUnreadMsg) && params.isUpdata
      }
    })
  },
  methods: {
    async getData() {
      await getAction(import.meta.env.VITE_APP_SITUATION_BASE_URL + '/topic/data/listAllBySql/ydd_sthj_dqhj_mksy').then(
        (res) => {
          if (res.success) {
            this.cardData = res.result
            this.year = res.result[0].year
            this.textData = res.result.map((item) => {
              return {
                value1: item.field_name,
                value2: item.field_value,
                unit: item.unit
              }
            })
          }
        }
      )
    }
  }
}
</script>

<style lang="scss" scoped>
.HasUpdata {
  position: absolute;
  right: -40px;
  top: -25px;
}
.atmosphericBox {
  width: 100%;
  position: relative;
  z-index: 2;
  .time {
    background: url('../img/timebg.png') center/100% 100% no-repeat;
    width: 110px;
    height: 75px;
    position: absolute;
    right: -4px;
    top: -8px;
    z-index: 3;
    text-align: center;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 24px;
    color: #ffffff;
    line-height: 65px;
    font-style: normal;
  }
  .title {
    width: 411px;
    height: 220px;
    background: url('../img/boxbg_top.png') center / 100% 100% no-repeat;
    position: absolute;
    z-index: 1;
    top: -70px;
    left: 10px;
    span {
      font-family: PingFangSC, PingFang SC;
      font-weight: 600;
      font-size: 34px;
      color: #ffffff;
      line-height: 48px;
      text-align: center;
      font-style: normal;
      position: absolute;
      top: 12px;
      left: 50px;
    }
    img {
      position: absolute;
      top: 20px;
      right: 20px;
      width: 49px;
      height: 38px;
    }
  }
  .contentBox {
    display: flex;
    height: 220px;
    position: relative;
    background-color: rgba(255, 255, 255, 0.87);
    backdrop-filter: blur(10px);
    box-shadow: 0px 5px 15px rgba(222, 222, 222, 1);
    z-index: 2;
    border-radius: 16px;
    align-items: center;
    justify-content: center;
    padding: 0 34px;

    .content {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      .valueBox {
        display: flex;
        align-items: baseline;
        justify-content: center;
        .value {
          font-family: D-DIN, D-DIN;
          font-weight: bold;
          font-size: 68px;
          color: #ff7b05;
          line-height: 74px;
          text-align: right;
          font-style: normal;
        }
        .unit {
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 24px;
          color: #666666;
          line-height: 33px;
          text-align: left;
          font-style: normal;
        }
      }
      .descBox {
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: PingFangSC, PingFang SC;
        font-weight: 600;
        font-size: 28px;
        color: #ffffff;
        line-height: 40px;
        text-align: center;
        font-style: normal;
        background: url('../img/PMbg.png') center / 100% 100% no-repeat;
        width: 258px;
        height: 59px;
        transform: translateY(15px);
      }
    }
  }
}
</style>
