<template>
  <div class="total-container">
    <div v-if="cardData" class="card-box">
      <div class="time">{{ cardData[0].index_time }}</div>
      <div class="select">
        <div>单位切换：</div>
        <div class="tab" :class="{ active: !!active }" @click="handleClickTab(1)">亿斤</div>
        <div class="tab" :class="{ active: !active }" @click="handleClickTab(0)">万吨</div>
      </div>
      <div class="flex-container">
        <div class="left-container">
          <div class="row-1">
            <span>{{ cardData[0].value1 }}</span>
            <span>{{ cardData[0].value2 }}</span>
          </div>
          <div class="row-2">
            <span>{{ cardData[0].value3 }}</span>
            <span>{{ cardData[0].value4 }}</span>
          </div>
          <div class="row-3">
            <span>{{ cardData[0].value5 }}</span>
            <span>{{ cardData[0].value6 }}</span>
            <span>{{ cardData[0].value7 }}</span>
            <span v-if="isPad">，{{ cardData[0].value8 }}</span>
            <div v-if="isPad" :class="cardData[0].value9 === 1 ? 'rank1' : 'rank-else'">{{ cardData[0].value9 }}</div>
          </div>
          <div v-if="!isPad" class="row-4">
            <span>{{ cardData[0].value8 }}</span>
            <div :class="cardData[0].value9 === 1 ? 'rank1' : 'rank-else'">{{ cardData[0].value9 }}</div>
          </div>
        </div>
        <div class="center-container">
          <div class="row-1">{{ cardData[1].value1 }}</div>
          <img :src="padArrow" alt="" />
          <div class="row-3">
            <img src="@/assets/img/arrow-up.png" alt="" />
            <div>{{ cardData[1].value2 }}</div>
            <div>{{ cardData[1].value3 }}</div>
          </div>
        </div>
        <div class="right-container">
          <div class="row-1">
            <span>{{ cardData[2].value1 }}</span>
            <span>{{ cardData[2].value2 }}</span>
          </div>
          <div class="row-2">
            <span>{{ cardData[2].value3 }}</span>
            <span>{{ cardData[2].value4 }}</span>
          </div>
          <div class="row-3">
            <span>{{ cardData[2].value5 }}</span>
            <span>{{ cardData[2].value6 }}</span>
            <span>{{ cardData[2].value7 }}</span>
            <span v-if="isPad">，{{ cardData[2].value8 }}</span>
            <div v-if="isPad" :class="cardData[2].value9 === 1 ? 'rank1' : 'rank-else'">{{ cardData[2].value9 }}</div>
          </div>
          <div v-if="!isPad" class="row-4">
            <span>{{ cardData[2].value8 }}</span>
            <div :class="cardData[2].value9 === 1 ? 'rank1' : 'rank-else'">{{ cardData[2].value9 }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { getCardData, getTime, getGrainYield } from '@/api/agriculture'
import { getAction } from '@/api/manage'
import titleWithIconVue from '../components/titleWithIcon.vue'
import popover from '@/components/selector.vue'
import pieChart from '../components/pieChart.vue'
import phoneArrow from '../image/arrow-right.png'
import padArrow from '../image/pad-arrow-right.png'

export default {
  components: {
    popover,
    titleWithIconVue,
    pieChart
  },
  inject: ['addBg'],
  data() {
    return {
      cardData: null,
      cardBelowData: null,
      options: [],
      selectedOption: '',
      chartData: [],
      active: 1,
      phoneArrow,
      padArrow,
      isPad: window.matchMedia('(min-width: 600px)').matches
    }
  },
  async mounted() {
    await this.getTime()
    this.getData()
    this.getBelowData()
  },
  methods: {
    async getData() {
      const res = await getAction(
        `${import.meta.env.VITE_APP_SITUATION_BASE_URL}/topic/data/listAllBySql/ydd_nync_lszclgk_kp?type=${this.active}`
      )
      this.cardData = res.result
      this.cardData.forEach((element) => {
        element.value3 = element.value3.replace(/,/g, '')
      })
    },
    async getTime() {
      const res = await getTime.time4()
      this.options = res.result.map((item) => {
        return {
          text: item.index_time
        }
      })
      this.selectedOption = this.options[0].text
    },
    async getBelowData() {
      const [res1, res2] = await Promise.all([
        getGrainYield(1, this.selectedOption),
        getGrainYield(2, this.selectedOption)
      ])
      this.cardBelowData = res1.result
      this.chartData = res2.result.map((item) => {
        return {
          name: item.field_name,
          value: item.field_value
        }
      })
    },
    handleClickTab(index) {
      this.active = index
      this.getData()
    },
    handleSelectedTime(time) {
      this.selectedOption = time
      this.getBelowData()
    }
  }
}
</script>
<style lang="scss" scoped>
.total-container {
  .card-box {
    width: 702px;
    height: 352px;
    background: url('../image/card-bg-4.png') no-repeat center center;
    background-size: 100% 100%;
    position: relative;
    .time {
      font-family: SourceHanSansCN, SourceHanSansCN;
      font-weight: 400;
      font-size: 22px;
      color: #ffffff;
      text-align: center;
      margin-left: 11px;
      background: url('/src/assets/img/date_bg3.png') no-repeat center/100% 48px;
      align-items: center;
      position: absolute;
      display: flex;
      align-items: center;
      justify-content: center;
      right: -5px;
      width: 192px;
      height: 48px;
      top: 25px;
      padding-bottom: 8px;
    }
    .select {
      display: flex;
      justify-content: start;
      align-items: center;
      padding: 20px 0 0 20px;

      div:nth-child(1) {
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 26px;
        color: #ffffff;
        line-height: 26px;
        text-align: left;
        font-style: normal;
      }
      .tab {
        width: 116px;
        height: 40px;
        background: rgba(255, 255, 255, 0.16);
        border-radius: 24px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 26px;
        color: rgba(255, 255, 255, 0.7);
        line-height: 40px;
        text-align: center;
        font-style: normal;
        margin-left: 20px;
      }
      .active {
        background-color: rgba(255, 255, 255, 0.4);
        color: #ffffff;
      }
    }
    .flex-container {
      width: 100%;
      top: 120px;
      position: absolute;
      display: flex;
      justify-content: space-around;
      align-items: center;
    }
    .left-container,
    .right-container {
      .row-1 {
        text-align: center;
        span {
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 30px;
          color: #ffffff;
          line-height: 25px;
          text-align: center;
          font-style: normal;
        }
      }
      .row-2 {
        margin: 20px auto;
        text-align: center;
        span:nth-child(1) {
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 52px;
          color: #ffffff;
          line-height: 48px;
          text-align: left;
          font-style: normal;
        }
        span:nth-child(2) {
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 24px;
          color: #ffffff;
          line-height: 22px;
          text-align: left;
          font-style: normal;
          margin-left: 3px;
        }
      }
      .row-3 {
        display: flex;
        align-items: center;
        justify-content: center;
        span {
          &:nth-child(1),
          &:nth-child(4) {
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 24px;
            color: #ffffff;
            line-height: 22px;
            text-align: left;
            font-style: normal;
          }
          &:nth-child(2) {
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 32px;
            color: #f6ff00;
            line-height: 28px;
            text-align: left;
            font-style: normal;
          }
          &:nth-child(3) {
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 24px;
            color: #f6ff00;
            line-height: 28px;
            text-align: left;
            font-style: normal;
          }
        }
        div {
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 25px;
          color: #ffffff;
          line-height: 45px;
          text-align: center;
          font-style: normal;
        }
        .rank1 {
          height: 45px;
          width: 45px;
          background: url('../image/sortBg1.png') no-repeat center center / 100% 100%;
        }
        .rank-else {
          height: 45px;
          width: 45px;
          background: url('../image/sortBg2.png') no-repeat center center / 100% 100%;
        }
      }
      .row-4 {
        display: flex;
        // margin-top: 10px;
        align-items: center;
        padding-top: 10px;
        justify-content: center;
        span {
          &:nth-child(1),
          &:nth-child(4) {
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 24px;
            color: #ffffff;
            line-height: 22px;
            text-align: left;
            font-style: normal;
          }
          &:nth-child(2) {
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 28px;
            color: #f6ff00;
            line-height: 28px;
            text-align: left;
            font-style: normal;
          }
          // &:nth-child(3) {
          //   font-family: PingFangSC, PingFang SC;
          //   font-weight: 500;
          //   font-size: 16px;
          //   color: #f6ff00;
          //   line-height: 28px;
          //   text-align: left;
          //   font-style: normal;
          // }
        }
        div {
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 25px;
          color: #ffffff;
          line-height: 45px;
          text-align: center;
          font-style: normal;
        }
        .rank1 {
          height: 52px;
          width: 52px;
          background: url('../image/sortBg1.png') no-repeat center center / 100% 100%;
        }
        .rank-else {
          height: 52px;
          width: 52px;
          background: url('../image/sortBg2.png') no-repeat center center / 100% 100%;
        }
      }
    }
    .center-container {
      width: 150px;
      height: 100px;
      background: rgba(58, 105, 237, 0.6);
      border-radius: 23px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      > img {
        width: 140px;
      }
      .row-1 {
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 22px;
        color: #ffffff;
        line-height: 22px;
        text-align: center;
        font-style: normal;
      }
      .row-3 {
        display: flex;
        align-items: baseline;
        img {
          height: 30px;
          width: 20px;
        }
        div:nth-of-type(1) {
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 32px;
          color: #dd2929;
          line-height: 32px;
          text-align: left;
          font-style: normal;
        }
        div:nth-of-type(2) {
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 18px;
          color: #ffffff;
          line-height: 18px;
          text-align: left;
          font-style: normal;
        }
      }
    }
  }
  .card-box-below {
    padding: 0 28px 0 28px;
    .popover-4 {
      display: flex;
      justify-content: end;
    }
    .flex-container {
      display: flex;
      justify-content: space-around;
      align-items: center;
      margin-top: 42px;
    }
    .left-container,
    .right-container {
      .title {
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 32px;
        color: #212121;
        line-height: 45px;
        text-align: center;
        font-style: normal;
      }
      .unit {
        margin-left: 8px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 24px;
        color: #666666;
        line-height: 33px;
        text-align: left;
        font-style: normal;
      }
      .value {
        margin-top: 15px;
        text-align: center;
      }
    }
    .left-container .num {
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 48px;
      color: #3b93fb;
      line-height: 67px;
      text-align: right;
      font-style: normal;
    }
    .right-container {
      .image-up {
        height: 42px;
        width: 30px;
        background: url('@/assets/img/arrow-up.png') no-repeat center center;
        background-size: 100% 100%;
        display: inline-block;
      }
      .image-down {
        height: 42px;
        width: 36px;
        background: url('@/assets/img/arrow-up.png') no-repeat center center;
        background-size: 100% 100%;
        display: inline-block;
      }
      .num {
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 48px;
        color: #de242c;
        line-height: 67px;
        text-align: right;
        font-style: normal;
      }
    }
    .below-title2 {
      margin-top: 46px;
    }
    .center-container {
      width: 97px;
      height: 63px;
      background: url('../image/grainYield-bg-1.png') no-repeat center center;
      background-size: 100% 100%;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 24px;
      color: #ffffff;
      line-height: 33px;
      text-align: center;
      font-style: normal;
    }
    .chart-container {
      height: 530px;
    }
  }
}
::v-deep {
  .van-button {
    // width: 260px;
    padding: 10px 30px;
    height: 60px;
    font-size: 26px;
  }
  .van-popover__content {
    overflow: scroll;
    max-height: 300px;
    width: 180px;
    border-radius: 10px;
  }
  .van-popover__action {
    font-size: 24px;
    line-height: 60px;
    width: 100%;
    height: 60px;
    padding: 8px;
  }
}
</style>
