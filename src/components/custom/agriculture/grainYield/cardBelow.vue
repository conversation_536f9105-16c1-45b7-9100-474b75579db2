<template>
  <div v-if="!!data" class="total-container">
    <div class="row-1">
      <div class="left">
        <div class="title title1">{{ data[0].index_name }}</div>
        <div class="value value1">
          <span class="num">{{ data[0].index_value1 }}</span>
          <span class="unit">{{ data[0].unit1 }}</span>
        </div>
      </div>
      <div class="right">
        <div class="title">{{ data[0].index_name2 }}</div>
        <div class="value">
          <img src="@/assets/img/arrow-up.png" alt="" />
          <div class="num">{{ data[0].index_value2 }}</div>
          <div class="unit">{{ data[0].unit2 }}</div>
        </div>
      </div>
    </div>
    <div class="row-2">
      <div class="left">
        <div class="title">{{ data[1].index_name }}</div>
        <div class="value">
          <span class="num">{{ data[1].index_value1 }}</span>
          <span class="unit">{{ data[1].unit1 }}</span>
        </div>
      </div>
      <div class="right">
        <div class="title">{{ data[1].index_name2 }}</div>
        <img src="@/assets/img/arrow-up.png" alt="" />
        <div class="num">{{ data[1].index_value2 }}</div>
        <div class="unit">{{ data[1].unit2 }}</div>
      </div>
    </div>
    <div class="row-3">
      <div class="left">
        <div class="title">{{ data[2].index_name }}</div>
        <div class="value">
          <span class="num">{{ data[2].index_value1 }}</span>
          <span class="unit">{{ data[2].unit1 }}</span>
        </div>
      </div>
      <div class="right">
        <div class="title">{{ data[2].index_name2 }}</div>
        <div class="value">
          <div class="num">{{ data[2].index_value2 }}</div>
          <div class="unit">{{ data[2].unit2 }}</div>
        </div>
      </div>
    </div>
    <orderlist></orderlist>
  </div>
</template>
<script>
import { getAction } from '@/api/manage'
import orderlist from './orderlist.vue'

export default {
  components: { orderlist },

  data() {
    return {
      data: null
    }
  },
  mounted() {
    this.getData()
  },
  methods: {
    async getData() {
      const res = await getAction(
        `${import.meta.env.VITE_APP_SITUATION_BASE_URL}/topic/data/listAllBySql/ydd_nync_lszclgk`
      )
      this.data = res.result
    }
  }
}
</script>
<style lang="scss" scoped>
.total-container {
  .row-1,
  .row-2,
  .row-3 {
    width: 100%;
    height: 200px;
    background: url('../image/lscl-icon1.png') no-repeat center center / 100% 100%;
    position: relative;
    .title {
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 30px;
      color: #212121;
      line-height: 30px;
      text-align: left;
      font-style: normal;
    }

    .left {
      position: absolute;
      top: 30px;
      left: 20px;
      .value {
        margin-top: 40px;
      }
      .num {
        font-family: PingFangSC, PingFang SC;
        font-weight: 600;
        font-size: 45px;
        color: #3b93fb;
        line-height: 45px;
        text-align: justify;
        font-style: normal;
      }
      .unit {
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 23px;
        color: #666666;
        line-height: 23px;
        text-align: left;
        font-style: normal;
        margin-left: 3px;
      }
    }
  }
  .row-1,
  .row-3 {
    .right {
      position: absolute;
      top: 30px;
      left: 360px;
      .value {
        display: flex;
        align-items: baseline;
        margin-top: 40px;
        img {
          height: 36px;
          width: 24px;
        }
        .num {
          font-family: PingFangSC, PingFang SC;
          font-weight: 600;
          font-size: 45px;
          color: #f63146;
          line-height: 45px;
          text-align: justify;
          font-style: normal;
        }
        .unit {
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 23px;
          color: #666666;
          line-height: 23px;
          text-align: left;
          font-style: normal;
          margin-left: 3px;
        }
      }
    }
  }
  .row-3 {
    .right {
      left: 380px;
      .value {
        .num,
        .unit {
          color: #3b93fb;
        }
      }
    }
  }
  .row-2 {
    background: url('../image/lscl-icon2.png') no-repeat center center / 100% 100%;
    .right {
      display: flex;
      align-items: baseline;
      position: absolute;
      left: 340px;
      bottom: 60px;
      img {
        height: 36px;
        width: 24px;
        margin-left: 20px;
      }
      .num {
        font-family: PingFangSC, PingFang SC;
        font-weight: 600;
        font-size: 45px;
        color: #f63146;
        line-height: 45px;
        text-align: justify;
        font-style: normal;
      }
      .unit {
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 23px;
        color: #f63146;
        line-height: 23px;
        text-align: left;
        font-style: normal;
        margin-left: 3px;
      }
    }
  }
  .row-3 {
    background: url('../image/lscl-icon3.png') no-repeat center center / 100% 100%;
  }
  .title1 {
    white-space: pre;
  }
  .value1{
    margin-top: 10px !important;
  }
}
</style>
