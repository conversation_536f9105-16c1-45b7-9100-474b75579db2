<template>
  <div class="container">
    <div class="orderList" @click="toggle">
      <div class="left">
        <img src="../image/selectLeft.png" alt="" />
        <span class="desc">{{ year }}山东省粮食总产量全国排名第<span class="no">{{ shandongOrder }}</span></span>
      </div>
      <div class="right">
        <div>查看更多</div>
        <div :class="{ icon: true, ani: isVisible }"></div>
        <!-- <van-icon v-if="isVisible" name="arrow-up" />
        <van-icon v-else name="arrow-down" /> -->
      </div>
    </div>

    <transition name="fade" @before-enter="beforeEnter" @enter="enter" @before-leave="beforeLeave" @leave="leave">
      <ul v-if="isVisible" class="item-list">
        <li>
          <div class="topTime">
            <div v-if="isVisible" class="unitBox">
              <div :class="{ yijin: true, active: activeName == '亿斤' }" @click="unitClick('亿斤')">亿斤</div>
              <div :class="{ wandun: true, active: activeName == '万吨' }" @click="unitClick('万吨')">万吨</div>
            </div>
            <div>数据期别: {{ year }}</div>
          </div>
          <div class="firstli">
            <div class="addr">地区</div>
            <div class="addIcon addIcon1" @click="productionClick">
              粮食总产量({{ activeName }})
              <div class="icon">
                <i
                  class="el-icon-caret-top"
                  :style="{ color: sort_field == 'production1' && production_sort == 'asc' ? '#1994ff' : '#bdd3f8' }"
                ></i>
                <i
                  class="el-icon-caret-bottom"
                  :style="{ color: sort_field == 'production1' && production_sort == 'des' ? '#1994ff' : '#bdd3f8' }"
                ></i>
              </div>
            </div>
            <div class="addIcon" @click="rateClick">
              比重(%)
              <div class="icon">
                <i
                  class="el-icon-caret-top"
                  :style="{ color: sort_field == 'ratio' && rate_sort == 'asc' ? '#1994ff' : '#bdd3f8' }"
                ></i>
                <i
                  class="el-icon-caret-bottom"
                  :style="{ color: sort_field == 'ratio' && rate_sort == 'des' ? '#1994ff' : '#bdd3f8' }"
                ></i>
              </div>
            </div>
          </div>
        </li>
        <li v-for="(item, index) in items" :key="index" :class="{ listItem: true, isactive: item.isactive }">
          <div class="addr">{{ item.district_name }}</div>
          <div>
            {{
              activeName == '亿斤'
                ? item.production2
                  ? item.production2
                  : '--'
                : item.production1
                  ? item.production1
                  : '--'
            }}
          </div>
          <div class="rate">
            <!-- <img v-if="item.ratio > 0" src="@/assets/img/arrow-up.png" alt="" />
            <img v-else src="@/assets/img/arrow-down.png" alt="" /> -->
            <span>
              {{ item.ratio ? item.ratio : '--' }}
            </span>
          </div>
        </li>
      </ul>
    </transition>
  </div>
</template>

<script>
import { getAction } from '@/api/manage'

export default {
  data() {
    return {
      activeName: '亿斤',
      isVisible: false,
      items: [],
      production_sort: 'des',
      rate_sort: 'des',
      sort_field: 'production1',
      shandongOrder: 0,
      year: 0
    }
  },
  mounted() {
    this.loadData(1)
  },
  methods: {
    unitClick(t) {
      this.activeName = t
    },
    toggle() {
      this.isVisible = !this.isVisible
    },
    productionClick() {
      this.sort_field = 'production1'
      this.production_sort = this.production_sort == 'des' ? 'asc' : 'des'
      this.rate_sort = 'des'
      this.loadData(1)
    },
    rateClick() {
      this.sort_field = 'ratio'
      this.rate_sort = this.rate_sort == 'des' ? 'asc' : 'des'
      this.production_sort = 'des'
      this.loadData(2)
    },
    async loadData(type) {
      let params
      if (type === 1) {
        params = {
          sort_field: this.sort_field,
          sort_order: this.production_sort
        }
      } else {
        params = {
          sort_field: this.sort_field,
          sort_order: this.rate_sort
        }
      }
      const res = await getAction(
        `${import.meta.env.VITE_APP_SITUATION_BASE_URL}/topic/data/listAllBySql/ydd_nync_lszclgk_qgsfczzs`,
        params
      )
      this.items = res.result
      res.result.forEach((el, index) => {
        if (el.district_name == '山东') {
          el.isactive = true
        }
        if (this.sort_field == 'production1' && this.production_sort == 'des' && el.district_name == '山东') {
          this.shandongOrder = index
          this.year = el.year
        }
      })
    },
    beforeEnter(el) {
      el.style.maxHeight = '0'
      el.style.transition = 'max-height 0.5s ease-in-out'
    },
    enter(el, done) {
      el.offsetHeight
      el.style.maxHeight = `${el.scrollHeight}px`
      done()
    },
    beforeLeave(el) {
      el.style.transition = 'max-height 0.5s ease-in-out'
      el.style.maxHeight = `${el.scrollHeight}px`
    },
    leave(el, done) {
      el.style.maxHeight = '0'
      done()
    }
  }
}
</script>

<style scoped lang="scss">
.orderList {
  width: 100%;
  margin-top: 20px;
  height: 156px;
  background: url('../image/selectBg.png') center/100% 100% no-repeat;
  border-radius: 15px;
  position: relative;
  display: flex;
  align-items: center;
  .left {
    display: flex;
    border-radius: 15px;
    align-items: center;
    img {
      width: 70px;
      height: 70px;
      margin-left: 25px;
    }
    .desc {
      font-size: 30px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      margin-left: 23px;
      width: 12em;
    }
    .no {
      color: rgb(59, 147, 251);
      font-size: 52px;
      line-height: 52px;
      margin-left: 4px;
      vertical-align: bottom;
    }
  }
  .right {
    position: absolute;
    right: 10px;
    bottom: 16px;
    width: 178px;
    height: 55px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 24px;
    color: #ffffff;
    line-height: 33px;
    text-align: left;
    font-style: normal;
    display: flex;
    align-items: center;
    justify-content: center;
    background: url('../image/selectrightbg.png') center/100% 100% no-repeat;
    padding: 8px 0 0 8px;
    .icon {
      margin-left: 8px;
      width: 22px;
      height: 14px;
      transition: all ease 0.2s;
      background: url('../image/selecticon.png') center/100% 100% no-repeat;
    }
    .ani {
      transition: all ease 0.2s;
      transform: rotateZ(180deg);
    }
  }
}
.container {
  margin: 20px auto 0;
  // padding: 0 10px;
}

.item-list {
  padding: 0;
  height: 490px;
  overflow-y: scroll !important;
  list-style: none;
  margin: 20px 0;
  position: relative;
  overflow: hidden; /* 防止内容溢出 */
}

li {
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  background-color: rgb(245, 249, 255);
  border-radius: 30px;
  margin-bottom: 20px;
  display: flex;
  justify-content: space-evenly;
  padding: 20px 8px;
  font-size: 30px;
  color: #333;
  .addIcon {
    display: flex;
    align-items: center;
    justify-content: center;

    .icon {
      display: flex;
      flex-direction: column;
      align-items: center;
      .el-icon-caret-top,
      .el-icon-caret-bottom {
        line-height: 0.7 !important;
      }
    }
  }

  > div {
    flex: 1;
    text-align: center;
  }
  .addIcon1 {
    flex: 1.3 !important;
  }
  .addr {
    flex: 0.7;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .rate {
    color: #1994ff;
    font-weight: 600;
    img {
      width: 26px;
      height: 30px;
    }
  }
  .rate_up {
    color: #f63146;
    vertical-align: middle;
  }
  .rate_down {
    color: #43d593;
    vertical-align: middle;
  }
}

li:first-child {
  top: 0;
  position: sticky;
  background-color: rgb(224, 241, 255);
  font-weight: 600;
  margin-bottom: 20px;
  padding: 10px 0 15px;
  border-radius: 0;
  background-color: white;
  display: flex;
  flex-direction: column;
  background-color: rgb(224, 241, 255);
  border-radius: 30px;

  .topTime {
    color: #75b9f4;
    line-height: 1.5;
    font-size: 24px;
    padding-bottom: 10px;
    text-align: right;
    padding: 0 25px 10px;
    border-bottom: 1px dashed #bdd3f8;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .unitBox {
      display: flex;
      align-items: center;
      div {
        padding: 4px 30px;
        border-radius: 20px;
        background-color: #e0f1ff;
        font-family: PingFangSC, PingFang SC;
        font-size: 24px;
      }
      .yijin {
        margin-right: 20px;
      }
      .active {
        background-color: #f5f9ff;
        color: #3c74be;
        font-weight: 600;
      }
    }
  }
  .firstli {
    color: rgb(25, 148, 255);
    display: flex;
    padding: 10px 8px 0px;
    font-size: 28px;
    justify-content: space-evenly;
    align-items: center;

    .addr {
      flex: 0.7;
    }
    .addIcon {
      flex: 1;
    }
  }
}
.isactive {
  background-color: #fef8e8;
}
/* 定义过渡效果 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter,
.fade-leave-to {
  opacity: 0;
}
</style>
