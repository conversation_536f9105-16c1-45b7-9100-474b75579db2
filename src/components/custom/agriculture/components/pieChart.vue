<template>
  <div ref="charts" class="chart"></div>
</template>

<script>
import { colors } from '@/utils/constant'
import * as echarts from 'echarts'
export default {
  name: 'PieC<PERSON>',
  props: {
    chartData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      myChart: null,
      colors: ['hsla(43, 100%, 70%, 1)', 'hsla(220, 93%, 72%, 1)']
    }
  },
  watch: {
    chartData: {
      handler(val) {
        if (val) {
          this.init(val)
        }
      }
    }
  },
  mounted() {
    // const dom = document.querySelector('.chart-container')
    // let drag = elementResizeDetectorMaker()
    // drag.listenTo(dom, () => {
    //   this.myChart && this.myChart.resize()
    // })
  },
  methods: {
    init(val) {
      if (this.myChart === null) {
        this.myChart = echarts.init(this.$refs.charts)
      }
      let that = this
      let option = {
        color: this.colors,
        tooltip: {
          confine: true,
          valueFormatter: (value) => value + '%',
          extraCssText: 'z-index: 9;',
          backgroundColor: 'rgba(0,0,0,0.6)',
          textStyle: {
            color: 'white'
          }
        },
        legend: {
          type: 'scroll',
          orient: 'vertical',
          left: 'left',
          top: 'top'
        },
        series: [
          {
            name: '',
            type: 'pie',
            radius: '45%',
            center: ['50%', '60%'],
            data: val,
            label: {
              alignTo: 'edge',
              fontFamily: 'PingFangSC, PingFang SC',
              fontWeight: '400',
              textAlign: 'center',
              minMargin: 5,
              edgeDistance: 0,
              lineHeight: 20,
              width: 80,
              posinTo: 'edge',
              margtion: 'outer',
              overflow: 'break',
              aligin: 0,
              fontSize: 12,
              fontStyle: 'normal',
              color: 'inherit',
              formatter: (params) => {
                return `${params.name}${params.value}%`
              }
            }
          }
        ]
      }
      this.myChart.setOption(option)
      window.addEventListener('resize', function () {
        this.myChart?.resize()
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.chart {
  width: 100%;
  height: 100%;
}
</style>
