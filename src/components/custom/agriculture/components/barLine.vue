<template>
  <div>
    <div ref="chartRef" class="echarts-container base-line"></div>
  </div>
</template>

<script>
import { ref, reactive, watchEffect } from 'vue'
import { useECharts } from '@/composables/useECharts'
import { omit, isEmpty } from 'lodash'
import * as echarts from 'echarts'
export default {
  name: 'BarLine',
  props: {
    chartData: {
      type: Object,
      default: () => ({})
    },
    isShowBottominfor: {
      type: Boolean,
      default: true
    },
    type: {
      type: Number,
      default: 0
    }
  },
  setup(props, { emit }) {
    // const emit = defineEmits(['clickData'])
    var screenWidth = 0
    window.addEventListener('resize', updateScreenWidth()) // 监听窗口大小变化
    function updateScreenWidth() {
      screenWidth = window.innerWidth
    }
    const chartRef = ref(null)
    const { setOptions, getInstance } = useECharts(chartRef)
    const option = reactive({
      grid: {
        top: '26%',
        bottom: '10%',
        left: '0%',
        right: '0%',
        containLabel: true
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        },
        // 将 tooltip 框限制在图表的区域内
        confine: 'true',
        extraCssText: 'z-index: 9;',
        backgroundColor: 'rgba(0,0,0,0.6)',
        textStyle: {
          color: 'white'
        }
      },
      dataZoom:
        props.type == 2
          ? [
              {
                bottom: 0,
                type: 'slider',
                showDetail: false,
                show: true,
                xAxisIndex: [0],
                start: 50,
                end: 100,
                height: 17, // 高度
                handleSize: '100%', // 手柄的大小
                handleIcon:
                  'path://M50 0 C22.4 0 0 22.4 0 50 C0 77.6 22.4 100 50 100 C77.6 100 100 77.6 100 50 C100 22.4 77.6 0 50 0 Z', // 圆形手柄
                handleStyle: {
                  color: 'rgb(90, 172, 243)', // 手柄颜色
                  borderColor: '#fff', // 手柄边框颜色
                  borderWidth: 1
                },
                fillerColor: 'rgba(165, 210, 248,0.6)', // 选中范围的填充颜色
                backgroundColor: 'rgba(90, 172, 243, 0.1)', // 背景色
                borderColor: '#ddd', // 边框颜色
                brushSelect: false,
                zoomLock: true
              }
            ]
          : [],
      title: [
        {
          textStyle: {
            fontFamily: 'PingFangSC, PingFang SC',
            fontWeight: '400',
            fontSize: '11px',
            color: '#73767f',
            textAlign: 'left',
            fontStyle: 'normal'
          },
          left: 0
        }
      ],
      legend: {
        type: 'scroll',
        data: [],
        top: 0,
        left: 'center',
        icon: 'rect',
        itemGap: 14,
        itemWidth: 10,
        itemHeight: 10,
        textStyle: {
          fontSize: '11px',
          color: '#73767f'
        }
      },
      xAxis: {
        type: 'category',
        data: [],
        show: true,
        axisTick: { alignWithLabel: true },
        axisLabel: {
          fontSize: 11,
          interval: 0,
          overflow: 'break',
          // color: '#73767f',
          color: '#73767f',
          // margin: 18,
          // rotate: props.chartData.rotateX || 0,
          // formatter(value) {
          //   let ret = ''
          //   let maxLength = 8
          //   for (let i = 0; i < value.length; i += maxLength) {
          //     ret += value.substring(i, i + maxLength) + '\n'
          //   }
          //   return ret
          // },
          formatter: function (params) {
            var newParamsName = '' // 最终拼接成的字符串
            var paramsNameNumber = params.length // 实际标签的个数
            var provideNumber = props.chartData.xLabelLineStrNum || 20 // 每行能显示的字的个数
            var rowNumber = Math.ceil(paramsNameNumber / provideNumber) // 换行的话，需要显示几行，向上取整
            if (paramsNameNumber > provideNumber) {
              for (var p = 0; p < rowNumber; p++) {
                var tempStr = '' // 表示每一次截取的字符串
                var start = p * provideNumber // 开始截取的位置
                var end = start + provideNumber // 结束截取的位置
                if (p == rowNumber - 1) {
                  // 最后一次不换行
                  tempStr = params.substring(start, paramsNameNumber)
                } else {
                  // 每一次拼接字符串并换行
                  tempStr = params.substring(start, end) + '\n'
                }
                newParamsName += tempStr // 最终拼成的字符串
              }
            } else {
              // 将旧标签的值赋给新标签
              newParamsName = params
            }
            //将最终的字符串返回
            return newParamsName
          }
        }
      },
      yAxis: [
        {
          type: 'value',
          alignTicks: true,
          axisLabel: {
            // 设置y轴刻度宽度
            fontSize: '11px',
            color: '#73767f'
          },
          name: `单位:${props.chartData.unitList.value1}`,
          nameTextStyle: {
            padding: props.type == 1 ? [0, 0, 5, 75] : [0, 0, 5, 50],
            fontSize: '11px',
            color: '#73767f'
          }
        }
      ],
      // dataZoom: [
      //   {
      //     show: !!props.chartData.showProp,
      //     type: 'slider',
      //     start: 0,
      //     end: props.chartData.showProp,
      //     height: 0,
      //     // top: 280,
      //     bottom: 15
      //   }
      // ],
      series: []
    })

    watchEffect(() => {
      !isEmpty(props.chartData) && initCharts()
    })

    function initCharts() {
      // x 轴
      let xAxisData = props.chartData.data.map((item) => {
        return item.name
      })
      option.xAxis.data = xAxisData
      if (props.type == 2) {
        option.yAxis[0].min = 0
      }
      if (props.chartData.column.length > 2) {
        option.yAxis[1] = {
          type: 'value',
          axisLabel: {
            width: 30, // 设置y轴刻度宽度
            fontSize: '11px',
            color: '#73767f'
          },
          name: `单位:${props.chartData.unitList.value2}`,
          nameTextStyle: {
            padding: [0, 40, 5, 0],
            fontSize: '11px',
            color: '#73767f'
          }
        }
      }
      let unit = []
      const unitList = props.chartData.unitList
      for (const key in unitList) {
        if (unitList[key]) {
          unit.push(unitList[key])
        }
      }
      // 设置 y 轴单位
      if (props.chartData.column.length > 2) {
        // option.title[1] = {
        //   textStyle: {
        //     fontFamily: 'PingFangSC, PingFang SC',
        //     fontWeight: '400',
        //     fontSize: '12px',
        //     color: '#999999',
        //     textAlign: 'right',
        //     fontStyle: 'normal'
        //   },
        //   right: 0
        // }
      }
      if (unit) {
        // option.yAxis.name = `单位：${unit}`
        // option.title.forEach((item, index) => {
        //   item.text = '单位:' + unit[index]
        // })
        // option.yAxis.forEach((item) => {
        //   item.nameTextStyle = {
        //     color: 'hsla(0, 0%, 40%, 1)',
        //     fontSize: 12
        //   }
        // })
        option.tooltip.valueFormatter = (value) => `${value}${unit}`
      }
      // series
      let seriesData = []
      // 获取排除 name 后的对象
      const valuesObj = omit(props.chartData.columnName, 'name')
      const entries = Object.entries(valuesObj)
      // series 不小于两个 显示图例
      if (entries.length > 1) {
        option.legend.data = entries.map(([, value]) => value)
        option.tooltip.formatter = (params) => {
          let result = `<div>${params[0].axisValue}</br></div>`
          params.forEach((item) => {
            result += `<div>
            <div style="height: 10px; width: 10px; border-radius: 50%; display: inline-block; background-color: ${
              props.chartData.colors[item.seriesIndex]
            }"></div>
            <span style="margin-right: 20px;">${item.seriesName}</span>
            <span style="font-weight: bold">${item.value}${unit[item.seriesIndex]}</span></br>
            </div>`
          })
          return result
          // return `${params[0].data}${unit[params[0].seriesIndex]}`
        }
      }
      for (let i = 0; i < entries.length; i++) {
        const [seriesKey, seriesName] = entries[i]
        let obj = {
          name: seriesName,
          type: props.chartData.type[i],
          barMaxWidth: 20,
          yAxisIndex: i,
          smooth: true,
          symbolSize: 8
        }
        obj.itemStyle = {
          color:
            obj.type === 'bar'
              ? new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: 'rgba(80, 167, 242, 1)' }, // 0% 处的颜色
                  // { offset: 0.5, color: '#2f4554' }, // 50% 处的颜色
                  { offset: 1, color: 'rgba(80, 167, 242, 0.20)' } // 100% 处的颜色
                ])
              : props.chartData.colors[i]
        }
        obj.data = props.chartData.data.map((item) => {
          return item[seriesKey]
        })
        obj.label = {
          show: obj.type === 'bar',
          position: 'top',
          fontWeight: '400',
          fontSize: '12px',
          color: 'black',
          padding: [0, 0, 0, 0]
        }
        seriesData.push(obj)
        // option.yAxis[i].max = Math.round(Math.max(...obj.data) * 1.1)
      }
      option.series = seriesData

      setOptions(option)
      let chart = getInstance()
      // chart?.on('click', (p) => {
      //   emit('choose', p)
      // })
      chart &&
        chart.getZr().on('click', (e) => {
          let pointInPixel = [e.offsetX, e.offsetY]
          if (chart.containPixel('grid', pointInPixel)) {
            let pointInGrid = chart.convertFromPixel(
              {
                seriesIndex: 0
              },
              pointInPixel
            )
            let xIndex = pointInGrid[0] //索引
            let handleIndex = Number(xIndex)
            let op = chart.getOption()
            emit('clickData', {
              name: op.xAxis[0].data[handleIndex],
              value1: op.series[0].data[handleIndex],
              value2: op.series[1].data[handleIndex]
            })
          }
        })
    }
    return { chartRef }
  }
}
</script>

<style lang="scss" scoped>
.text-container {
  transition: height 0.3s ease;
}

.text-1 {
  width: 100%;
  margin-top: 25px;
  z-index: 2;
  position: relative;
}

.text-2 {
  width: 100%;
  transition: transform 0.3s ease;
  transform: translateY(-100%);
  z-index: 1;
}

.text-2-show {
  width: 100%;
  transform: translateY(-10%);
}

.text-3 {
  width: 100%;
  transition: transform 0.5s ease;
  transform: translateY(-100%);
  z-index: 2;
}

.text-3-show {
  width: 100%;
  transform: translateY(-20%);
}

.text-4 {
  width: 100%;
  transition: transform 0.7s ease;
  transform: translateY(-100%);
  z-index: 2;
}

.text-4-show {
  width: 100%;
  transform: translateY(-20%);
}
</style>
