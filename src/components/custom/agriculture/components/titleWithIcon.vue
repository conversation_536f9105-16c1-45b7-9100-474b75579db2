<template>
  <div class="title-container">
    <div class="title">{{ title }}</div>
  </div>
</template>
<script>
export default {
  name: 'TitleWithIcon',
  props: {
    title: {
      type: String,
      default: () => ''
    },
    isPad: {
      type: Boolean,
      default: () => false
    }
  }
}
</script>
<style lang="scss" scoped>
.title-container {
  width: 100%;
  height: 50px;
  margin-top: 36px;
  .title {
    height: 100%;
    width: 100%;
    // font-family: PingFangSC, PingFang SC;
    // font-weight: bold;
    // font-size: 30px;
    // color: #333333;
    // line-height: 40px;
    // text-align: left;
    // font-style: normal;
    color: #212121;
    font-family: PingFang SC;
    font-weight: 500;
    font-size: 34px;
    line-height: 1;
  }
  .title::before {
    content: '';
    width: 12px;
    height: 31px;
    margin-right: 11px;
    display: inline-block;
    background: url('@/assets/img/homepage-title-icon.png') no-repeat;
    // background: red;
    background-size: 100% 100%;
    background-position-y: 50%;
  }
}
@media (min-width: 600px) {
  .title-container {
    width: 100%;
    height: 25px;
    margin-top: 0;
    .title {
      width: 100%;
      height: 100%;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 18px;
      color: #212121;
      line-height: 25px;
      text-align: left;
      font-style: normal;
    }
    .title::before {
      content: '';
      display: inline-block;
      width: 4px;
      margin-right: 6px;
      height: 16px;
      background-color: rgb(65, 118, 202);
    }
  }
}
</style>
