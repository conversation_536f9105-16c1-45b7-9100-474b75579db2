<template>
  <div v-if="!!data" class="card-box">
    <!-- <div class="time">{{ data.index_time }}</div> -->
    <popover
      v-if="!!options.length"
      :container="'.popover-vegetable'"
      :actions="options"
      :default-option="selectedOption"
      class="toppopover"
      @selectedOption="handleSelectedTime"
    ></popover>
    <div class="title">{{ data.index_name }}</div>
    <div class="value">
      <div class="left">
        <span>{{ data.index_value }}</span>
        <span>{{ data.index_unit }}</span>
      </div>
      <div class="right">
        <div>{{ data.index_name2 }}</div>
        <img v-if="data.index_value2 > 0" src="@/assets/img/arrow-up.png" alt="" />
        <img v-else src="@/assets/img/arrow-down.png" alt="" />
        <div :style="{ color: data.index_value2 > 0 ? '#dd2929' : '#43d593' }">{{ Math.abs(data.index_value2) }}</div>
        <div :style="{ color: data.index_value2 > 0 ? '#dd2929' : '#43d593' }">{{ data.index_unit2 }}</div>
      </div>
    </div>
    <div class="icon"></div>
  </div>
</template>
<script>
import { getAction } from '@/api/manage'
import popover from '@/components/selector.vue'

export default {
  components: {
    popover
  },
  data() {
    return {
      data: null,
      options: [],
      selectedOption: '',
      isPad: window.matchMedia('(min-width: 600px)').matches
    }
  },
  async mounted() {
    await this.getOptions()
    this.getData()
  },
  methods: {
    async getOptions() {
      const time = await getAction(
        `${import.meta.env.VITE_APP_SITUATION_BASE_URL}/topic/data/listAllBySql/ydd_nync_zyxqrdn_kp_time`
      )
      this.options = time.result.map((item) => {
        return {
          text: item.index_time
        }
      })
      this.selectedOption = this.options[0].text
    },
    async getData() {
      const res = await getAction(
        `${import.meta.env.VITE_APP_SITUATION_BASE_URL}topic/data/listAllBySql/ydd_nync_zyxqrdn_kp`,
        {
          index_time: this.selectedOption
        }
      )
      if (res.success) {
        this.data = res.result.filter((item) => item.index_time === this.selectedOption)[0]
      }
    },
    handleSelectedTime(time) {
      this.selectedOption = time
      this.getData()
    }
  }
}
</script>
<style lang="scss" scoped>
.card-box {
  width: 702px;
  height: 302px;
  background: url('../image/card-bg-5.png') no-repeat center center;
  background-size: 100% 100%;
  position: relative;
  .icon {
    height: 200px;
    width: 450px;
    background: url('../image/card-bottom-icon3.png') no-repeat center center / 100% 100%;
    position: absolute;
    bottom: 0;
    right: 0;
    z-index: 1;
  }
  // .time {
  //   font-family: SourceHanSansCN, SourceHanSansCN;
  //   font-weight: 400;
  //   font-size: 22px;
  //   color: #ffffff;
  //   text-align: center;
  //   margin-left: 11px;
  //   background: url('/src/assets/img/date_bg2.png') no-repeat center/100% 48px;
  //   align-items: center;
  //   position: absolute;
  //   display: flex;
  //   align-items: center;
  //   justify-content: center;
  //   right: -5px;
  //   width: 192px;
  //   height: 48px;
  //   top: 35px;
  //   padding-bottom: 8px;
  // }
  .title {
    position: absolute;
    top: 34px;
    left: 28px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 32px;
    color: #ffffff;
    line-height: 32px;
    text-align: left;
    font-style: normal;
  }
  .value {
    position: absolute;
    top: 120px;
    left: 28px;
    // display: flex;
    justify-content: space-between;
    align-items: baseline;
    z-index: 2;
    .left {
      span {
        &:nth-child(1) {
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 68px;
          color: #ffffff;
          line-height: 95px;
          text-align: left;
          font-style: normal;
        }
        &:nth-child(2) {
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 28px;
          color: #ffffff;
          line-height: 40px;
          text-align: left;
          font-style: normal;
          margin-left: 10px;
        }
      }
    }
    .right {
      display: flex;
      align-items: baseline;
      // margin-left: 100px;
      img {
        height: 30px;
        width: 20px;
        margin-left: 10px;
      }
      div {
        &:nth-child(1) {
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 28px;
          color: #ffffff;
          line-height: 40px;
          text-align: left;
          font-style: normal;
        }
        &:nth-child(3) {
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 48px;
          color: #dd2929;
          line-height: 48px;
          text-align: left;
          font-style: normal;
        }
        &:nth-child(4) {
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 32px;
          color: #dd2929;
          line-height: 32px;
          text-align: left;
          font-style: normal;
        }
      }
    }
  }
}
.toppopover {
  position: absolute;
  right: -5px;
  top: 20px;
  z-index: 80;
}
::v-deep {
  .van-button {
    width: calc(var(--base-size) * 20);
    height: calc(var(--base-size) * 6);
    font-size: calc(var(--base-size) * 2.6);
  }
  .van-popover__content {
    overflow: scroll;
    max-height: 300px;
    width: 200px;
    border-radius: 10px;
  }
  .van-popover__action {
    font-size: calc(var(--base-size) * 2.4);
    line-height: calc(var(--base-size) * 6);
    width: 100%;
    height: 60px;
    padding: 8px;
  }
  .el-input {
    width: 230px;
  }
  .el-input__inner {
    // background-color: transparent;
    background: url('/src/assets/img/date_bg.png') no-repeat center/100% calc(var(--base-size) * 5.6);
    color: white;
    border: 0;
    text-align: center;
    font-size: calc(var(--base-size) * 2.4);
    // padding-bottom: 5px;
  }
}
</style>
