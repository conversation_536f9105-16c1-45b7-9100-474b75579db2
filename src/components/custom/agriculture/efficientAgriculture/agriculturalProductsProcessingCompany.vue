<template>
  <div class="total-container">
    <div v-if="!!data" class="bottom-container">
      <div class="popover-10" @click="addBg($event, options)">
        <popover
          :container="'.popover-10'"
          :actions="options"
          :default-option="selectedOption"
          @selectedOption="handleSelectedTime"
        ></popover>
      </div>
      <div class="left">
        <div class="title">{{ data[0].index_name }}</div>
        <div class="value">
          <span class="num">{{ data[0].value }}</span>
          <span class="unit">{{ data[0].unit }}</span>
        </div>
      </div>
      <div class="right">
        <div class="title">{{ data[1].index_name }}</div>
        <div class="value">
          <div class="num">{{ data[1].value }}</div>
          <div class="unit">{{ data[1].unit }}</div>
        </div>
      </div>
      <img src="../image/xdgxny-bar.png" />
    </div>
  </div>
</template>
<script>
import popover from '@/components/selector.vue'

import titleWithIcon from '../components/titleWithIcon.vue'
import { getAction } from '@/api/manage'

import pieChart from '../components/pieChart.vue'
export default {
  components: {
    popover,
    titleWithIcon,
    pieChart
  },
  inject: ['addBg'],
  props: {
    padSelectedOption: {
      type: String,
      default: () => ''
    }
  },
  data() {
    return {
      data: null,
      options: [],
      selectedOption: '',
      isPad: window.matchMedia('(min-width: 600px)').matches,
      topicId: '',
      topicCode: 'A38A10',
      topicOrder: 0
    }
  },
  watch: {
    padSelectedOption: {
      handler(val) {
        this.selectedOption = val
        this.getData()
      }
    }
  },
  async mounted() {
    const topic = this.$store.state.DataSourceList.filter((item) => item.topicCode == this.topicCode)[0]
    this.topicId = topic.topicId
    this.topicOrder = topic.order
    await this.getTime()
    this.getData()
  },
  methods: {
    async getTime() {
      const time = await getAction(
        `${import.meta.env.VITE_APP_SITUATION_BASE_URL}/topic/data/listAllBySql/ydd_nync_xdgxnygk_jgqy_time`
      )
      this.options = time.result.map((item) => {
        return {
          text: item.index_time
        }
      })
      this.selectedOption = this.options[0].text
    },
    async getData() {
      const res = await getAction(
        `${import.meta.env.VITE_APP_SITUATION_BASE_URL}/topic/data/listAllBySql/ydd_nync_xdgxnygk_jgqy?indexTime=${
          this.selectedOption
        }`
      )
      this.data = [...res.result]
    },
    getChangeTime() {
      getAction(
        `${import.meta.env.VITE_APP_SITUATION_BASE_URL}/topic/data/listAllBySql/ydd_topic_date_switch_dropdown`,
        {
          id: this.topicId,
          name: this.selectedOption,
          indexTime: this.selectedOption
        }
      ).then((res) => {
        if (res) {
          //index用于父组件修改哪个子组件下的数据来源
          //其他的参数是该组件获取四组数据,让父组件修改该组件的四组数据
          const result = res.result[0]
          this.$eventBus.$emit('changeTime', {
            dpDataTime: result.dp_data_time,
            dataSource: result.data_source,
            frequency: result.frequency,
            stSyncTime: result.st_sync_time,
            index: this.topicOrder
          })
          const newobj = {
            value1: result.data_source,
            value2: result.st_sync_time,
            value3: result.frequency,
            value4: result.dp_data_time
          }
          this.$emit('selectedOption', newobj)
        }
      })
    },
    handleSelectedTime(time) {
      this.selectedOption = time
      this.getChangeTime()
      this.getData()
    }
  }
}
</script>
<style lang="scss" scoped>
.bottom-container {
  width: 100%;
  height: 250px;
  margin-top: 20px;
  background: url('../image/lscl-icon3.png') no-repeat center center / 100% 100%;
  position: relative;
  .popover-10 {
    position: absolute;
    right: 20px;
    top: 20px;
  }
  > img {
    width: 50%;
    position: absolute;
    right: 0;
    top: 150px;
  }
  .title {
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 30px;
    color: #212121;
    line-height: 30px;
    text-align: left;
    font-style: normal;
  }
  .left {
    position: absolute;
    top: 30px;
    left: 20px;
    .value {
      margin-top: 60px;
    }
    .num {
      font-family: PingFangSC, PingFang SC;
      font-weight: 600;
      font-size: 45px;
      color: #3b93fb;
      line-height: 45px;
      text-align: justify;
      font-style: normal;
    }
    .unit {
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 23px;
      color: #666666;
      line-height: 23px;
      text-align: left;
      font-style: normal;
      margin-left: 3px;
    }
  }
  .right {
    position: absolute;
    top: 100px;
    left: 360px;
    display: flex;
    align-items: center;

    .value {
      display: flex;
      align-items: baseline;
      // margin-top: 40px;
      img {
        height: 36px;
        width: 24px;
      }
      .num {
        font-family: PingFangSC, PingFang SC;
        font-weight: 600;
        font-size: 45px;
        color: #3b93fb;
        line-height: 45px;
        text-align: justify;
        font-style: normal;
      }
      .unit {
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 23px;
        color: #666666;
        line-height: 23px;
        text-align: left;
        font-style: normal;
        margin-left: 3px;
      }
    }
  }
}

::v-deep {
  .van-button {
    // width: 260px;
    padding: 10px 20px;
    height: 60px;
    font-size: 26px;
  }
  .van-popover__content {
    overflow: scroll;
    max-height: 300px;
    width: 280px;
    border-radius: 10px;
  }
  .van-popover__action {
    font-size: 24px;
    line-height: 60px;
    width: 100%;
    height: 60px;
    padding: 8px;
  }
}
::v-deep {
  .el-input {
    width: 290px;
  }
}
</style>
