<template>
  <div class="total-container">
    <div v-if="!!cardData" class="card-box">
      <popover
        v-if="!!options.length"
        :container="'.popover-efficient'"
        :actions="options"
        :default-option="selectedOption"
        class="toppopover"
        @selectedOption="handleSelectedTime"
      ></popover>
      <div v-if="!isPad" class="vertical-line" :class="{ line2: cardData.length === 2 }"></div>
      <div v-if="!isPad && cardData.length === 3" class="horizontal-line"></div>
      <div class="flex-container">
        <div v-for="item in cardData" :key="item.id" class="item-container">
          <div class="row-1">{{ item.index_name }}</div>
          <div class="row-2" :class="!item.name2.length ? 'row-2-type1' : 'row-2-type2'">
            <div>
              <span>{{ item.name1 }}</span>
              <span>{{ item.value1 }}</span>
              <span>{{ item.unit1 }}</span>
            </div>
            <div v-if="!!item.name2.length">
              <span>{{ item.name2 }}</span>
              <span>{{ item.value2 }}</span>
              <span>{{ item.unit2 }}</span>
            </div>
          </div>
          <div class="row-3">
            <span>{{ item.name3 }}</span>
            <span>{{ item.value3 }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import titleWithIcon from '../components/titleWithIcon.vue'
import popover from '@/components/selector.vue'
import { getAction } from '@/api/manage'

export default {
  components: {
    titleWithIcon,
    popover
  },
  inject: ['addBg'],
  data() {
    return {
      cardData: null,
      options: [],
      selectedOption: '',
      isPad: window.matchMedia('(min-width: 600px)').matches
    }
  },

  async mounted() {
    await this.getOptions()
    this.getCardData()
  },
  methods: {
    async getOptions() {
      const time = await getAction(
        `${import.meta.env.VITE_APP_SITUATION_BASE_URL}/topic/data/listAllBySql/ydd_nync_xdgxnygk_kp_time`
      )
      this.options = time.result.map((item) => {
        return {
          text: item.index_time
        }
      })
      this.selectedOption = this.options[0].text
    },
    async getCardData() {
      const res = await getAction(
        `${import.meta.env.VITE_APP_SITUATION_BASE_URL}/topic/data/listAllBySql/ydd_nync_xdgxnygk_kp?indexTime=${
          this.selectedOption
        }`
      )
      this.cardData = [...res.result]
    },
    handleSelectedTime(time) {
      this.selectedOption = time
      this.getCardData()
    }
  }
}
</script>
<style lang="scss" scoped>
.total-container {
  .card-box {
    position: relative;
    width: 702px;
    height: 450px;
    background: url('../image/card-bg-3.png') no-repeat center center;
    background-size: 100% 100%;
    display: flex;
    align-items: center;
    .vertical-line {
      position: absolute;
      top: 70px;
      left: calc(50% + 2px);
      height: 40%;
      border: 1px solid;
      border-image: linear-gradient(
          180deg,
          rgba(255, 255, 255, 0),
          rgba(255, 255, 255, 0.63),
          rgba(255, 255, 255, 1),
          rgba(255, 255, 255, 0.62),
          rgba(255, 255, 255, 0)
        )
        2 2;
      &.line2 {
        top: 170px;
        left: calc(50% + 6px);
      }
    }
    .horizontal-line {
      position: absolute;
      top: calc(50% + 30px);
      width: 90%;
      height: 1px;
      background: linear-gradient(to right, #5c8bd4 0%, #f1f4fa 50%, #5c8bd4 100%) center / 100% no-repeat;
    }
    .toppopover {
      position: absolute;
      right: -5px;
      top: 20px;
    }
    .flex-container {
      display: flex;
      justify-content: space-around;
      align-items: center;
      flex-wrap: wrap;
      // position: absolute;
      // top: 70px;
      width: 100%;
      margin-top: 60px;
    }
    .item-container {
      width: 45%;
      .row-1 {
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 28px;
        color: #ffffff;
        line-height: 45px;
        text-align: center;
        font-style: normal;
      }
      .row-2 {
        display: flex;
        justify-content: center;
        text-align: center;
        margin: 10px auto;
        span {
          &:nth-of-type(1),
          &:nth-of-type(3) {
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 28px;
            color: #ffffff;
            line-height: 40px;
            text-align: left;
            font-style: normal;
          }
          &:nth-of-type(2) {
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 48px;
            color: #ffffff;
            line-height: 48px;
            text-align: left;
            font-style: normal;
            margin: 0 5px;
          }
        }
        &.row-2-type1 {
          height: 80px;
          width: 250px;
          background: url('../image/xdgxny-card-icon2.png') no-repeat center center / 100% 100%;
          padding: 20px;
        }
        &.row-2-type2 {
          height: 80px;
          width: 330px;
          background: url('../image/xdgxny-card-icon1.png') no-repeat center center / 100% 100%;
          padding: 20px;
          justify-content: space-around;
          span:nth-of-type(2) {
            font-size: 48px;
          }
        }
      }
      .row-3 {
        text-align: center;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 28px;
        line-height: 40px;
        text-align: center;
        font-style: normal;
        span:nth-child(1) {
          color: #ffffff;
        }
        span:nth-child(2) {
          color: #f63146;
        }
      }
    }
  }
}
::v-deep {
  .van-button {
    width: calc(var(--base-size) * 20);
    height: calc(var(--base-size) * 6);
    font-size: calc(var(--base-size) * 2.6);
  }
  .van-popover__content {
    overflow: scroll;
    max-height: 300px;
    width: 200px;
    border-radius: 10px;
  }
  .van-popover__action {
    font-size: calc(var(--base-size) * 2.4);
    line-height: calc(var(--base-size) * 6);
    width: 100%;
    height: 60px;
    padding: 8px;
  }
  .el-input {
    width: 280px;
  }
  .el-input__inner {
    // background-color: transparent;
    background: url('/src/assets/img/date_bg2.png') no-repeat center/100% calc(var(--base-size) * 5.6);
    color: white;
    border: 0;
    text-align: center;
    font-size: calc(var(--base-size) * 2.4);
    padding-bottom: 5px;
  }
}
</style>
