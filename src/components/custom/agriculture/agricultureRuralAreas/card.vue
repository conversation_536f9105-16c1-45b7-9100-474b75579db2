<template>
  <!-- 全省农业农村概况 -->
  <div v-if="dataBelow" class="total-container">
    <div class="card-box">
      <div class="time">{{ selectedOption }}</div>
      <div class="card-box-below">
        <div class="vertical-line"></div>
        <div v-if="dataBelow && dataBelow.length" class="grid-container">
          <div v-for="item in dataBelow" :key="item.id" class="item-box">
            <!-- <img class="left-icon" :src="imgPath[index]" /> -->
            <div class="right-content">
              <div class="title">{{ item.field }}</div>
              <div class="value">
                <span class="num">{{ item.field_value }}</span>
                <span class="unit">{{ item.unit }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { getProvinceAgriculture } from '@/api/agriculture'
import titleWithIcon from '../components/titleWithIcon.vue'
import popover from '@/components/selector.vue'
import { getDataProperty } from '@/api/getDataProperty'
import { getAction } from '@/api/manage'

export default {
  components: {
    titleWithIcon,
    popover
  },
  inject: ['addBg'],
  data() {
    return {
      data: null,
      dataBelow: null,
      options: [],
      selectedOption: '2023年',
      // sourceData: [],
      topicId: '',
      topicCode: 'A38A02',
      topicSort: 0
    }
  },

  async mounted() {
    const topic = this.$store.state.DataSourceList.filter((item) => item.topicCode == this.topicCode)[0]
    this.topicId = topic.topicId
    this.topicOrder = topic.order
    // await this.getOptions()
    this.getCardBelowData()
    // this.sourceData = getDataProperty(this.$store.state.DataSourceList, [1])[0]
  },
  methods: {
    // async getOptions() {
    //   const options = await getAction(
    //     import.meta.env.VITE_APP_SITUATION_BASE_URL + '/topic/data/listAllBySql/ydd_nync_qsnyncqk_time',
    //     {}
    //   ).then((res) => {
    //     if (res.success) {
    //       this.options = res.result.map((item) => {
    //         return {
    //           text: item.index_time
    //         }
    //       })
    //       this.selectedOption = this.options[0].text
    //     }
    //   })
    // },
    async getCardBelowData() {
      const res = await getProvinceAgriculture(this.selectedOption)
      this.dataBelow = [...res.result]
    }
    // handleSelectedTime(time) {
    //   this.selectedOption = time
    //   this.getCardBelowData()
    //   getAction(
    //     `${import.meta.env.VITE_APP_SITUATION_BASE_URL}/topic/data/listAllBySql/ydd_topic_date_switch_dropdown`,
    //     {
    //       id: this.topicId,
    //       name: this.selectedOption,
    //       indexTime: this.selectedOption
    //     }
    //   ).then((res) => {
    //     if (res) {
    //       const result = res.result[0]
    //       // this.$eventBus.$emit('changeTime', {
    //       //   dpDataTime: result.dp_data_time,
    //       //   dataSource: result.data_source,
    //       //   frequency: result.frequency,
    //       //   stSyncTime: result.st_sync_time,
    //       //   index: this.topicOrder
    //       // })
    //       const newobj = {
    //         value1: result.data_source,
    //         value2: result.st_sync_time,
    //         value3: result.frequency,
    //         value4: result.dp_data_time
    //       }
    //       this.$emit('selectedOption', newobj)
    //     }
    //   })
    // }
  }
}
</script>
<style lang="scss" scoped>
.total-container {
  .card-box {
    width: 702px;
    height: 352px;
    background: url('../image/card-bg-1.png') no-repeat center center;
    background-size: 100% 100%;
    position: relative;
    .toppopover {
      position: absolute;
      right: -5px;
      top: 20px;
    }

    .time {
      font-family: SourceHanSansCN, SourceHanSansCN;
      font-weight: 400;
      font-size: 24px;
      color: #ffffff;
      text-align: center;
      margin-left: 13px;
      background: url('/src/assets/img/date_bg3.png') no-repeat center/100% 56px;
      align-items: center;
      position: absolute;
      display: flex;
      align-items: center;
      justify-content: center;
      right: -5px;
      width: 192px;
      height: 56px;
      top: 25px;
      padding-bottom: 8px;
    }
  }
  .vertical-line {
    position: absolute;
    top: 70px;
    height: 120px;
    border: 1px solid;
    border-image: linear-gradient(
        180deg,
        rgba(255, 255, 255, 0),
        rgba(255, 255, 255, 0.63),
        rgba(255, 255, 255, 1),
        rgba(255, 255, 255, 0.62),
        rgba(255, 255, 255, 0)
      )
      2 2;
  }
  .card-box-below {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    width: 100%;
    padding-top: 50px;
    position: relative;
    .grid-container {
      position: relative;
      display: grid;
      flex: 1;
      grid-template-columns: auto auto;
      justify-content: space-around;
      row-gap: 25px;
      > .item-box {
        width: 300px;
        display: flex;
        justify-content: center;
        align-items: center;
        background: unset !important;
        .left-icon {
          height: 80px;
          width: 80px;
        }
        .right-content {
          margin-left: 14px;
          .title {
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 32px;
            color: white;
            line-height: 45px;
            text-align: center;
            font-style: normal;
          }
          .value {
            text-align: center;
          }
          .num {
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 48px;
            color: #ffffff;
            line-height: 48px;
            text-align: left;
            font-style: normal;
          }
          .unit {
            margin-left: 12px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 24px;
            color: white;
            line-height: 33px;
            text-align: left;
            font-style: normal;
          }
        }
        &:nth-of-type(3) {
          width: revert !important;
          grid-column: 1 / 3;
        }
      }
      .item-box:first-of-type {
        // padding-right: 60px;
        margin-right: 30px;
      }
    }
    .grid-container::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 30px;
      height: 1px;
      width: 674px;
      border: 1px solid;
      border-image: linear-gradient(
          90deg,
          rgba(255, 255, 255, 0),
          rgba(255, 255, 255, 0.63),
          rgba(255, 255, 255, 1),
          rgba(255, 255, 255, 0.62),
          rgba(255, 255, 255, 0)
        )
        2 2;
    }
  }
}
::v-deep {
  .van-button {
    width: calc(var(--base-size) * 20);
    height: calc(var(--base-size) * 6);
    font-size: calc(var(--base-size) * 2.6);
  }
  .van-popover__content {
    overflow: scroll;
    max-height: 300px;
    width: 200px;
    border-radius: 10px;
  }
  .van-popover__action {
    font-size: calc(var(--base-size) * 2.4);
    line-height: calc(var(--base-size) * 6);
    width: 100%;
    height: 60px;
    padding: 8px;
  }
  .el-input {
    width: 270px;
  }
  .el-input__inner {
    // background-color: transparent;
    background: url('/src/assets/img/date_bg3.png') no-repeat center/100% calc(var(--base-size) * 5.6);
    color: white;
    border: 0;
    padding-bottom: 5px;
    text-align: center;
    font-size: calc(var(--base-size) * 2.4);
    // padding-bottom: 5px;
  }
}
</style>
<style>
.el-select-dropdown {
  border: 0;
}
.el-select-dropdown {
  background-color: rgba(255, 255, 255, 0.9);
}
</style>
