<template>
  <div class="total-container">
    <div v-if="data" class="flex-container">
      <div class="item-box">
        <div class="title">{{ data[1].field_name }}</div>
        <div class="value">
          <span class="num">{{ data[1].field_value }}</span>
          <span class="unit">{{ data[1].unit }}</span>
        </div>
      </div>
      <div class="item-box item-box2">
        <div class="title">{{ data[0].field_name }}</div>
        <div class="value">
          <img src="@/assets/img/arrow-up.png" alt="" />
          <span class="num">{{ data[0].field_value }}</span>
          <span class="unit">{{ data[0].unit }}</span>
        </div>
      </div>
      <div class="popover-2" @click="addBg($event, options)">
        <popover
          :container="'.popover-2'"
          :actions="options"
          :default-option="selectedOption"
          @selectedOption="handleSelectedTime"
        ></popover>
      </div>
    </div>
  </div>
</template>
<script>
import popover from '@/components/selector.vue'
import { getAction } from '@/api/manage'
export default {
  components: {
    popover
  },
  inject: ['addBg'],
  data() {
    return {
      data: null,
      options: [],
      selectedOption: '',
      topicId: '',
      topicCode: 'A38A02',
      topicSort: 0
    }
  },

  async mounted() {
    const topic = this.$store.state.DataSourceList.filter((item) => item.topicCode == this.topicCode)[0]
    this.topicId = topic.topicId
    this.topicOrder = topic.order
    await this.getTime()
    this.getData()
  },
  methods: {
    async getTime() {
      const time = await getAction(
        `${import.meta.env.VITE_APP_SITUATION_BASE_URL}/topic/data/listAllBySql/ydd_nync_qsncrjsr_time`
      )
      this.options = time.result.map((item) => {
        return {
          text: item.index_time
        }
      })
      this.selectedOption = this.options[0].text
    },
    async getData() {
      const res = await getAction(
        `${import.meta.env.VITE_APP_SITUATION_BASE_URL}/topic/data/listAllBySql/ydd_nync_qsncrjsr`,
        {
          indexTime: this.selectedOption
        }
      )
      this.data = res.result.reverse()
    },
    getChangeTime() {
      getAction(
        `${import.meta.env.VITE_APP_SITUATION_BASE_URL}/topic/data/listAllBySql/ydd_topic_date_switch_dropdown`,
        {
          id: this.topicId,
          name: this.selectedOption,
          indexTime: this.selectedOption
        }
      ).then((res) => {
        if (res) {
          //index用于父组件修改哪个子组件下的数据来源
          //其他的参数是该组件获取四组数据,让父组件修改该组件的四组数据
          const result = res.result[0]
          this.$eventBus.$emit('changeTime', {
            dpDataTime: result.dp_data_time,
            dataSource: result.data_source,
            frequency: result.frequency,
            stSyncTime: result.st_sync_time,
            index: this.topicOrder
          })
          //供pad使用
          const newobj = {
            value1: result.data_source,
            value2: result.st_sync_time,
            value3: result.frequency,
            value4: result.dp_data_time
          }
          this.$emit('selectedOption', newobj)
        }
      })
    },
    handleSelectedTime(time) {
      this.selectedOption = time
      this.getChangeTime()
      this.getData()
      this.$emit('aaa', 222)
    }
  }
}
</script>
<style lang="scss" scoped>
.total-container {
  margin-top: -10px;
  position: relative;
  .popover-2 {
    position: absolute;
    top: 20px;
    right: 20px;
  }
  .flex-container {
    width: 100%;
    margin-top: 30px;
    display: flex;
    justify-content: space-around;
    align-items: center;
    height: 230px;
    background: url('@/components/custom/obtainEmployment/assets/splitimg.png') center/100% 100% no-repeat;
    > .item-box {
      width: 100%;
      height: 194px;
      border-radius: 16px;
      margin-right: 10px;
      display: flex;
      flex-direction: column;
      align-items: center;
      .title {
        margin-top: 8px;
        padding: 0 15px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 32px;
        color: #212121;
        line-height: 45px;
        text-align: center;
        font-style: normal;
      }
      .value {
        text-align: center;
        display: flex;
        align-items: baseline;
        img {
          width: 28px;
        }
      }
      .num {
        font-family: PingFangSC, PingFang SC;
        font-weight: 600;
        font-size: 48px;
        color: #3b93fb;
        line-height: 48px;
        text-align: center;
        font-style: normal;
      }
      .unit {
        margin-left: 3px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 24px;
        color: #666666;
        line-height: 28px;
        text-align: center;
        font-style: normal;
      }
      .value {
        margin-top: 20px;
      }
    }
    .item-box:nth-child(2) {
      height: revert;
      flex-direction: row;
      padding-top: 80px;
      .value {
        margin-top: 0;
      }
      .num,
      .unit {
        color: rgb(246, 49, 70);
      }
    }
  }
}
::v-deep {
  .van-button {
    width: 260px;
    height: 60px;
    font-size: 26px;
  }
  .van-popover__content {
    overflow: scroll;
    max-height: 300px;
    width: 280px;
    border-radius: 10px;
  }
  .van-popover__action {
    font-size: 24px;
    line-height: 60px;
    width: 100%;
    height: 60px;
    padding: 8px;
  }
}
::v-deep {
  .el-input {
    width: 290px;
  }
}
</style>
