<template>
  <div>
    <div class="chart-container">
      <barLine :chart-data="chartData" :type="2"></barLine>
    </div>
    <orderlist></orderlist>
  </div>
</template>
<script>
import orderlist from './orderlist.vue'
import { getAction } from '@/api/manage'
import barLine from '../components/barLine.vue'
export default {
  components: { barLine, orderlist },
  data() {
    return {
      chartData: {
        data: [],
        column: ['name', 'value1', 'value2'],
        columnName: { name: '', value1: '农产品出口', value2: '同比增速' },
        unitList: { name: '', value1: '亿元', value2: '%' },
        type: ['bar', 'line'],
        colors: ['#0192f8', '#39c56a'],
        xLabelLineStrNum: 5
        // rotateX: 45
        // 一次显示条数占总数百分比
        // showProp: 30
      }
    }
  },
  mounted() {
    this.getChartData()
  },
  methods: {
    async getChartData() {
      const res = await getAction(
        `${import.meta.env.VITE_APP_SITUATION_BASE_URL}/topic/data/listAllBySql/ydd_nync_ncpckgk_qs`
      )
      if (res.success)
        this.chartData.data = res.result.map((item) => {
          return {
            name: item.index_time,
            value1: item.ck,
            value2: item.yoy
          }
        })
    }
  }
}
</script>
<style lang="scss" scoped>
.chart-container {
  margin-top: -24px;
  height: 500px;
  div {
    height: 100%;
  }
  ::v-deep .echarts-container {
    height: 100%;
  }
}
</style>
