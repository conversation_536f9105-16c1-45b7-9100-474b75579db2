<template>
  <div class="total-container">
    <div v-if="!!cardData" class="card-box">
      <popover
        v-if="!!options.length"
        :container="'.popover-export'"
        :actions="options"
        :default-option="selectedOption"
        class="toppopover"
        @selectedOption="handleSelectedTime"
      ></popover>
      <div class="title">{{ cardData[0].index_name }}</div>
      <span class="value">
        <span>{{ cardData[0].value }}</span>
        <span>{{ cardData[0].unit }}</span>
      </span>
      <div class="rate-value">
        <div :class="!!cardData[2] ? 'rate-1' : 'rate-2'">
          <span class="title">{{ cardData[1].index_name }}</span>
          <img v-if="!cardData[2]" src="@/assets/img/arrow-up.png" alt="" />
          <div v-if="!!cardData[2]" :style="{ display: cardData[1].index_name === '占全国' ? 'inline' : 'block' }">
            <span class="num">{{ cardData[1].value }}</span>
            <span class="unit">{{ cardData[1].unit }}</span>
          </div>
          <span v-if="!cardData[2]" class="num">{{ cardData[1].value }}</span>
          <span v-if="!cardData[2]" class="unit">{{ cardData[1].unit }}</span>
        </div>
        <div v-if="!!cardData[2]" class="rate-2">
          <span>{{ cardData[2].index_name }}</span>
          <img src="@/assets/img/arrow-up.png" alt="" />
          <span>{{ cardData[2].value }}</span>
          <span>{{ cardData[2].unit }}</span>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { getAction } from '@/api/manage'

import titleWithIcon from '../components/titleWithIcon.vue'
import popover from '@/components/selector.vue'
export default {
  components: {
    titleWithIcon,
    popover
  },
  inject: ['addBg'],
  data() {
    return {
      cardData: null,
      options: [],
      selectedOption: ''
    }
  },
  async mounted() {
    await this.getOptions()
    this.getCardData()
  },
  methods: {
    async getOptions() {
      const time = await getAction(
        `${import.meta.env.VITE_APP_SITUATION_BASE_URL}/topic/data/listAllBySql/ydd_nync_ncpckgk_kp_time`
      )
      this.options = time.result.map((item) => {
        return {
          text: item.index_time
        }
      })
      this.selectedOption = this.options[0].text
    },
    async getCardData() {
      const res = await getAction(
        `${import.meta.env.VITE_APP_SITUATION_BASE_URL}/topic/data/listAllBySql/ydd_nync_ncpckgk_kp?indexTime=${
          this.selectedOption
        }`
      )
      this.cardData = [...res.result]
    },
    handleSelectedTime(time) {
      this.selectedOption = time
      this.getCardData()
    }
    // getChangeTime() {
    //   getAction(
    //     `${import.meta.env.VITE_APP_SITUATION_BASE_URL}/topic/data/listAllBySql/ydd_topic_date_switch_dropdown`,
    //     {
    //       id: this.topicId,
    //       name: this.selectedOption.selectedOption1,
    //       indexTime: this.selectedOption.selectedOption1
    //     }
    //   ).then((res) => {
    //     if (res) {
    //       //index用于父组件修改哪个子组件下的数据来源
    //       //其他的参数是该组件获取四组数据,让父组件修改该组件的四组数据
    //       const result = res.result[0]
    //       this.$eventBus.$emit('changeTime', {
    //         dpDataTime: result.dp_data_time,
    //         dataSource: result.data_source,
    //         frequency: result.frequency,
    //         stSyncTime: result.st_sync_time,
    //         index: 6
    //       })
    //       //供pad使用
    //       const newobj = {
    //         value1: result.data_source,
    //         value2: result.st_sync_time,
    //         value3: result.frequency,
    //         value4: result.dp_data_time
    //       }
    //       this.$emit('selectedOption', newobj)
    //     }
    //   })
    // },
  }
}
</script>

<style lang="scss" scoped>
.total-container {
  .card-box {
    overflow: hidden;
    width: 702px;
    height: 302px;
    background: url('../image/card-bg-2.png') no-repeat center center;
    background-size: 100% 100%;
    position: relative;
    .toppopover {
      position: absolute;
      right: -5px;
      top: 20px;
    }
    > .title {
      position: absolute;
      top: 34px;
      left: 28px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 32px;
      color: #ffffff;
      line-height: 32px;
      text-align: left;
      font-style: normal;
    }
    > .value {
      position: absolute;
      top: 140px;
      left: 28px;
      span:nth-child(1) {
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 68px;
        color: #ffffff;
        line-height: 95px;
        text-align: left;
        font-style: normal;
      }
      span:nth-child(2) {
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 28px;
        color: #ffffff;
        line-height: 40px;
        text-align: left;
        font-style: normal;
        margin-left: 10px;
      }
    }
    .rate-value {
      position: absolute;
      right: -20px;
      top: 80px;
      width: 460px;
      height: 200px;
      background: url('../image/ncpck-card-icon.png') no-repeat left center / 120% 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      padding-left: 70px;
      justify-content: space-around;
      padding-top: 40px;
      padding-bottom: 20px;
    }
    .rate-1 {
      text-align: center;
      span {
        &:nth-of-type(1) {
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 36px;
          color: #ffffff;
          line-height: 36px;
          text-align: left;
          font-style: normal;
        }
        &:nth-of-type(2) {
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 20px;
          color: #ffffff;
          line-height: 20px;
          text-align: left;
          font-style: normal;
        }
        &.title {
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 28px;
          color: #ffffff;
          line-height: 28px;
          text-align: left;
          font-style: normal;
        }
      }
    }
    .rate-2 {
      display: flex;
      align-items: baseline;
      img {
        height: 30px;
        width: 20px;
        margin-left: 10px;
      }
      span {
        &:nth-of-type(2) {
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 48px;
          color: #f63146;
          line-height: 48px;
          text-align: left;
          font-style: normal;
        }
        &:nth-of-type(3) {
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 32px;
          color: #f63146;
          line-height: 32px;
          text-align: left;
          font-style: normal;
        }
        &:nth-of-type(1) {
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 28px;
          color: #ffffff;
          line-height: 28px;
          text-align: left;
          font-style: normal;
        }
      }
    }
  }
}
::v-deep {
  .van-button {
    width: calc(var(--base-size) * 20);
    height: calc(var(--base-size) * 6);
    font-size: calc(var(--base-size) * 2.6);
  }
  .van-popover__content {
    overflow: scroll;
    max-height: 300px;
    width: 200px;
    border-radius: 10px;
  }
  .van-popover__action {
    font-size: calc(var(--base-size) * 2.4);
    line-height: calc(var(--base-size) * 6);
    width: 100%;
    height: 60px;
    padding: 8px;
  }
  .el-input {
    width: 230px;
  }
  .el-input__inner {
    // background-color: transparent;
    background: url('/src/assets/img/date_bg2.png') no-repeat center/100% calc(var(--base-size) * 5.6);
    color: white;
    border: 0;
    padding-bottom: 8px;
    text-align: center;
    font-size: calc(var(--base-size) * 2.4);
    // padding-bottom: 5px;
  }
}
</style>
