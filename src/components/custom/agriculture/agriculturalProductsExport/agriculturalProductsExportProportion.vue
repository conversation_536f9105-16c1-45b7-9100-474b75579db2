<template>
  <div class="total-container">
    <div class="popover-8" @click="addBg($event, options)">
      <popover
        :container="'.popover-8'"
        :actions="options"
        :default-option="selectedOption"
        @selectedOption="handleSelectedTime"
      ></popover>
    </div>
    <div class="chart-container">
      <pieChart :chart-data="chartData"></pieChart>
    </div>
  </div>
</template>
<script>
import { getTime, getAgriculturalProductsExportProportion } from '@/api/agriculture'
import popover from '@/components/selector.vue'
import { getAction } from '@/api/manage'
import pieChart from '../components/pieChart.vue'
export default {
  components: {
    popover,
    pieChart
  },
  inject: ['addBg'],
  data() {
    return {
      chartData: null,
      options: [],
      selectedOption: '',
      topicId: ''
    }
  },
  async mounted() {
    this.topicId = this.$store.state.DataSourceList[7].topicId
    await this.getTime()
    this.getData()
  },
  methods: {
    async getTime() {
      const res = await getTime.time8()
      this.options = res.result.map((item) => {
        return {
          text: item.index_time
        }
      })
      this.selectedOption = this.options[0].text
    },
    async getData() {
      const res = await getAgriculturalProductsExportProportion(this.selectedOption)
      this.chartData = res.result.map((item) => {
        return {
          name: item.field_name,
          value: item.field_value
        }
      })
    },
    getChangeTime() {
      getAction(
        `${import.meta.env.VITE_APP_SITUATION_BASE_URL}/topic/data/listAllBySql/ydd_topic_date_switch_dropdown`,
        {
          id: this.topicId,
          name: this.selectedOption,
          indexTime: this.selectedOption
        }
      ).then((res) => {
        if (res) {
          //index用于父组件修改哪个子组件下的数据来源
          //其他的参数是该组件获取四组数据,让父组件修改该组件的四组数据
          const result = res.result[0]
          this.$eventBus.$emit('changeTime', {
            dpDataTime: result.dp_data_time,
            dataSource: result.data_source,
            frequency: result.frequency,
            stSyncTime: result.st_sync_time,
            index: 7
          })
          //供pad使用
          const newobj = {
            value1: result.data_source,
            value2: result.st_sync_time,
            value3: result.frequency,
            value4: result.dp_data_time
          }
          this.$emit('selectedOption', newobj)
        }
      })
    },
    handleSelectedTime(time) {
      this.selectedOption = time
      this.getChangeTime()

      this.getData()
    }
  }
}
</script>
<style lang="scss" scoped>
.total-container {
  .popover-8 {
    display: flex;
    justify-content: end;
  }
  .chart-container {
    height: 530px;
  }
}
::v-deep {
  .van-button {
    // width: 260px;
    padding: 10px 20px;
    height: 60px;
    font-size: 26px;
  }
  .van-popover__content {
    overflow: scroll;
    max-height: 300px;
    width: 280px;
    border-radius: 10px;
  }
  .van-popover__action {
    font-size: 24px;
    line-height: 60px;
    width: 100%;
    height: 60px;
    padding: 8px;
  }
}
::v-deep {
  .el-input {
    width: 290px;
  }
}
</style>
