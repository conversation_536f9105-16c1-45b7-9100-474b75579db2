<template>
  <div>
    <table>
      <!-- 名称在一行 并且只有一个名称的时候 -->
      <tbody v-if="layout === 'column' && typeof nameData === 'string'">
        <tr>
          <td></td>
          <td class="head">{{ nameData }}</td>
        </tr>
        <tr v-for="item in tableData.slice(0, showTotal ? tableData.length : showNumber)" :key="item.id">
          <td class="name">{{ item.name }}</td>
          <td class="value">{{ item.value }}</td>
        </tr>
      </tbody>

      <!-- 名称在一行并且有多个name的时候 -->
      <tbody v-if="layout === 'column' && Array.isArray(nameData)">
        <tr>
          <td></td>
          <td v-for="item in nameData" :key="item" class="head">
            {{ item }}
          </td>
        </tr>
        <tr v-for="item in newTableData" :key="item.id">
          <td class="name">{{ item.name }}</td>
          <td class="value">{{ item.value0 }}</td>
          <td class="value">{{ item.value1 }}</td>
          <td class="value">{{ item.value2 }}</td>
        </tr>
      </tbody>

      <!-- 名称在一列并且只有一个name的时候 -->
      <tbody v-if="layout === 'row' && typeof nameData === 'string'">
        <tr>
          <td></td>
          <td v-for="item in tableData" :key="item.id" class="head">{{ item.name }}</td>
        </tr>
        <tr>
          <td class="name">{{ nameData }}</td>
          <td v-for="item in tableData.slice(0, showNumber)" :key="item.id" class="value">{{ item.value }}</td>
        </tr>
      </tbody>

      <!-- 名称在一列并且只有多个name的时候 -->
      <tbody v-if="layout === 'row' && Array.isArray(nameData)">
        <tr>
          <td></td>
          <td v-for="item in tableData" :key="item.id" class="head">{{ item.name }}</td>
        </tr>
        <tr v-for="name in nameData" :key="name">
          <td class="name">{{ name }}</td>
          <td v-for="(item, i) in tableData" :key="i" class="value">
            <span>{{ item['value' + i] }}</span>
          </td>
        </tr>
      </tbody>
    </table>
    <div v-if="!windowConst.matchMedia('(min-width: 600px)')" class="icon" @click="handleClickTableMore">
      <svg
        v-show="!showTotal"
        t="1711797818862"
        viewBox="0 0 1024 1024"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        p-id="7008"
        width="24"
        height="24"
      >
        <path
          d="M856.55 507.86a20.05 20.05 0 0 0-28.28 0L503 833.13 177.73 507.86a20.05 20.05 0 0 0-28.28 0 20.05 20.05 0 0 0 0 28.28l339.41 339.41a20.05 20.05 0 0 0 28.28 0l339.41-339.41a20.05 20.05 0 0 0 0-28.28z"
          p-id="7009"
          fill="#1296db"
        ></path>
        <path
          d="M856.55 148.86a20.05 20.05 0 0 0-28.28 0L503 474.13 177.73 148.86a20.05 20.05 0 0 0-28.28 0 20.05 20.05 0 0 0 0 28.28l339.41 339.41a20.05 20.05 0 0 0 28.28 0l339.41-339.41a20.05 20.05 0 0 0 0-28.28z"
          p-id="7010"
          fill="#1296db"
        ></path>
      </svg>
      <svg
        v-show="showTotal"
        t="1711797818862"
        viewBox="0 0 1024 1024"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        p-id="7008"
        width="24"
        height="24"
        style="transform: rotate(180deg)"
      >
        <path
          d="M856.55 507.86a20.05 20.05 0 0 0-28.28 0L503 833.13 177.73 507.86a20.05 20.05 0 0 0-28.28 0 20.05 20.05 0 0 0 0 28.28l339.41 339.41a20.05 20.05 0 0 0 28.28 0l339.41-339.41a20.05 20.05 0 0 0 0-28.28z"
          p-id="7009"
          fill="#1296db"
        ></path>
        <path
          d="M856.55 148.86a20.05 20.05 0 0 0-28.28 0L503 474.13 177.73 148.86a20.05 20.05 0 0 0-28.28 0 20.05 20.05 0 0 0 0 28.28l339.41 339.41a20.05 20.05 0 0 0 28.28 0l339.41-339.41a20.05 20.05 0 0 0 0-28.28z"
          p-id="7010"
          fill="#1296db"
        ></path>
      </svg>
    </div>

    <!-- <div v-if="Array.isArray(nameData)" class="seeMore" @click="showCollapse">查看更多</div> -->
  </div>
</template>
<script>
export default {
  name: 'TableCpn',
  props: {
    // {value:'',name:''}
    tableData: {
      type: Array,
      required: true
    },
    //可选值   row 横向 /  column 纵向
    layout: {
      type: String,
      required: false,
      default: 'column'
    },
    nameData: {
      type: String || Array,
      required: true,
      default: '旅游总收入'
    },
    showNum: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      windowConst: window,
      showTotal: matchMedia('(min-width: 600px)') ? true : false
    }
  },
  computed: {
    showNumber() {
      return this.showNum || this.tableData.length
    }
  },
  mounted() {
    // console.log(this.showTotal)
  },
  methods: {
    handleClickTableMore() {
      this.showTotal = !this.showTotal
    }
  }
  // data() {
  //   return {
  //     isCollapse: false,
  //     newTableData: [],
  //     lockWatch: false
  //   }
  // },
  // watch: {
  //   tableData(newvalue) {
  //     if (newvalue.length > 0 && !this.lockWatch) {
  //       this.showCollapse()
  //     }
  //   }
  // },
  // methods: {
  //   showCollapse() {
  //     this.lockWatch = true
  //     this.isCollapse = !this.isCollapse
  //     this.newTableData = this.isCollapse ? this.tableData.slice(0, 3) : this.tableData
  //   }
  // }
}
</script>

<style lang="scss" scoped>
table {
  border-collapse: collapse;
  width: 100%;
  border: 1px solid #7da2cd;
  margin: 10px 0 0;
  .value {
    color: rgb(65, 150, 250) !important;
  }
  .name,
  .value,
  .head {
    overflow: hidden;
    font-size: 24px;
    text-align: left;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    color: #000000;
    text-align: right;
    font-style: normal;
    text-align: center;
    height: 64px;
  }
  .name {
    width: 100px;
  }
}
th,
td {
  border: 1px solid black;
  padding: 8px;
  border: 1px solid #7da2cd;
  border-radius: 4px;
}

tr:first-child {
  background: #e8f1fe;
}
td:first-child {
  background: #e8f1fe;
}
.icon {
  display: flex;
  justify-content: center;
}
.seeMore {
  margin-bottom: 44px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 28px;
  color: #0089fe;
  line-height: 36px;
  text-align: center;
  font-style: normal;
}
</style>
