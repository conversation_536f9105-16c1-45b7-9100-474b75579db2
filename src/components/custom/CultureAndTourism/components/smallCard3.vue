<template>
  <!-- 文物保护单位小卡片 -->
  <div v-if="data1 && data2" class="smallCardBox">
    <div class="time">{{ data1.year }}</div>
    <div class="title">文物保护单位</div>

    <div class="contentValue">
      <div class="left">
        <div class="contentTitle1">
          <span class="imgText">{{ data1.field_name }}</span>
        </div>
        <div class="setFontStyle">
          <span class="contentNum">{{ data1.field_value }}</span>{{ data1.field_unit }}
        </div>
        <div class="bottomvlaue">
          {{ data1.name }}
          <img src="../img/jiangpin01.png" alt="" />
          <span>{{ data1.value }}</span>
        </div>
      </div>
      <div class="right">
        <div class="contentTitle2">
          <span class="imgText">{{ data2.field_name }}</span>
        </div>
        <div class="setFontStyle">
          <span class="contentNum">{{ data2.field_value }}</span>
          <span>
            {{ data2.field_unit }}
          </span>
        </div>
        <div class="bottomvlaue">
          {{ data2.name }}
          <img src="../img/jiangpin05.png" alt="" />
          <span>{{ data2.value }}</span>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { getAction } from '@/api/manage.js'
export default {
  name: 'TourismAndCultureSmallCard1',
  data() {
    return {
      data1: null,
      data2: null
    }
  },
  mounted() {
    this.getData()
  },
  methods: {
    async getData() {
      await getAction(import.meta.env.VITE_APP_SITUATION_BASE_URL + '/topic/data/listAllBySql/ydd_ly_kp_wwgk', {}).then(
        (res) => {
          if (res.success) {
            this.data1 = res.result[0]
            this.data2 = res.result[1]
          }
        }
      )
    }
  }
}
</script>

<style lang="scss" scoped>
.smallCardBox {
  width: 100%;
  height: 357px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 44px 35px;
  border-radius: 15px;
  background-image: url('@/assets/img/smallcard/smallcard3.png');
  background-size: 100% 357px;
  background-repeat: no-repeat;
  position: relative;
  .title {
    font-family: SourceHanSansCN, SourceHanSansCN;
    font-weight: 400;
    font-size: 28px;
    color: #ffffff;
    text-align: center;
    margin-left: 11px;
    align-items: center;
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: center;
    left: 35px;
    top: 22px;
  }
  .time {
    font-family: SourceHanSansCN, SourceHanSansCN;
    font-weight: 400;
    font-size: 22px;
    color: #ffffff;
    text-align: center;
    margin-left: 11px;
    background: url('/src/assets/img/date_bg2.png') no-repeat center/100% 48px;
    align-items: center;
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: center;
    right: -5px;
    width: 225px;
    height: 48px;
    top: 17px;
    padding-bottom: 8px;
  }
  .title {
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 40px;
    color: #ffffff;
    text-align: left;
    font-style: normal;
    margin-bottom: 20px;
    line-height: 5.8vw;
    display: flex;
    div:last-child {
      display: flex;
      flex: 1;
      align-items: center;
      justify-content: center;
      font-family: SourceHanSansCN, SourceHanSansCN;
      font-weight: 400;
      font-size: 24px;
      color: #ffffff;
      text-align: center;
      font-style: normal;
      padding-bottom: 4px;
      margin-left: 1.466667vw;
      margin-right: -3.4vw;
      height: 7vw;
    }
  }
  .contentValue {
    display: flex;
    margin-top: 50px;
    justify-content: space-between;
    .left {
      flex: 1;
    }
    .right {
      flex: 1;
      padding-left: 20px;
    }
    .left,
    .right {
      display: flex;
      flex-direction: column;
      .bottomvlaue {
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 28px;
        color: #ffffff;
        display: flex;
        align-items: center;
        position: relative;
        span {
          position: absolute;
          left: 103px;
          top: 5px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
        }
        img {
          width: 55px;
        }
      }
      .contentTitle1 {
        width: 300px;
        height: 62px;
        background-image: url('../img/gjj.png');
        background-size: 300px auto;
        background-repeat: no-repeat;
        // line-height: 62px;
        display: flex;
        align-items: center;
        margin-bottom: 5px;
        padding-left: 75px;
        color: white;
        img {
          width: 300px;
          height: 620px;
          max-width: unset;
        }
      }
      .imgText {
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 28px;
        color: #ffffff;
        text-align: left;
        font-style: normal;
      }
      .contentTitle2 {
        width: 300px;
        // line-height: 62px;
        display: flex;
        align-items: center;
        margin-bottom: 5px;
        height: 62px;
        background-image: url('../img/sj.png');
        background-size: 300px 62px;
        background-repeat: no-repeat;
        padding-left: 75px;
      }

      .setFontStyle {
        font-family: SourceHanSansCN, SourceHanSansCN;
        font-weight: 400;
        font-size: 32px;
        color: #ffffff;
        text-align: left;
        font-style: normal;
      }
      .contentNum {
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 68px;
        color: #ffffff;
        line-height: 95px;
        text-align: left;
        font-style: normal;
        span:last-child {
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 28px;
          color: #ffffff;
          line-height: 40px;
          text-align: left;
          font-style: normal;
        }
      }
    }
  }
}
</style>
