<template>
  <!-- 文化概况小卡片 -->
  <div class="smallcardBox">
    <div class="time">{{ data1.year }}</div>
    <div class="contentBox">
      <div class="imgbox1">
        <span class="imgText">{{ data1.field_name }}</span>
      </div>
      <div class="valuebox">
        <span class="num">{{ data1.field_value }}</span>
        <span class="unit">{{ data1.field_unit }}</span>
      </div>
    </div>
    <div class="contentBox">
      <div class="imgbox2">
        <span class="imgText">{{ data2.field_name }}</span>
      </div>
      <div class="valuebox">
        <span class="num">{{ data2.field_value }}</span>
        <span class="unit">{{ data2.field_unit }}</span>
      </div>
    </div>
  </div>
</template>
<script>
import { getAction } from '@/api/manage.js'

export default {
  data() {
    return {
      data1: null,
      data2: null,
      data3: null
    }
  },
  mounted() {
    // 模拟数据
    this.data1 = {
      year: '2023年',
      field_name: '世界文化遗产',
      field_value: '10000',
      field_unit: '件'
    }
    this.data2 = {
      year: '2023年',
      field_name: '文化场所总面积',
      field_value: '5000',
      field_unit: '个'
    }
  },
  methods: {
    // async getData() {
    //   await getAction(import.meta.env.VITE_APP_SITUATION_BASE_URL + '/topic/data/listAllBySql/ydd_ly_kp_whgk', {}).then(
    //     (res) => {
    //       if (res.success) {
    //         this.data1 = res.result[0]
    //         this.data2 = res.result[1]
    //         this.data3 = res.result[2]
    //       }
    //     }
    //   )
    // }
  }
}
</script>

<style lang="scss" scoped>
.smallcardBox {
  display: flex;
  flex-direction: column;
  padding: 53px 26px 35px;
  justify-content: space-evenly;
  background-image: url('@/assets/img/smallcard/smallcard2.png');
  background-size: 100% 370px;
  background-repeat: no-repeat;
  height: 370px;
  position: relative;
  .time {
    font-family: SourceHanSansCN, SourceHanSansCN;
    font-weight: 400;
    font-size: 22px;
    color: #ffffff;
    text-align: center;
    margin-left: 11px;
    background: url('@/assets/img/date_bg.png') no-repeat center/100% auto;
    align-items: center;
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: center;
    right: -6px;
    width: 225px;
    height: 48px;
    top: 25px;
    padding-bottom: 8px;
  }

  .contentBox {
    margin-top: 10px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    div:first-child {
      width: 375px;
      height: 70px;
    }
    div:last-child {
      line-height: 1;
    }
    .title {
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 40px;
      color: #ffffff;
      text-align: left;
      font-style: normal;
    }
    .year {
      font-family: SourceHanSansCN, SourceHanSansCN;
      font-weight: 400;
      font-size: 24px;
      color: #ffffff;
      text-align: center;
      font-style: normal;
    }
    .imgbox1 {
      background-image: url('../img/whcszmj.png');
      // line-height: 70px;
      display: flex;
      align-items: center;
      background-size: 375px auto;
      background-repeat: no-repeat;
    }
    .imgbox2 {
      background-image: url('../img/whcsfwzrc.png');
      // line-height: 70px;
      display: flex;
      align-items: center;
      background-size: 375px auto;
      background-repeat: no-repeat;
    }
    .imgText {
      margin-left: 70px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 32px;
      color: #ffffff;
      text-align: left;
      font-style: normal;
    }
    img {
      width: 375px;
      height: 70px;
    }
    .num {
      font-family: D-DIN, D-DIN;
      font-weight: bold;
      font-size: 60px;
      color: #ffffff;
      text-align: center;
      font-style: normal;
    }
    .unit {
      font-family: SourceHanSansCN, SourceHanSansCN;
      font-weight: 400;
      font-size: 30px;
      color: #ffffff;
      text-align: left;
      font-style: normal;
      white-space: nowrap;
    }
  }
  .valuebox {
    display: flex;
    align-items: baseline;
  }
}
</style>
