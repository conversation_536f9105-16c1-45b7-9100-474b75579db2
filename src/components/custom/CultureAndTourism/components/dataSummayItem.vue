<template>
  <div class="dataSummayItemBox1" :style="config.baseStyle ?? {}">
    <div class="leftContentBox">
      <img v-if="config.icon?.content" :style="config.icon.style ?? {}" :src="config.icon.content" class="icon" />
      <div v-if="config.text?.content" :style="config.text.style ?? {}" class="text">
        {{ config.text.content }}
      </div>
    </div>
    <div class="rightContentBox">
      <div v-if="config.num?.content" :style="config.num.style ?? {}" class="num">{{ config.num.content }}</div>
      <div v-if="config.unit?.content" :style="config.unit.style ?? {}" class="unit">{{ config.unit.content }}</div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    config: {
      type: Object,
      default() {
        return {}
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.dataSummayItemBox1 {
  width: 100%;
  display: flex;
  justify-content: space-between;
  .icon {
    width: 30px;
    height: 30px;
    margin-right: 10px;
  }
  .text {
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    font-size: 28px;
    color: #333333;
    line-height: 28px;
    text-align: left;
    font-style: normal;
  }
  .num {
    font-family: D-DIN, D-DIN;
    font-weight: bold;
    font-size: 48px;
    color: #3b93fb;
    line-height: 48px;
    text-align: justify;
    font-style: normal;
  }
  .unit {
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 22px;
    color: #666666;
    line-height: 22px;
    text-align: center;
    font-style: normal;
  }
}
.leftContentBox,
.rightContentBox {
  display: flex;
  align-items: center;
}
</style>
