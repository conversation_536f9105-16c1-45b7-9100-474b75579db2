<template>
  <div class="dataSummaryBox">
    <div class="dataSummayItemBox">
      <div class="leftContentBox">
        <img :src="config.icon" class="icon" />
        <div class="text">
          {{ config.name }}
        </div>
      </div>
      <div class="rightContentBox">
        <div class="num">{{ config.value }}</div>
        <div class="unit">{{ config.unit }}</div>
      </div>
      <slot></slot>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    config: {
      type: Object,
      default() {
        return {
          type: 1
        }
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.dataSummaryBox {
  .leftContentBox,
  .rightContentBox {
    display: flex;
    align-items: center;
  }
  .leftContentBox {
    flex: 2;
  }
  .rightContentBox {
    flex: 1;
    justify-content: end;
  }
  .dataSummayItemBox {
    position: relative;
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding: 10px 15px;
    background-image: url('../img/itemBag.png');
    /* background: linear-gradient(180deg, #dce3ff 0%, #eff2ff 100%); */
    box-shadow: 0px 2.133333vw 4.266667vw 0px rgba(192, 199, 218, 0.35),
      0px 0.266667vw 1.066667vw 0px rgba(201, 201, 201, 0.13);
    border-radius: 1.066667vw;
    border: 0.266667vw solid #ffffff;
    background-size: 113% 159%;
    background-position-y: -20px;
    background-position-x: -34px;
    background-repeat: no-repeat;
    margin: 24px 0;
  
    .num {
      font-family: D-DIN, D-DIN;
      font-weight: bold;
      font-size: 48px;
      color: #fbba3b;
      line-height: 48px;
      text-shadow: 0px 16px 32px rgba(192, 199, 218, 0.35);
      text-align: center;
      font-style: normal;
    }
    .unit {
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 24px;
      color: #666666;
      line-height: 28px;
      text-shadow: 0px 16px 32px rgba(192, 199, 218, 0.35);
      text-align: center;
      font-style: normal;
    }
    .text {
      font-family: SourceHanSansCN, SourceHanSansCN;
      font-weight: 500;
      font-size: 32px;
      color: #333333;
      line-height: 36px;
      text-shadow: 0px 16px 32px rgba(192, 199, 218, 0.35);
      text-align: left;
      font-style: normal;
    }
    .icon {
      width: 98px;
      height: 98px;
      margin-right: 10px;
    }
  }
}
</style>
