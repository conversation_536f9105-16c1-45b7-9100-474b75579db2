<template>
  <div class="chartBox">
    <div ref="chart" :style="chartStyle" />
    <div v-if="showTotal" class="total">
      <div>总计</div>
      <div>{{ sum }}{{ unit }}</div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
export default {
  props: {
    chartStyle: {
      type: Object,
      required: false,
      default() {
        return {
          width: '100%',
          height: '100%'
        }
      }
    },
    //如要修改grid则传grid:222
    //如要修改lengend下面textStyle下面fontSize,则传 legend_textStyle_fontSize:1111
    chartConfig: {
      type: Object,
      required: true
    },
    chartData: {
      type: Array,
      required: true
    },
    //下面不用showlegend 或者show...也可以,因为可以传配置
    showLabel: {
      type: Boolean,
      default: true
    },
    showLegend: {
      type: Boolean,
      default: true
    },
    showTotal: {
      type: Boolean,
      default: true
    },
    labelPadding: {
      type: [Number, Array],
      default: 0
    },
    //比例系数,用来调整图例所占的宽度
    ratio: {
      type: Object,
      default() {
        return {
          a: 0.2,
          b: 0.5,
          c: 0.3
        }
      }
    }
  },
  data() {
    return {
      remValue: 1,
      myChart: null,
      legendSize: {
        left: window.matchMedia('(min-width: 600px) and (max-width: 630px)').matches ? 75 : 85,
        right: 85,
        center: 85
      },
      sum: '',
      unit: '',
      colorArr: ['#FFA26F', '#3CE2B0', '#66A0FF', '#FFD368', '#48E0E8', '#BD83FF']
    }
  },
  watch: {
    chartData: {
      handler(newVal) {
        this.getChart(newVal)
        this.sum = newVal.reduce((accumulator, currentValue) => accumulator + Number(currentValue.value), 0)
        this.unit = newVal[0].unit ?? ''
      }
    }
  },
  mounted() {
    // window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    getChart(chartData) {
      let that = this
      const sourceData = chartData.map((item) => [+item.value, item.name])
      if (this.myChart === null) {
        this.myChart = echarts.init(this.$refs.chart, null, { renderer: 'svg' })
      }
      let option = {
        grid: {
          left: '5%',
          right: '5%',
          top: 0,
          containLabel: true
        },

        legend: {
          show: that.showLegend,
          type: 'scroll',
          orient: 'vertical',
          top: 'auto',
          bottom: 0,
          data: chartData.map((item) => item.name),
          icon: 'circle',
          itemWidth: 12,
          itemHeight: 12,
          itemGap: 10,
          textStyle: {
            rich: {
              a: {
                width: this.legendSize.left, // 左侧内容宽度
                color: '#333333',
                fontFamily: 'MicrosoftYaHei',
                fontSize: 14,
                lineHeight: 18,
                fontStyle: 'normal',
                align: 'left'
              },
              b: {
                width: this.legendSize.center, // 中间内容宽度
                color: '#979797',
                fontFamily: 'MicrosoftYaHei',
                fontSize: 14,
                lineHeight: 18,
                fontStyle: 'normal',
                align: 'center'
              },
              c: {
                width: this.legendSize.right, // 右侧内容宽度
                color: '#979797',
                fontFamily: 'MicrosoftYaHei',
                fontSize: 14,
                lineHeight: 18,
                fontStyle: 'normal',
                align: 'right'
              }
            }
          },
          formatter: function (value) {
            //计算各个等级的占比
            const item = chartData.find((item) => item.name === value)
            if (item) {
              const total = chartData.reduce((accumulator, currentValue) => accumulator + Number(currentValue.value), 0)
              const percentage = ((parseInt(item.value) / total) * 100).toFixed(2)
              return '{a|' + item.name + '}{b|' + item.value + item.unit + '}{c|' + percentage + '%}'
            } else {
              return value
            }
          }
        },
        dataset: {
          dimensions: ['value', 'name'],
          source: sourceData
        },
        tooltip: {
          trigger: 'item',
          confine: true,
          valueFormatter: (value) => value + chartData[0].unit,
          backgroundColor: 'rgba(0,0,0,0.6)',
          extraCssText: 'z-index: 9;',
          textStyle: {
            color: 'white'
          }
        },
        series: {
          color: this.colorArr,
          width: '100%',
          height: '70%',
          left: 'center',
          radius: ['30%', '65%'],
          top: '5%',
          name: this.chartConfig.series_name,
          label: {
            alignTo: 'edge',
            show: that.showLabel,
            fontFamily: 'PingFangSC, PingFang SC',
            fontWeight: '400',
            textAlign: 'center',
            // minMargin: 5,
            edgeDistance: 0,
            lineHeight: 20,
            padding: this.labelPadding,
            posinTo: 'edge',
            margtion: 'outer',
            aligin: 0,
            fontSize: 14,
            fontStyle: 'normal',
            color: 'inherit',
            formatter: (params) => {
              const data = params.data[1].slice(0, 3) + '\n' + params.data[1].slice(3) + '\n'
              if (params.data[1].length > 7) return '{a|' + data + '}{b|' + params.percent + '%}'
              return `${params.data[1]}\n${params.percent}%`
            },
            textStyle: {
              rich: {
                a: {
                  align: 'center'
                },
                b: {
                  align: 'center'
                }
              }
            }
          },
          labelLine: {
            length: 5,
            length2: 5
          },
          labelLayout: function (params) {
            const isLeft = params.labelRect.x < that.myChart.getWidth() / 2
            const points = params.labelLinePoints
            points[2][0] = isLeft ? params.labelRect.x : params.labelRect.x + params.labelRect.width
            return {
              labelLinePoints: points
            }
          },
          type: 'pie',
          center: ['50%', '50%'],
          datasetIndex: 0,
          minAngle: 10,
          avoidLabelOverlap: true,
          hoverOffset: 15
        }
      }
      this.parseOptions(option, this.chartConfig)
      this.myChart.setOption(option)
    },

    parseOptions(options, overrides) {
      function applyOverrides(obj, overrides) {
        for (const key in overrides) {
          if (Object.prototype.hasOwnProperty.call(overrides, key)) {
            const keys = key.split('_')
            let currentObj = obj
            for (let i = 0; i < keys.length - 1; i++) {
              if (!currentObj[keys[i]]) {
                throw new Error(`Echarts的options中找不到${keys}属性`)
              }
              currentObj = currentObj[keys[i]]
            }

            const lastKey = keys[keys.length - 1]
            if (currentObj[lastKey] === undefined) {
              throw new Error(`Echarts的options中找不到${keys}属性`)
            }
            currentObj[lastKey] = overrides[key]
          }
        }
      }
      applyOverrides(options, overrides)
    },
    handleResize() {
      if (this.myChart) {
        this.myChart.resize()
        this.myChart.setOption({
          series: [
            {
              width: '100%',
              height: '100%',
              label: {
                fontSize: 14
              },
              legend: {
                textStyle: {
                  fontFamily: 'PingFangSC, PingFang Screen',
                  fontWeight: 400,
                  fontSize: 12,
                  fontStyle: 'normal'
                }
              }
            }
          ]
        })
      }
    },
    // setRemValue() {
    //   this.remValue = parseFloat(document.documentElement.style.fontSize)
    // }
    setRemValue() {
      //10px =>  10  拿到1px的值
      this.remValue = parseFloat(document.documentElement.style.fontSize)
      //重新计算legend的宽度, 用来让一行3列的图例占满一行
      const elWidth = document.querySelector('.chart-container').offsetWidth
      this.legendSize.left = elWidth * this.ratio.a
      this.legendSize.center = elWidth * this.ratio.b
      this.legendSize.right = elWidth * this.ratio.c
    }
  }
}

//以下是圆环饼图版本使用的配置,暂时注释

// series下面labelLine | label

// label: {
//   rich: {
//     a: {
//       lineHeight: 78 * this.remValue
//     },
//     b: {
//       lineHeight: 50 * this.remValue
//     },
//     x: {
//       lineHeight: 45 * this.remValue
//     }
//   }
// }
//labelLine: {
//   show: false // 隐藏标注线
// },
// label: {
//   show: true
// position: 'center',
// formatter: ['{a|' + that.total + '}', '{b|' + chartData[0].unit + '}', '{x|' + '总量统计' + '}'].join(
//   '\n'
// ),
// rich: {
//   a: {
//     fontFamily: ' PingFangSC, PingFang SC',
//     fontWeight: '600',
//     fontSize: '56px',
//     color: '#3B93FB',
//     lineHeight: 78 * this.remValue
//   },
//   b: {
//     fontFamily: 'PingFangSC, PingFang SC',
//     fontWeight: '400',
//     fontSize: '28px',
//     color: ' #666666',
//     lineHeight: 50 * this.remValue
//   },
//   x: {
//     fontFamily: 'PingFangSC, PingFang SC',
//     fontWeight: '500',
//     fontSize: '32px',
//     color: ' #333333',
//     lineHeight: 45 * this.remValue
//   }
// }
// },

// legend: {
//   type: 'scroll',
//   orient: 'vertical',
//   bottom: 0,
//   left: 0,
//   data: chartData.map((item) => item.name),
//   icon: 'circle',
//   itemWidth: 12,
//   itemHeight: 12,
//   itemGap: 18,
//   textStyle: {
//     rich: {
//       a: {
//         width: this.legendSize.left, // 左侧内容宽度
//         color: '#333333',
//         fontFamily: 'MicrosoftYaHei',
//         fontSize: 28 * this.remValue,
//         lineHeight: 18,
//         fontStyle: 'normal',
//         align: 'left'
//       },
//       b: {
//         width: this.legendSize.center, // 中间内容宽度
//         color: '#979797',
//         fontFamily: 'MicrosoftYaHei',
//         fontSize: 28 * this.remValue,

//         lineHeight: 18,
//         fontStyle: 'normal',
//         align: 'right'
//       },
//       c: {
//         width: this.legendSize.right, // 右侧内容宽度
//         color: '#979797',
//         fontFamily: 'MicrosoftYaHei',
//         fontSize: 28 * this.remValue,
//         lineHeight: 18,
//         fontStyle: 'normal',
//         align: 'center'
//       }
//     }
//   },
//   formatter: function (value) {
//     //计算各个等级的占比
//     const item = chartData.find((item) => item.name === value)
//     if (item) {
//       const percentage = ((parseInt(item.value) / that.total) * 100).toFixed(2)
//       return '{a|' + item.name + '}{b|' + item.value + item.unit + '}{c|' + percentage + '%}'
//     } else {
//       return value
//     }
//   }
// },

//     handleResize() {
//   if (this.myChart) {
//     this.setRemValue()
//     this.myChart.resize()
//     this.myChart.setOption({
// legend: {
//   textStyle: {
//     rich: {
//       a: {
//         width: this.legendSize.left, // 左侧内容宽度
//         fontSize: 28 * this.remValue
//       },
//       b: {
//         width: this.legendSize.center, // 中间内容宽度
//         fontSize: 28 * this.remValue
//       },
//       c: {
//         width: this.legendSize.right, // 右侧内容宽度
//         fontSize: 28 * this.remValue
//       }
//     }
//   }
// },
//   series: [
//     {
//       width: '100%',
//       height: '100%',
//       label: {
//         fontSize: 22 * this.remValue
//       }
// label: {
//   rich: {
//     a: {
//       lineHeight: 78 * this.remValue
//     },
//     b: {
//       lineHeight: 50 * this.remValue
//     },
//     x: {
//       lineHeight: 45 * this.remValue
//     }
//   }
// }
//         }
//       ]
//     })
//   }
// },

// setRemValue() {
//   //10px =>  10  拿到1px的值
//   this.remValue = parseFloat(document.documentElement.style.fontSize)
//   //重新计算legend的宽度, 用来让一行3列的图例占满一行
//   const elWidth = document.querySelector('.chart-container').offsetWidth
//   this.legendSize.left = elWidth * 0.2
//   this.legendSize.center = elWidth * 0.5
//   this.legendSize.right = elWidth * 0.3
// }
</script>
<style scoped>
.chartBox {
  width: 100%;
  height: 100%;
  position: relative;
  color: red;
}
.chart {
  width: 100%;
}
.total {
  font-size: 28px;
  color: #333333;
  font-weight: bold;
  margin-top: 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: absolute;
  width: 110px;
  top: 224px;
  left: 265px;
}
@media screen and (min-width: 600px) {
  .total {
    font-size: 16px;
    margin-top: 0;
    top: 34%;
    left: 33%;
  }
}
@media screen and (min-width: 800px) {
  .total {
    font-size: 16px;
    margin-top: 0;
    top: 35%;
    left: 33%;
  }
}
</style>
