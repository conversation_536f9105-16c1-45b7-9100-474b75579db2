<template>
  <div :class="deleteBaseStyle ? 'baseDataSummaryBox' : 'dataSummaryBox'">
    <div class="row1">
      <slot name="row1"> </slot>
    </div>
    <div class="row2">
      <slot name="row2"> </slot>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    deleteBaseStyle: {
      type: Boolean,
      default: false
    }
  }
}
</script>
<style lang="scss" scoped>
.dataSummaryBox {
  width: 100%;
  padding: 15px 22px;
  background-color: rgb(240, 247, 255);
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-content: space-between;
  margin-bottom: 16px;
  .row1,
  .row2 {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}
.baseDataSummaryBox {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-content: space-between;
  margin-bottom: 16px;
  .row1,
  .row2 {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}
</style>
