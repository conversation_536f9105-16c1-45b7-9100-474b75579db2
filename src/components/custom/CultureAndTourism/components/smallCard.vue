<template>
  <!-- 游客与收入小卡片 -->
  <div v-if="data1 && data2" class="smallCardBox">
    <popover
      :container="'.popover-4'"
      :actions="options"
      :default-option="selectedOption"
      class="toppopover"
      @selectedOption="handleSelectedTime"
    ></popover>
    <div class="contentValue">
      <div class="left">
        <div class="contentTitle1">
          <span class="imgText">{{ data1.field_name }}</span>
        </div>
        <div class="setFontStyle">
          <span class="contentNum">{{ data1.field_value }}</span>{{ data1.field_unit }}
        </div>
      </div>
      <div class="right">
        <div class="contentTitle2">
          <span class="imgText">{{ data2.field_name }}</span>
        </div>
        <div class="setFontStyle">
          <span class="contentNum">{{ data2.field_value }}</span>{{ data2.field_unit }}
        </div>
      </div>
    </div>
    <div class="contentRatio">
      <div class="left">
        <div class="flex-container">
          <span class="desc">
            {{ data1.growth_name }}
          </span>
          <div class="ratio">
            <img src="@/assets/img/arrow-up.png" alt="" />
            <span> {{ data1.growth_value }} %</span>
          </div>
        </div>
      </div>
      <div class="right">
        <div class="flex-container">
          <span class="desc">
            {{ data2.growth_name }}
          </span>
          <div class="ratio">
            <img src="@/assets/img/arrow-up.png" alt="" />
            <span>{{ data2.growth_value }}%</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getAction } from '@/api/manage.js'
import popover from '@/components/selector.vue'

export default {
  name: 'TourismAndCultureSmallCard1',
  components: {
    popover
  },
  data() {
    return {
      data1: null,
      data2: null,
      options: [],
      selectedOption: ''
    }
  },
  mounted() {
    this.getTime()
  },
  methods: {
    getData() {
      getAction(import.meta.env.VITE_APP_SITUATION_BASE_URL + '/topic/data/listAllBySql/ydd_ly_kp_lygk', {
        year: this.selectedOption
      }).then((res) => {
        if (res.success) {
          this.data1 = res.result[0]
          this.data2 = res.result[1]
        }
      })
    },
    getTime() {
      getAction(import.meta.env.VITE_APP_SITUATION_BASE_URL + '/topic/data/listAllBySql/ydd_ly_kp_lygk_nf').then(
        (res) => {
          if (res.success) {
            this.options = res.result.map((item) => {
              return {
                text: item.year
              }
            })
            this.selectedOption = this.options[0].text
            this.getData()
          }
        }
      )
    },
    handleSelectedTime(time) {
      this.selectedOption = time
      this.getData()
    }
  }
}
</script>

<style lang="scss" scoped>
.smallCardBox {
  width: 100%;
  height: 357px;
  display: flex;
  flex-direction: column;
  padding: 25px 35px;
  border-radius: 15px;
  background-image: url('@/assets/img/smallcard/smallcard1.png');
  background-size: 100% 357px;
  background-repeat: no-repeat;
  position: relative;
  .toppopover {
    position: absolute;
    right: -5px;
    top: 20px;
  }
  .time {
    font-family: SourceHanSansCN, SourceHanSansCN;
    font-weight: 400;
    font-size: 22px;
    color: #ffffff;
    text-align: center;
    margin-left: 11px;
    background: url('@/assets/img/date_bg3.png') no-repeat center/100% 48px;
    align-items: center;
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: center;
    right: -5px;
    width: 192px;
    height: 48px;
    top: 30px;
    padding-bottom: 8px;
  }
  .contentValue {
    margin-top: 70px;
    display: flex;
    justify-content: space-between;
    .left {
      flex: 1;
    }
    .right {
      flex: 1;
      padding-left: 20px;
    }
    .left,
    .right {
      display: flex;
      flex-direction: column;
      .contentTitle1 {
        // line-height: 62px;
        display: flex;
        height: 62px;
        align-items: center;
        margin-bottom: 5px;
        padding-left: 63px;
        color: white;
        background-image: url('../img/jdgnyk.png');
        background-size: 300px 62px;
        background-repeat: no-repeat;
      }
      .imgText {
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 28px;
        color: #ffffff;
        text-align: left;
        font-style: normal;
      }
      .contentTitle2 {
        width: 300px;
        padding-left: 63px;
        margin-bottom: 5px;
        // line-height: 62px;
        display: flex;
        align-items: center;
        height: 62px;
        background-image: url('../img/lvzsr.png');
        background-size: 300px 62px;
        background-repeat: no-repeat;
      }
      .setFontStyle {
        font-family: SourceHanSansCN, SourceHanSansCN;
        font-weight: 400;
        font-size: 32px;
        color: #ffffff;
        text-align: left;
        font-style: normal;
        margin-top: 20px;
      }
      .contentNum {
        font-family: D-DIN, D-DIN;
        font-weight: bold;
        font-size: 60px;
        color: #ffffff;
        text-align: left;
        font-style: normal;
      }
    }
  }
  .contentRatio {
    display: flex;
    align-items: center;
    .desc {
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 28px;
      color: #ffffff;
      line-height: 40px;
      text-align: left;
      font-style: normal;
    }
    .left {
      flex: 1;
    }
    .right {
      flex: 1;
      padding-left: 20px;
    }
    .left,
    .right {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      font-family: SourceHanSansCN, SourceHanSansCN;
      font-weight: 400;
      font-size: 22px;
      color: #ffffff;
      text-align: left;
      font-style: normal;
      & div:first-child {
        margin-top: 10px;
        display: flex;
        align-items: baseline;
        .ratio {
          display: flex;
          align-items: baseline;
          margin-top: 0;
          img {
            width: 17px;
            height: 25px;
            margin-left: 10px;
          }
          span {
            font-family: AppleSystemUIFont;
            font-size: 28px;
            color: #f63146;
            line-height: 33px;
            text-align: left;
            font-style: normal;
          }
        }
      }
    }
  }
  .desc2 {
    font-family: PingFangSC, PingFang SC !important;
    font-weight: 500 !important;
    font-size: 24px !important;
    color: #ffffff !important;
    line-height: 33px !important;
    text-align: left !important;
    font-style: normal !important;
  }
}
::v-deep {
  .van-button {
    width: 200px;
    height: 60px;
    font-size: 26px;
  }
  .van-popover__content {
    overflow: scroll;
    max-height: 300px;
    width: 200px;
    border-radius: 10px;
  }
  .van-popover__action {
    font-size: 24px;
    line-height: 60px;
    width: 100%;
    height: 60px;
    padding: 8px;
  }
  .el-input {
    width: 225;
  }
  .el-input__inner {
    // background-color: transparent;
    background: url('/src/assets/img/date_bg3.png') no-repeat center/100% 56px;
    color: white;
    border: 0;
    text-align: center;
    font-size: 24px;
    padding-bottom: 5px;
  }
}
</style>
<style>
.el-select-dropdown {
  border: 0;
}
.el-select-dropdown {
  background-color: rgba(255, 255, 255, 0.9);
}
</style>
