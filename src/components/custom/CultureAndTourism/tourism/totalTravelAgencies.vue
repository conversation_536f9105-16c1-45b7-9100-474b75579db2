<template>
  <div class="agenciesBox">
    <div v-if="!isPad" class="rowBox">
      <div v-for="(item, i) in boxData" :key="item.id" class="itemBox">
        <div class="itemBox2">
          <img v-if="i === 0" src="../img/lxszs.png" alt="" />
          <img v-if="i === 1" src="../img/ybafss.png" alt="" />
          <img v-if="i === 2" src="../img/ybawds.png" alt="" />
          <div class="itemBox3">
            <div class="itemName">{{ item.name }}</div>
            <div class="itemValue">
              {{ item.value }}<span>{{ item.unit }}</span>
            </div>
          </div>
        </div>
        <div v-if="i === 0" class="bottomLine"></div>
        <div v-if="i === 1" class="rightLine"></div>
      </div>
    </div>
    <div v-else class="pad-content">
      <div v-for="(item, index) in boxData" :key="index" class="pad-item">
        <div class="icon">
          <img :src="dynamicImageSrc(index)" alt="" />
        </div>
        <div class="content">
          <div class="name">{{ item.name }}</div>
          <div style="margin-left: 12px; display: flex; align-items: baseline">
            <div class="value">{{ item.value }}</div>
            <div class="unit">{{ item.unit }}</div>
          </div>
        </div>
        <img v-if="index !== 2" src="@/assets/img/energy-separator.png" alt="" class="separator" />
      </div>
    </div>
  </div>
</template>

<script>
import { postAction } from '@/api/manage.js'

export default {
  data() {
    return {
      boxData: [],
      isPad: false
    }
  },
  mounted() {
    this.getData()
    this.isPad = this.getPad()
  },
  methods: {
    dynamicImageSrc(index) {
      // 动态构建图片路径并导入
      return new URL(`../img/qslys-icon${index}.png`, import.meta.url).href
    },
    async getData() {
      await postAction(import.meta.env.VITE_APP_SITUATION_BASE_URL + '/topic/data/listAllData/ydd_ly_qslxs', {}).then(
        (res) => {
          if (res.success) {
            this.boxData = res.result.data
          }
        }
      )
    },
    getPad() {
      return window.matchMedia('(min-width: 600px)').matches
    }
  }
}
</script>

<style lang="scss" scoped>
.rowBox {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  position: relative;
  .itemBox2 {
    display: flex;
    align-items: center;
  }
  .itemBox3 {
    display: flex;
    align-items: start;
    flex-direction: column;
    margin-left: 12px;
  }
  .itemBox {
    width: 305px;
    height: 172px;
    // background-image: url('../img/travelAgenceBottombag.png');
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    // background-repeat: no-repeat;
    // background-size: 100% 172px;
    img {
      width: 90px;
      height: 90px;
    }
    .itemName {
      margin-bottom: 10px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 600;
      font-size: 28px;
      color: #333333;
      line-height: 28px;
      text-align: right;
      font-style: normal;
    }
    .itemValue {
      font-family: D-DIN, D-DIN;
      font-weight: bold;
      font-size: 48px;
      color: #3b93fb;
      line-height: 48px;
      text-align: left;
      font-style: normal;
      span {
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 24px;
        color: #666666;
        line-height: 28px;
        text-align: left;
        font-style: normal;
      }
    }
  }
  .itemBox:first-of-type {
    width: 100%;
    height: 172px;
    // background-image: url('../img/travelAgenceTopbag.png');
  }
  .bottomLine {
    width: 100%;
    height: 3px;
    background: linear-gradient(to right, #f1f4fa 0%, #5c8bd4 50%, #f1f4fa 100%);
    background-repeat: no-repeat;
    position: absolute;
    border-radius: 50%;
    top: 172px;
    left: 0;
  }
  .rightLine {
    width: 3px;
    height: 119px;
    background: linear-gradient(to top, white 0%, #5c8bd4 50%, white 100%);
    background-repeat: no-repeat;
    position: absolute;
    border-radius: 50%;
    bottom: 23px;
    left: 306px;
  }
}
.agenciesBox {
  margin-bottom: 0;
}
.separator {
  margin-left: 30px;
}
</style>
