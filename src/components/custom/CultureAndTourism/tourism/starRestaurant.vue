<template>
  <div class="starRestaurantBox">
    <div class="chart-container">
      <pieChart :chart-config="chartConfig" :chart-data="chartData" :chart-style="chartStyle"></pieChart>
    </div>
    <!-- <div class="dropdownBox">
      <van-dropdown-menu :overlay="false" active-color="#3795ff">
        <van-dropdown-item v-model="defaultValue" :options="options" @change="dropDownHandler" />
      </van-dropdown-menu>
    </div> -->
  </div>
</template>

<script>
import { getAction } from '@/api/manage.js'
import pieChart from '../components/pieChart.vue'

export default {
  name: 'ServiceTextSingleLine',
  components: {
    pieChart
  },
  data() {
    return {
      defaultValue: 2024,
      myChart: null,
      //给pie组件传值
      chartData: [],
      chartConfig: {},
      chartStyle: {
        width: '100%',
        height: '100%'
      }
    }
  },
  mounted() {
    this.getData({ opt: this.defaultValue })
    this.chartConfig = {
      series_name: '全省星级饭店',
      series_height: window.matchMedia('(min-width: 600px)').matches ? '68%' : '70%'
    }
  },
  methods: {
    async getData(params) {
      await getAction(
        import.meta.env.VITE_APP_SITUATION_BASE_URL + '/topic/data/listAllBySql/ydd_ly_xjfd',
        params
      ).then((res) => {
        if (res.success) {
          if (res.result.length === 0) {
            // this.$toast('该年度暂无数据')
            return
          }
          this.chartData = res.result
        }
      })
    },
    dropDownHandler(e) {
      this.getData({ opt: e })
    }
  }
}
</script>

<style lang="scss" scoped>
.starRestaurantBox {
  position: relative;
  height: 100%;
  .chart-container {
    width: 100%;
    height: 680px;
    // height: 680px;
    .chart {
      width: 100%;
      height: 100%;
    }
    .text {
      margin-top: -465px;
      text-align: center;
      .num {
        font-size: 40px;
        font-family: PingFangSC-Semibold, PingFang SC;
        font-weight: 600;
        color: #3b93fb;
      }
      .title2,
      .title1 {
        font-size: 28px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #666666;
      }
      .title1 {
        margin-bottom: 20px;
      }
    }
  }
  .dropdownBox {
    position: absolute;
    top: -30px;
    right: 0px;
    width: 222px;
    height: 60px;
    background: #ffffff;
    border-radius: 4px;
    border: 2px solid #3795ff;
  }
}
</style>
<style>
/* 下拉框box的高度 */
.starRestaurantBox > .dropdownBox .van-dropdown-menu__bar {
  height: 56px !important;
}
/* 下拉框item的宽度和位置 */
.starRestaurantBox > .dropdownBox .van-popup--top {
  top: 0px;
  left: 466px;
  width: 222px;
}
/* 取消选中item时右边  'v' */
.starRestaurantBox > .dropdownBox .van-cell__value {
  display: none;
}
/* 调整下拉框文字大小和arrow的距离 */
.starRestaurantBox > .dropdownBox .van-ellipsis {
  width: 160px;
  font-family: PingFangSC;
  font-size: 26px;
  color: #333333;
  line-height: 37px;
  text-align: left;
  font-style: normal;
}
.starRestaurantBox > .dropdownBox .van-dropdown-menu__title {
  padding: 0 !important;
}
/* 调整下拉框arrow的颜色 */
.starRestaurantBox > .dropdownBox .van-dropdown-menu__title::after {
  border-color: transparent transparent black black !important;
  opacity: 1 !important;
}
</style>
