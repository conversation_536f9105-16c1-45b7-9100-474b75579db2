<template>
  <div class="rowBox">
    <div v-for="item in boxData" :key="item.id" class="itemBox">
      <div class="itemName">{{ item.name }}</div>
      <div class="itemValue">
        {{ item.value }}<span>{{ item.unit }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import { postAction } from '@/api/manage.js'

export default {
  data() {
    return {
      boxData: []
    }
  },
  mounted() {
    this.getData()
  },
  methods: {
    async getData() {
      await postAction(import.meta.env.VITE_APP_SITUATION_BASE_URL + '/topic/data/listAllData/ydd_lyfm_jbxx', {}).then(
        (res) => {
          if (res.success) {
            this.boxData = res.result.data.slice(-2)
            this.boxData[0].name = '数量'
          }
        }
      )
    }
  }
}
</script>

<style lang="scss" scoped>
.rowBox {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  .itemBox {
    width: 305px;
    height: 172px;
    background-image: url('../img/travelAgenceBottombag.png');
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-repeat: no-repeat;
    background-size: 100% 172px;
    margin-bottom: 22px;

    div:first-child {
      font-family: PingFangSC, PingFang SC;
      font-weight: 600;
      font-size: 28px;
      color: #333333;
      text-align: right;
      font-style: normal;
    }
    div:last-child {
      font-family: D-DIN, D-DIN;
      font-weight: bold;
      font-size: 48px;
      color: #3b93fb;
      text-align: justify;
      font-style: normal;
      span {
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 24px;
        color: #666666;
        text-align: left;
        font-style: normal;
      }
    }
  }
  .itemBox:first-of-type {
    background-image: url('../img/travelAgenceTopbag.png');
  }
}
</style>
