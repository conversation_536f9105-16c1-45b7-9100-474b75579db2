<template>
  <div class="attracionsNumBox">
    <div class="chart-container">
      <div ref="chart" class="chart" />
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { getAction } from '@/api/manage.js'
export default {
  name: 'ServiceTextSingleLine',

  data() {
    return {
      myChart: null
    }
  },
  mounted() {
    window.addEventListener('resize', this.handleResize)
    this.getData()
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    async getData() {
      await getAction(import.meta.env.VITE_APP_SITUATION_BASE_URL + '/topic/data/listAllBySql/ydd_ly_lyjqs').then(
        (res) => {
          if (res.success) {
            this.getChart(res.result)
          }
        }
      )
    },
    getChart(chartData) {
      if (this.myChart === null) {
        this.myChart = echarts.init(this.$refs.chart, null, { renderer: 'svg' })
      }

      let cities = chartData.map((item) => item.name) // 获取所有城市名称
      let values = chartData.map((item) => item.value) // 获取所有数据值
      let yAxisData = cities // y 轴刻度值为所有城市名称
      let option = {
        title: {
          text: '单位:' + chartData[0].unit,
          textStyle: {
            fontFamily: 'PingFangSC, PingFang SC',
            fontWeight: '400',
            fontSize: '11px',
            color: '#73767f',
            textAlign: 'left',
            fontStyle: 'normal'
          },
          right: 0
        },
        dataset: {
          source: chartData
        },
        // tooltip: {
        //   trigger: 'axis',
        //   axisPointer: {
        //     type: 'shadow'
        //   },
        //   confine: 'true',
        //   extraCssText: 'z-index: 9;',
        //   valueFormatter: (value) => value + chartData[0].unit
        // },
        grid: {
          left: '0%',
          right: '10%',
          top: '10%',
          containLabel: true,
          bottom: '0%'
        },
        yAxis: {
          inverse: true,
          type: 'category',
          data: yAxisData, // 设置 y 轴刻度值为所有城市名称
          axisTick: {
            show: false
          },
          axisLabel: {
            fontSize: '11px',
            color: '#73767f'
          }
        },
        xAxis: {
          type: 'value',
          data: [0, 1, 2, 3, 4, 5],
          axisTick: {
            show: false
          },
          axisLabel: {
            fontSize: '12px',
            color: '#73767f'
          }
        },
        series: [
          {
            name: 'A级旅游景区数量:',
            type: 'bar',
            label: {
              show: true,
              position: 'right',
              fontSize: '12px',
              color: 'black'
            },
            color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
              {
                offset: 0,
                color: 'rgb(202, 225, 254)'
              },
              {
                offset: 1,
                color: 'rgb(64, 137, 255)'
              }
            ]),
            data: values
          }
        ]
      }

      this.myChart.setOption(option)
    },
    handleResize() {
      if (this.myChart) {
        this.myChart.resize()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.chart-container {
  width: 100%;
  height: 800px;
  .chart {
    width: 100%;
    height: 100%;
  }
  .text {
    margin-top: -275px;
    text-align: center;
    .num {
      font-size: 40px;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
      color: #3b93fb;
    }
    .title {
      font-size: 28px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #666666;
    }
  }
}
.addMargin {
  margin-bottom: 32px;
}
</style>
