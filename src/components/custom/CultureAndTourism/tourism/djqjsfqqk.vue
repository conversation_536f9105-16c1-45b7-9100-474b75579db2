<template>
  <div v-if="data2 && data1" class="databox">
    <div v-for="(item, i) in data1" :key="i" class="data">
      <div class="leftbox">
        <div style="display: flex; align-items: center">
          <img src="@/components/custom/CultureAndTourism/img/computer.png" alt="" class="icon" />
          <div class="text">{{ item.index_name }}</div>
        </div>

        <div class="valuebox">
          <span class="value">{{ item.index_value }}</span>
          <span class="unit">{{ item.unit }}</span>
        </div>
      </div>

      <div class="value2box">
        全国第
        <img v-if="data2[i].index_value == 1" src="../img/no1.png" alt="" />
        <img v-if="data2[i].index_value == 2" src="../img/no2.png" alt="" />
        <img v-if="data2[i].index_value == 3" src="../img/no3.png" alt="" />
      </div>
    </div>
  </div>
</template>
<script>
import { getAction } from '@/api/manage.js'

export default {
  data() {
    return {
      data1: [],
      data2: []
    }
  },
  mounted() {
    this.getData()
  },
  methods: {
    getData() {
      getAction(import.meta.env.VITE_APP_SITUATION_BASE_URL + '/topic/data/listAllBySql/ydd_ly_djqjsfq', {}).then(
        (res) => {
          if (res.success) {
            this.data1 = [res.result[0], res.result[2]]
            this.data2 = [res.result[1], res.result[3]]
            console.log(this.data1)
            console.log(this.data2)
          }
        }
      )
    }
  }
}
</script>

<style lang="scss" scoped>
.data {
  width: 100%;
  background-color: rgb(240, 247, 255);
  border-radius: 5px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 15px;
  margin-bottom: 20px;
  .icon {
    width: 30px;
    height: 30px;
    margin-right: 10px;
  }
  .text {
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    font-size: 28px;
    color: #333333;
    line-height: 28px;
    text-align: left;
    font-style: normal;
  }
  .leftbox {
    display: flex;
    align-items: center;
    width: 60%;
    justify-content: space-between;
  }
  .valuebox {
    display: flex;
    align-items: center;

    .value {
      font-family: D-DIN, D-DIN;
      font-weight: bold;
      font-size: 48px;
      color: #3b93fb;
      line-height: 48px;
      text-align: justify;
      font-style: normal;
    }
    .unit {
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 22px;
      color: #666666;
      line-height: 22px;
      text-align: center;
      font-style: normal;
    }
  }
  .value2box {
    display: flex;
    align-items: center;
    justify-content: start;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 26px;
    color: #666666;
    line-height: 26px;
    text-align: center;
    font-style: normal;
    img {
      width: 55px;
      height: 55px;
    }
  }
}
</style>
