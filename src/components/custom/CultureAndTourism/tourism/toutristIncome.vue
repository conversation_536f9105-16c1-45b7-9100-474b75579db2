<template>
  <div class="incomebox">
    <div class="chart-container">
      <div ref="chart" class="chart" />
    </div>
    <!-- <div class="table-container">
      <tableCpn
        name-data="旅游总收入(亿元)"
        :table-data="chartDataReverse"
        :show-num="isPad ? 3 : chartDataReverse.length"
      ></tableCpn>
    </div> -->
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { postAction } from '@/api/manage.js'
import tableCpn from '../components/tableCpn.vue'

export default {
  name: 'ServiceTextSingleLine',
  components: {
    tableCpn
  },
  data() {
    return {
      isRow2Hidden: true,
      currentData: {},
      myChart: null,
      chartData: [],
      chartDataReverse: [],
      isPad: false
    }
  },
  mounted() {
    window.addEventListener('resize', this.handleResize)
    this.getData()
    this.isPad = this.judgePad()
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    async getData() {
      await postAction(
        import.meta.env.VITE_APP_SITUATION_BASE_URL + '/topic/data/listAllData/ydd_ly_lyfm_lyzsr',
        {}
      ).then((res) => {
        if (res.success) {
          this.chartData = res.result.data
          this.chartDataReverse = this.chartData.slice().reverse()
          this.currentData = res.result.data[res.result.data.length - 1]
          this.getChart(res.result.data)
        }
      })
    },
    judgePad() {
      return window.matchMedia('(min-width: 600px)').matches
    },
    getChart(chartData) {
      let that = this
      if (this.myChart === null) {
        this.myChart = echarts.init(this.$refs.chart, null, { renderer: 'svg' })
      }
      let option = {
        dataset: {
          dimensions: ['name', 'value'],
          source: chartData
        },
        title: {
          text: '单位:' + chartData[0].unit,
          textStyle: {
            fontFamily: 'PingFangSC, PingFang SC',
            fontWeight: '400',
            fontSize: '11px',
            color: '#73767f',
            textAlign: 'left',
            fontStyle: 'normal'
          },
          right: 0
        },
        grid: {
          left: '0%',
          right: '3%',
          top: '15%',
          bottom: '0%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          axisLine: {
            lineStyle: {
              color: '#73767f'
            }
          },
          axisTick: { alignWithLabel: true },
          axisLabel: {
            fontSize: '11px',
            color: '#73767f',
            interval:0,
            rotate:45
          }
        },
        yAxis: {
          axisLabel: {
            fontSize: '11px',
            color: '#73767f'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          confine: 'true',
          extraCssText: 'z-index: 9;',
          valueFormatter: (value) => value + chartData[0].unit,
          backgroundColor: 'rgba(0,0,0,0.6)',
          textStyle: {
            color: 'white'
          }
        },
        series: [
          {
            symbolSize: 8,
            name: '收入',
            type: 'line',
            color: '#3b93fb',
            // label: {
            //   show: true
            // }
            label: {
              show: true,
              position: 'top',
              fontSize: '12px',
              color: 'black'
            }
          }
        ]
      }
      this.myChart.setOption(option)
      // 监听柱状图的点击事件
      this.myChart.on('click', function (params) {
        that.currentData = params.data
        that.$nextTick(() => {
          that.isRow2Hidden = false
        })
      })
    },
    handleResize() {
      if (this.myChart) {
        this.myChart.resize()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.chart-container {
  width: 100%;
  height: 500px;

  .chart {
    width: 100%;
    height: 100%;
  }

  .text {
    margin-top: -275px;
    text-align: center;

    .num {
      font-size: 40px;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
      color: #3b93fb;
    }

    .title {
      font-size: 28px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #666666;
    }
  }
}
</style>
