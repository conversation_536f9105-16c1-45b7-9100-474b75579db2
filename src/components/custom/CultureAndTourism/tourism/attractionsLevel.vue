<template>
  <div class="chart-container">
    <pieChart :chart-config="chartConfig" :chart-data="chartData" :chart-style="chartStyle"></pieChart>
  </div>
</template>

<script>
import pieChart from '../components/pieChart.vue'
import { postAction } from '@/api/manage.js'

export default {
  name: 'ServiceTextSingleLine',
  components: {
    pieChart
  },
  data() {
    return {
      chartData: [],
      rate: [],
      chartConfig: {
        
      },
      chartStyle: {
        width: '100%',
        height: '100%'
      },
      colorArr: ['#FFA26F', '#3CE2B0', '#66A0FF', '#FFD368', '#48E0E8', '#48E0E8', '#BD83FF']
    }
  },

  created() {
    this.getData()
    
  },
  mounted(){
this.chartConfig={
  series_name: 'A级旅游景区等级分布',
  series_height:window.matchMedia('(min-width: 600px)').matches ? '53%' : '70%'
}
  },
  methods: {
    async getData() {
      let sum = 0
      await postAction(
        import.meta.env.VITE_APP_SITUATION_BASE_URL + '/topic/data/listAllData/ydd_ly_lyjqdjfb',
        {}
      ).then((res) => {
        if (res.success) {
          this.chartData = res.result.data
          res.result.data.forEach((element) => {
            sum += +element.value
          })
          for (let i = 0; i < this.chartData.length; i++) {
            this.chartData[i].rate = ((100 * +this.chartData[i].value) / sum).toFixed(2)
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.chart-container {
  width: 100%;
  height: 680px;
}
</style>
