<template>
  <div class>
    <dataSummayBox>
      <template #row1>
        <dataSummayItem :config="top1Data"> </dataSummayItem>
      </template>
    </dataSummayBox>
    <slot></slot>
    <!-- <dataSummayBox>
      <template #row1>
        <dataSummayItem :config="top2Data"> </dataSummayItem>
      </template>
      <template #row2>
        <dataSummayItem :config="top3Data"> </dataSummayItem>
        <dataSummayItem :config="top4Data"> </dataSummayItem>
      </template>
    </dataSummayBox> -->
  </div>
</template>
<script>
import dataSummayItem from '../components/dataSummayItem.vue'
import dataSummayBox from '../components/dataSummayBox.vue'
import { postAction } from '@/api/manage.js'
import computerImg from '../img/computer.png'

export default {
  components: {
    dataSummayItem,
    dataSummayBox
  },
  data() {
    return {
      top1Data: {
        num: {
          content: 0
        },
        text: {
          content: ''
        },
        icon: {
          content: computerImg
        },
        unit: {
          content: ''
        }
      },

      top2Data: {
        text: {
          content: ''
        },
        icon: {
          content: computerImg
        }
      },

      top3Data: {
        baseStyle: {
          justifyContent: 'start',
          paddingLeft: '40px',
          marginTop: '20px',
          flex: 0.7
        },
        text: {
          content: '数量',
          style: {
            marginRight: '20px'
          }
        },
        unit: {
          content: ''
        },
        num: {
          content: 0
        }
      },

      top4Data: {
        baseStyle: {
          justifyContent: 'end',
          marginTop: '20px',
          flex: 1
        },
        text: {
          content: '',
          style: {
            marginRight: '12px'
          }
        },
        unit: {
          content: ''
        },
        num: {
          content: 0
        }
      }
    }
  },
  mounted() {
    this.getData()
  },
  methods: {
    async getData() {
      await postAction(import.meta.env.VITE_APP_SITUATION_BASE_URL + '/topic/data/listAllData/ydd_lyfm_jbxx', {}).then(
        (res) => {
          if (res.success) {
            this.setData(res.result.data)
          }
        }
      )
    },
    setData(responseData) {
      this.top1Data.text.content = responseData[0].name
      this.top1Data.num.content = responseData[0].value
      this.top1Data.unit.content = responseData[0].unit
      this.top2Data.text.content = responseData[1].name
      this.top3Data.num.content = responseData[1].value
      this.top3Data.unit.content = responseData[1].unit
      this.top4Data.text.content = responseData[2].name
      this.top4Data.unit.content = responseData[2].unit
      this.top4Data.num.content = responseData[2].value
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep {
  .leftContentBox {
    .text {
      width: 13em;
      line-height: 1.2;
    }
  }
}
</style>
