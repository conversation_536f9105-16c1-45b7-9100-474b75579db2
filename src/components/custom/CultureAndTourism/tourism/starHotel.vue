<template>
  <div class="chart-container">
    <pieChart :chart-config="chartConfig" :chart-data="chartData" :chart-style="chartStyle"></pieChart>
  </div>
</template>

<script>
import pieChart from '../components/pieChart.vue'
import { getAction } from '@/api/manage.js'
export default {
  name: 'ServiceTextSingleLine',
  components: {
    pieChart
  },
  data() {
    return {
      myChart: null,
      chartData: [],
      chartConfig: {},
      chartStyle: {
        width: '100%',
        height: '100%'
      }
    }
  },
  mounted() {
    this.getData()
    this.chartConfig = {
      series_name: '全省星级民宿',
      series_height: window.matchMedia('(min-width: 600px)').matches ? '68%' : '70%'
    }
  },

  methods: {
    async getData() {
      await getAction(import.meta.env.VITE_APP_SITUATION_BASE_URL + '/topic/data/listAllBySql/ydd_ly_xjlyms').then(
        (res) => {
          if (res.success) {
            this.chartData = res.result
          }
        }
      )
    }
  }
}
</script>

<style lang="scss" scoped>
.chart-container {
  width: 100%;
  height: 680px;
}
</style>
