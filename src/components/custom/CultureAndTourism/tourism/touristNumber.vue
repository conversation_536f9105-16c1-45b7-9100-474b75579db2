<template>
  <div>
    <tourismTop>
      <div class="numberbox">
        <div class="chart-container">
          <div ref="chart" class="chart" />
        </div>
      </div>
    </tourismTop>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import tourismTop from './tourismTop.vue'
import { postAction } from '@/api/manage.js'
import tableCpn from '../components/tableCpn.vue'
export default {
  name: 'ServiceTextSingleLine',
  components: {
    tourismTop,
    tableCpn
  },
  data() {
    return {
      myChart: null,
      isRow2Hidden: true,
      remValue: 0,
      chartData: [],
      chartDataReverse: [],
      isPad: false
    }
  },
  mounted() {
    this.setRemValue()
    window.addEventListener('resize', this.handleResize)
    this.getData()
    this.isPad = this.judgePad()
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    async getData() {
      await postAction(
        import.meta.env.VITE_APP_SITUATION_BASE_URL + '/topic/data/listAllData/ydd_ly_lyfm_lyzrs',
        {}
      ).then((res) => {
        if (res.success) {
          this.chartData = res.result.data
          this.chartDataReverse = this.chartData.slice().reverse()
          this.getChart(res.result.data)
        }
      })
    },
    judgePad() {
      return window.matchMedia('(min-width: 600px)').matches
    },
    getChart(chartData) {
      if (this.myChart === null) {
        this.myChart = echarts.init(this.$refs.chart, null, { renderer: 'svg' })
      }
      let option = {
        dataset: {
          dimensions: ['name', 'value'],
          source: chartData
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          confine: 'true',
          extraCssText: 'z-index: 9;',
          valueFormatter: (value) => value + chartData[0].unit,
          backgroundColor: 'rgba(0,0,0,0.6)',
          textStyle: {
            color: 'white'
          }
        },

        title: {
          text: '单位:' + chartData[0].unit,
          textStyle: {
            fontFamily: 'PingFangSC, PingFang SC',
            fontWeight: '400',
            fontSize: '11px',
            color: '#99973767f999',
            textAlign: 'left',
            fontStyle: 'normal'
          },
          right: 0
        },
        grid: {
          left: '0%',
          right: '1%',
          top: '15%',
          bottom: '0%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          axisLine: {
            lineStyle: {
              color: '#73767f'
            }
          },
          axisTick: { alignWithLabel: true },
          axisLabel: {
            fontSize: '11px',
            color: '#73767f',
            interval:0,
            rotate:45

          }
        },
        yAxis: {
          axisLabel: {
            fontSize: '11px',
            color: '#73767f'
          }
        },
        series: [
          {
            symbolSize: 8,
            smooth: true,
            name: '旅游总人数:',
            type: 'line',
            color: '#3b93fb',
            label: {
              show: true,
              fontSize:12,
              position: 'top',
              color: 'black'
            },
            areaStyle: {
              opacity: 0.8,
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: '#58a8f9'
                },
                {
                  offset: 1,
                  color: 'white'
                }
              ])
            }
          }
        ]
      }
      this.myChart.setOption(option)
    },
    setRemValue() {
      //10px =>  10  拿到1px的值
      this.remValue = parseFloat(document.documentElement.style.fontSize)
    },
    handleResize() {
      this.setRemValue()
      if (this.myChart) {
        this.myChart.resize({ width: 'auto', height: 'auto' })
      }
      // this.myChart.setOption({
      //   xAxis: {
      //     axisLabel: {
      //       fontSize: 20 * this.remValue
      //     }
      //   },
      //   yAxis: {
      //     axisLabel: {
      //       fontSize: 20 * this.remValue
      //     }
      //   }
      // })
    }
  }
}
</script>

<style lang="scss" scoped>
.chart-container {
  width: 100%;
  height: 500px;
  .chart {
    width: 100%;
    height: 100%;
  }
  .text {
    margin-top: -275px;
    text-align: center;
    .num {
      font-size: 40px;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
      color: #3b93fb;
    }
    .title {
      font-size: 28px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #666666;
    }
  }
}
</style>
