<template>
  <div class="total-container">
    <div>
      <!-- <div class="title">博物馆数量</div> -->
      <dataSummayCard v-for="(item, i) in dataSummayCardConfig" :key="i" :config="item">
        <div v-if="i == 0" class="slotvalue">
          全国第
          <img src="../img/no1.png" alt="" />
        </div>
      </dataSummayCard>
    </div>

    <!-- <div class="title">博物馆级别分布情况</div> -->
    <div class="chart-container">
      <pieChart :chart-config="chartConfig" :chart-data="chartData" :chart-style="chartStyle"></pieChart>
    </div>
  </div>
</template>
<script>
import pieChart from '../components/pieChart.vue'
import dataSummayCard from '../components/dataSummayCard.vue'
import { postAction } from '@/api/manage.js'
import itemimg10 from '../img/itemimg10.png'
import itemimg11 from '../img/itemimg11.png'

export default {
  components: {
    dataSummayCard,
    pieChart
  },
  data() {
    return {
      chartData: [],
      chartConfig: {
        tooltip_valueFormatter: (value) => value + this.chartData[0].unit,
        series_name: '各省各级博物馆分类',
        series_label_formatter: (params) => {
          return `${params.data[1]}\n${params.percent}%`
        },
        series_top: window.matchMedia('(min-width: 600px)').matches ? '0%' : '5%'
      },
      chartStyle: {
        width: '100%',
        height: '100%'
      },
      myChart: null,
      dataSummayCardConfig: [],
      windowConst: window
    }
  },
  mounted() {
    this.getData1()
    this.getData2()
  },
  methods: {
    async getData1() {
      await postAction(import.meta.env.VITE_APP_SITUATION_BASE_URL + '/topic/data/listAllData/ydd_ly_ww_bwg', {}).then(
        (res) => {
          if (res.success) {
            this.setData(res.result.data)
          }
        }
      )
    },
    async getData2() {
      await postAction(
        import.meta.env.VITE_APP_SITUATION_BASE_URL + '/topic/data/listAllData/ydd_ly_ww_qsgjbwgfl',
        {}
      ).then((res) => {
        if (res.success) {
          this.chartData = res.result.data
        }
      })
    },
    setData(responseData) {
      const iconArr = [itemimg10, itemimg11]
      //映射一个新的数组,里面添加icon. 将数据交给组件使用
      this.dataSummayCardConfig = responseData
        .map((item, index) => {
          return {
            icon: iconArr[index],
            ...item
          }
        })
        .reverse()
    }
  }
}
</script>

<style lang="scss" scoped>
.title {
  font-family: PingFangSC, PingFang SC;
  font-weight: 600;
  font-size: 28px;
  color: #3b93fb;
  line-height: 40px;
  text-align: left;
  font-style: normal;
  padding-top: 26px;
  display: flex;
  align-items: center;
  justify-content: center;
}
div.title:first-of-type {
  padding: 0;
}
.chart-container {
  width: 100%;
  height: 680px;
  .chart {
    width: 100%;
    height: 100%;
  }
  .text {
    margin-top: -465px;
    text-align: center;
    .num {
      font-size: 40px;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
      color: #3b93fb;
    }
    .title2,
    .title1 {
      font-size: 28px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #666666;
    }
    .title1 {
      margin-bottom: 20px;
    }
  }
}
.slotvalue {
  display: flex;
  align-items: center;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 3.466667vw;
  margin-left: 40px;
  color: #666666;
  text-align: center;
  font-style: normal;
  img {
    width: 55px;
  }
}

</style>
