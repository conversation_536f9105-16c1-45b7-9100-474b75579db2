<template>
  <div class="total-container">
    <dataSummayCard v-for="(item, i) in dataSummayCardConfig" :key="i" :config="item"></dataSummayCard>
  </div>
</template>
<script>
import dataSummayCard from '../components/dataSummayCard.vue'
import { postAction } from '@/api/manage.js'
import itemimg1 from '../img/itemimg1.png'
import itemimg2 from '../img/itemimg2.png'
import itemimg3 from '../img/itemimg3.png'
import itemimg4 from '../img/itemimg4.png'
import itemimg5 from '../img/itemimg5.png'

export default {
  components: {
    dataSummayCard
  },
  data() {
    return {
      dataSummayCardConfig: []
    }
  },
  mounted() {
    this.getData()
  },
  methods: {
    async getData() {
      await postAction(import.meta.env.VITE_APP_SITUATION_BASE_URL + '/topic/data/listAllData/ydd_ly_whttcs', {}).then(
        (res) => {
          if (res.success) {
            this.setData(res.result.data)
          }
        }
      )
    },
    setData(responseData) {
      const iconArr = [itemimg1, itemimg2, itemimg3, itemimg4, itemimg5, itemimg5, itemimg5]
      //映射一个新的数组,里面添加icon. 将数据交给组件使用
      this.dataSummayCardConfig = responseData.slice(2).map((item, index) => {
        return {
          icon: iconArr[index],
          ...item
        }
      })
      this.dataSummayCardConfig = this.dataSummayCardConfig.slice(this.dataSummayCardConfig.length - 2)
    }
  }
}
</script>

<style lang="scss" scoped>
.cultureBox {
  display: flex;
  justify-content: space-between;
  .dataSummaryBox1 {
    width: 49%;
    padding: 15px 30px;
    background-color: rgb(240, 247, 255);
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    align-items: center;
    align-content: space-between;
    margin-bottom: 16px;
    .row1 {
      width: 100%;
      font-family: PingFangSC, PingFang SC;
      font-weight: 600;
      font-size: 26px;
      color: #333333;
      line-height: 34px;
      text-align: center;
      font-style: normal;
      margin-bottom: 10px;
    }
    .row2 {
      display: flex;
      align-items: baseline;
      .num {
        font-family: D-DIN, D-DIN;
        font-weight: bold;
        font-size: 38px;
        color: #3b93fb;
        line-height: 38px;
        text-align: justify;
        font-style: normal;
      }
      .unit {
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 22px;
        color: #666666;
        line-height: 22px;
        text-align: center;
        font-style: normal;
      }
    }
  }
}
::v-deep .dataSummayItemBox:first-of-type {
  margin: 0 0 24px !important;
}
</style>
