<template>
  <div class="total-container">
    <div class="item-container">
      <div class="title">代表性项目数量</div>
      <dataSummayCard v-for="(item, i) in dataSummayCardConfig1" :key="'config1_' + item.id" :config="item">
        <div v-if="i == 1" class="slotvalue">
          全国第
          <img src="../img/no2.png" alt="" />
        </div>
      </dataSummayCard>
    </div>
    <div v-if="windowdata" class="line"></div>
    <div class="item-container">
      <div class="title">代表性传承人</div>
      <dataSummayCard v-for="(item, i) in dataSummayCardConfig2" :key="'config2_' + item.id" :config="item">
        <div v-if="i == 0" class="slotvalue">
          <span>{{ item.name2 }}</span><span class="slotvlaue2">{{ item.value2 }}</span>
        </div>
      </dataSummayCard>
    </div>
  </div>
</template>
<script>
import { postAction } from '@/api/manage.js'
import dataSummayCard from '../components/dataSummayCard.vue'
import itemimg6 from '../img/itemimg6.png'
import itemimg7 from '../img/itemimg7.png'
import itemimg8 from '../img/itemimg8.png'
import itemimg9 from '../img/itemimg9.png'

export default {
  components: {
    dataSummayCard
  },
  data() {
    return {
      dataSummayCardConfig1: [],
      dataSummayCardConfig2: [],
      windowdata: false
    }
  },
  mounted() {
    this.getData1()
    this.getData2()
    this.windowdata = window.matchMedia('(min-width: 600px)').matches
  },
  methods: {
    async getData1() {
      await postAction(
        import.meta.env.VITE_APP_SITUATION_BASE_URL + '/topic/data/listAllData/ydd_ly_fwzwhycdbxxmsl',
        {}
      ).then((res) => {
        if (res.success) {
          this.setData(res.result.data, 1)
        }
      })
    },
    async getData2() {
      await postAction(
        import.meta.env.VITE_APP_SITUATION_BASE_URL + '/topic/data/listAllData/ydd_ly_fwzwhycdbxccrs',
        {}
      ).then((res) => {
        if (res.success) {
          this.setData(res.result.data, 2)
        }
      })
    },
    setData(responseData, type) {
      const iconArr = [itemimg6, itemimg7, itemimg8, itemimg9]
      //映射一个新的数组,里面添加icon. 将数据交给组件使用
      responseData.forEach((item, index) => {
        let obj = {}
        switch (type) {
          case 1:
            obj = {
              icon: iconArr[index],
              ...item
            }
            this.dataSummayCardConfig1.push(obj)
            break
          case 2:
            obj = {
              icon: iconArr[index + 2],
              ...item
            }
            this.dataSummayCardConfig2.push(obj)
            this.dataSummayCardConfig2.reverse()
            break
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep {
  .dataSummayItemBox {
    .text {
      width: 9em;
    }
  }
}
.title {
  font-family: PingFangSC, PingFang SC;
  font-weight: 600;
  font-size: 28px;
  color: #3b93fb;
  line-height: 40px;
  text-align: left;
  font-style: normal;
  padding: 26px 0;
  display: flex;
  align-items: center;
  justify-content: center;
}
div.title:first-of-type {
  padding: 0 0 26px 0;
}
.slotvalue {
  display: flex;
  align-items: center;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 3.466667vw;
  margin-left: 40px;
  color: #666666;
  text-align: center;
  font-style: normal;
  img {
    width: 55px;
  }
}
.slotvlaue2 {
  color: #fbba78;
  font-size: 28px;
  // margin-top: -3px;
}
.line {
  width: 1px;
  height: 250px;
  margin: auto 12px;
  background: linear-gradient(to bottom, #f1f4fa 0%, #5c8bd4 50%, #f1f4fa 100%) center / 100% no-repeat;
}
::v-deep {
  .leftContentBox {
    flex: 2.8 !important;
  }
}
</style>
