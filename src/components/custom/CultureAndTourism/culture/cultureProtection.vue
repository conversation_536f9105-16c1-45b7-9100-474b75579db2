<template>
  <div class="chart-container">
    <div ref="chart" class="chart" />
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { postAction } from '@/api/manage.js'
export default {
  name: 'ServiceTextSingleLine',
  data() {
    return {
      myChart: null
    }
  },
  mounted() {
    window.addEventListener('resize', this.handleResize)
    this.getData()
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    async getData() {
      await postAction(
        import.meta.env.VITE_APP_SITUATION_BASE_URL + '/topic/data/listAllData/ydd_ly_ww_wwbhdw',
        {}
      ).then((res) => {
        if (res.success) {
          this.getChart(res.result.data)
        }
      })
    },
    getChart(chartData) {
      if (this.myChart === null) {
        this.myChart = echarts.init(this.$refs.chart, null, { renderer: 'svg' })
      }
      let option = {
        title: {
          text: '单位:' + '家',
          textStyle: {
            fontFamily: 'PingFangSC, PingFang SC',
            fontWeight: '400',
            fontSize: '11px',
            color: '#73767f',
            textAlign: 'left',
            fontStyle: 'normal'
          },
          top: '25px',
          right: 0
        },
        grid: {
          left: '0%',
          right: '1%',
          top: '30%',
          bottom: '5%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: chartData.map((item) => item.nd),
          axisLine: {
            lineStyle: {
              color: '#73767f'
            }
          },
          axisTick: { alignWithLabel: true },
          axisLabel: {
            fontSize: 11,
            interval: 0,
            width: 40,
            overflow: 'break',
            color: '#73767f'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          confine: 'true',
          extraCssText: 'z-index: 9;',
          valueFormatter: (value) => value + '家',
          backgroundColor: 'rgba(0,0,0,0.6)',
          textStyle: {
            color: 'white'
          }
        },
        yAxis: {
          axisLabel: {
            fontSize: '11px',
            color: '#73767f'
          }
        },

        legend: {
          data: ['国家级文物保护单位', '省级文物保护单位'], // 设置图例项
          show: true,
          textStyle: {
            color: '#73767f',
            fontSize: '11px'
          },
          itemWidth: 10, // 设置图例小图标宽度为20像素
          itemHeight: 10, // 设置图例小图标高度为10像素
          left: 0
        },
        series: [
          {
            name: '国家级文物保护单位',
            type: 'bar',
            data: chartData.map((item) => item.gjjwwbhdw), // 假设 chartData 中的每个对象都有 value 属性表示柱状图的数据
            color: '#3b93fb',
            label: {
              show: true,
              position: 'top',
              fontSize: '12px',
              color: 'black'
            },
            barWidth: 20 // 设置柱体宽度为30像素
          },
          {
            name: '省级文物保护单位',
            type: 'bar',
            data: chartData.map((item) => item.sjwwbhdw), // 如果有第二个柱状图，也按照相同的方式设置数据
            color: '#FFAB67',
            label: {
              show: true,
              position: 'top',
              fontSize: '12px',
              color: 'black'
            },
            barWidth: 20 // 设置柱体宽度为30像素
          }
        ]
      }
      this.myChart.setOption(option)
    },
    handleResize() {
      if (this.myChart) {
        this.myChart.resize()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.chart-container {
  width: 100%;
  height: 455px;
  margin-bottom: -20px;
  .chart {
    width: 100%;
    height: 100%;
  }
}
</style>
