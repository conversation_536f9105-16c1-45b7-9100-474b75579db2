<template>
  <div class="total-container">
    <dataSummayCard v-for="(item, i) in dataSummayCardConfig" :key="i" :config="item">
      <div v-if="i == 0" class="slotvalue">
        全国第
        <img src="../img/no1.png" alt="" />
      </div>
    </dataSummayCard>
  </div>
</template>
<script>
import dataSummayCard from '../components/dataSummayCard.vue'
import { postAction } from '@/api/manage.js'
import itemimg10 from '../img/itemimg10.png'
import itemimg11 from '../img/itemimg11.png'

export default {
  components: {
    dataSummayCard
  },
  data() {
    return {
      dataSummayCardConfig: []
    }
  },
  mounted() {
    this.getData1()
  },
  methods: {
    async getData1() {
      await postAction(import.meta.env.VITE_APP_SITUATION_BASE_URL + '/topic/data/listAllData/ydd_ly_ww_bwg', {}).then(
        (res) => {
          if (res.success) {
            this.setData(res.result.data)
          }
        }
      )
    },
    setData(responseData) {
      const iconArr = [itemimg10, itemimg11]
      //映射一个新的数组,里面添加icon. 将数据交给组件使用
      this.dataSummayCardConfig = responseData.map((item, index) => {
        return {
          icon: iconArr[index],
          ...item
        }
      }).reverse()
    }
  }
}
</script>

<style lang="scss" scoped>
.slotvalue {
  display: flex;
  align-items: center;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  margin-left: 20px;
  color: #666666;
  text-align: center;
  font-style: normal;
  img {
    width: 27px;
  }
}
</style>
