<template>
  <BarWithNewTextData ref="BarWithNewTextData" class="chart" :type="1" tooltip-unit="万吨"></BarWithNewTextData>
</template>

<script>
import BarWithNewTextData from '@/components/charts/BarWithNewTextData.vue'
import { getAction, postAction } from '@/api/manage'
export default {
  components: {
    BarWithNewTextData
  },
  data() {
    return {
      chartData: {
        data: [],
        column: ['name', 'value1', 'value2'],
        columnName: { name: '省份', value1: '2022年调入量', value2: '2023年1-11月调入量' },
        unitList: { name: '', value1: '万吨', value2: '万吨' }
      }
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    async loadData() {
      const res = await getAction(
        import.meta.env.VITE_APP_SITUATION_BASE_URL + '/topic/data/listAllBySql/ydd_ny_mtly_swdrmtl_sql',
        {}
      )
      if (res.success) {
        this.chartData.data = res.result.map((item) => {
          return {
            name: item.province_name,
            value1: item.previous_value,
            value2: item.current_value
          }
        })
        this.$refs.BarWithNewTextData.loadChart(this.chartData)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.chart {
  margin-bottom: -25px;
}
</style>
