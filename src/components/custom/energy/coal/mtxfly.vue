<template>
  <div class="sortBox">
    <div class="sortTitle">
      <div>
        <div>时间</div>
        <div>(年)</div>
      </div>
      <div>
        <div>总量</div>
        <div>(万吨)</div>
      </div>
      <div>
        <div>省内</div>
        <div>(万吨)</div>
      </div>
      <div>
        <div>省外</div>
        <div>(万吨)</div>
      </div>
    </div>
    <div v-for="(item, i) in sortData" :key="i" class="sortItem">
      <div>{{ item.value1 }}</div>
      <div>{{ item.value2 }}</div>
      <div>{{ item.value3 }}</div>
      <div>{{ item.value4 }}</div>
    </div>
  </div>
</template>
<script>
import { getAction, postAction } from '@/api/manage'

export default {
  data() {
    return {
      sortData: []
    }
  },
  created() {
    this.loadData()
  },
  methods: {
    loadData() {
      const url = '/topic/data/listAllBySql/ydd_ny_mtly_jnqnmtxfl_sql'
      getAction(import.meta.env.VITE_APP_SITUATION_BASE_URL + url).then((res) => {
        this.sortData = res.result.map((item) => {
          return {
            value1: item.year,
            value2: item.cost_total,
            value3: item.cost_in_province,
            value4: item.cost_out_province
          }
        })
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.sortBox {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  width: 100%;
  height: 325px;
  .sortTitle,
  .sortItem {
    padding: 8px;
  }

  .sortTitle,
  .sortItem {
    width: 100%;
    height: 90px;
    background: #0089ff;
    border-radius: 12px;
    display: flex;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    // font-size: 13px;
    font-size: 26px;
    color: #ffffff;
    text-align: right;
    font-style: normal;
    justify-content: space-around;
    align-items: center;
    div {
      // display: flex;
      // align-items: center;
      text-align: center;
      // justify-content: center;
    }
    div:nth-of-type(1) {
      // width: 4em;
      flex: 1;
    }
    div:nth-of-type(2) {
      // width: 6em;
      flex: 1;
    }
    div:nth-of-type(3) {
      // width: 6em;
      flex: 1;
    }
    div:nth-of-type(4) {
      // width: 6em;
      flex: 1;
    }
  }
  .sortItem {
    height: 94px;
    background: #f6faff;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    // font-size: 12px;
    font-size: 26px;
    color: #0065bb;
    text-align: center;
    font-style: normal;
  }
}
</style>
