<template>
  <!-- 煤炭调出量 -->
  <div class="chart-container">
    <barLine :chart-data="chartData" :type="2"></barLine>
  </div>
</template>
<script>
import barLine from '../electricity/components/barLine.vue'
import { getAction } from '@/api/manage'
export default {
  components: { barLine },
  data() {
    return {
      chartData: {
        data: [],
        column: ['name', 'value1', 'value2'],
        columnName: { name: '', value1: '调出量', value2: '同比增长率' },
        unitList: { name: '', value1: '万吨', value2: '%' },
        type: ['bar', 'line'],
        colors: ['#0192f8', '#39c56a'],
        xLabelLineStrNum: 5
        // rotateX: 45
        // 一次显示条数占总数百分比
        // showProp: 30
      }
    }
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      getAction(
        import.meta.env.VITE_APP_SITUATION_BASE_URL + '/topic/data/listAllBySql/ydd_ny_mtly_swmtdclws_month',
        {}
      ).then((res) => {
        this.chartData.data = res.result.map((item) => {
          return {
            name: item.year,
            value1: item.coal_amount,
            value2: item.growth_yoy
          }
        })
      })
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .echarts-container {
  height: 510px;
}
</style>
