<template>
  <!-- 煤炭消费用途 -->
  <div class="chart-container">
    <pieChart :chart-data="chartData" :chart-config="chartConfig" :other-config="otherConfig" />
  </div>
</template>

<script>
import { getAction, postAction } from '@/api/manage.js'
import pieChart from '../common/pieChart.vue'

export default {
  components: { pieChart },
  props: {
    ispad: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      chartData: [],
      chartConfig: {},
      otherConfig: {
        labelType: 4,
        legendType: -1,
        legendColumn: 2
      }
    }
  },
  mounted() {
    this.getData()
  },
  methods: {
    async getData() {
      await getAction(
        import.meta.env.VITE_APP_SITUATION_BASE_URL + '/topic/data/listAllBySql/ydd_ny_mtly_mtxfyt_sql'
      ).then((res) => {
        if (res.success) {
          this.chartData = res.result
          this.chartConfig = {
            series_encode: {
              itemName: 'cost_category',
              value: 'cost_value'
            },
            tooltip_valueFormatter: (value) => `${value}万吨`,
            series_name: '煤炭消费用途',
            radius: window.matchMedia('(min-width: 600px)').matches ? ['30%', '50%'] : null,
            series_center: window.matchMedia('(min-width: 600px)').matches ? ['50%', '50%'] : ['50%', '50%'],
            label_formatter: (params) => {
              return `{a|${params.data.cost_category}}\n${params.data.cost_ratio}%`
            },
            startAngle: 270
          }
          if (window.matchMedia('(min-width: 600px)').matches) {
            this.otherConfig.legendType = -1
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.chart-container {
  height: 350px;
}
</style>
