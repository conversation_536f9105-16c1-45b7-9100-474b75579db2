<template>
  <!-- 原煤产量 -->
  <div class="box">
    <div class="tabBox">
      <div class="tabs">
        <div :class="{ activetab: active === 0 }" @click="handleClickTab(0)">月度累计(1-4月)</div>
        <div :class="{ activetab: active === 1 }" @click="handleClickTab(1)">年度(2023年)</div>
      </div>
    </div>
    <div v-if="clData != null" class="text-wrapper">
      <NewTextData
        show-icon
        single-line
        title="累计煤矿原煤产量"
        :value="clData.raw_coal_product"
        unit="万吨"
      ></NewTextData>
      <NewTextData
        show-icon
        single-line
        title="累计原煤入选量"
        :value="clData.raw_coal_select"
        unit="万吨"
      ></NewTextData>
      <NewTextData show-icon single-line title="累计商品煤销售量" :value="clData.coal_sale" unit="万吨"></NewTextData>
    </div>
  </div>
</template>

<script>
import { getAction } from '@/api/manage'
import { changeVuex } from '@/api/changeDataProperty.js'
import NewTextData from '@/components/charts/NewTextData.vue'
export default {
  components: {
    NewTextData
  },
  data() {
    return {
      chartData: {
        data: [],
        column: ['name', 'value1', 'value2', 'value3'],
        columnName: { name: '时间', value1: '总量', value2: '省内', value3: '省外' },
        unitList: { name: '年', value1: '万吨', value2: '万吨', value3: '万吨' }
      },
      active: 0,
      clData: {}
    }
  },
  created() {
    this.getData()
  },
  methods: {
    async handleClickTab(index) {
      this.active = index
      const name = index === 0 ? '月度累计' : '年度'
      const id = 8
      changeVuex(id, name)
      this.getData()
    },
    async getData() {
      const url =
        this.active == 0
          ? 'topic/data/listAllBySql/ydd_ny_mtly_ndljcl_month'
          : 'topic/data/listAllBySql/ydd_ny_mtly_ndljcl_year'
      const res = await getAction(import.meta.env.VITE_APP_SITUATION_BASE_URL + url, { year: this.defaultValue })
      this.clData = res.result[0]
    }
  }
}
</script>

<style lang="scss" scoped>
.tabBox {
  display: flex;
  justify-content: flex-end;
  padding: 0 20px;

  .tabs {
    width: 100%;
    display: flex;
    justify-content: space-around;
    align-items: center;
    margin-bottom: 10px;

    > div {
      width: 45%;
      height: 68px;
      display: flex;
      flex: 1;
      justify-content: center;
      align-items: center;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 28px;
      color: #020202;
      line-height: 54px;
      text-align: center;
      font-style: normal;
      border-radius: 40px;
      background: #f6f6f6;
      &:first-child {
        margin-right: 20px;
      }
    }

    .activetab {
      background-color: #ddeafc;
      color: #008cff;
    }
  }
}
.box {
  width: 100%;
  position: relative;
  .text-wrapper {
    div {
      margin-top: 20px;
    }
  }
}
</style>
