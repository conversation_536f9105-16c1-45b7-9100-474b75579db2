<template>
  <div class="wrapper">
    <div class="item">
      <div>
        <div class="icon"></div>
        <div class="title">{{ data.field_name }}</div>
      </div>
      <div>
        <div class="num">{{ data.field_value }}</div>
        <div class="unit">{{ data.unit }}</div>
      </div>
    </div>
  </div>
</template>
<script>
import { getAction, postAction } from '@/api/manage'
export default {
  data() {
    return {
      data: {}
    }
  },
  mounted() {
    this.getData()
  },
  methods: {
    getData() {
      getAction(import.meta.env.VITE_APP_SITUATION_BASE_URL + '/topic/data/listAllBySql/ydd_ny_mtly_mksy_add1').then(
        (res) => {
          this.data = res.result[0]
        }
      )
    }
  }
}
</script>
<style lang="scss" scoped>
.wrapper {
  .item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 646px;
    height: 104px;
    background: url('./img/coal-icon-1.png') no-repeat center center / 100% 100%;
    padding: 20px 40px 20px 20px;
    > div {
      display: flex;
      align-items: baseline;
      &:nth-child(1) {
        align-items: center;
      }
    }
    .icon {
      height: 56px;
      width: 56px;
      background: url('./img/coal-icon-2.png') no-repeat center center / 100% 100%;
    }
    .title {
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 32px;
      color: #212121;
      line-height: 28px;
      text-align: left;
      font-style: normal;
      margin-left: 10px;
    }
    .num {
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 48px;
      color: #3b93fb;
      line-height: 48px;
      text-align: right;
      font-style: normal;
    }
    .unit {
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 24px;
      color: #666666;
      line-height: 28px;
      text-align: left;
      font-style: normal;
    }
  }
}
</style>
