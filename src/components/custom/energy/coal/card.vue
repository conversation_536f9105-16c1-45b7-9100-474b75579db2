<template>
  <div class="total-container">
    <div class="card">
      <div class="top-time">
        <div class="time">
          <span>{{ data?.field_value }}</span>
        </div>
      </div>
      <textBox :text-data="textData"></textBox>
    </div>
  </div>
</template>

<script>
import { getAction, postAction } from '@/api/manage'
import textBox from '../common/textBox.vue'
export default {
  name: 'Energy2',
  components: { textBox },
  data() {
    return {
      data: [],
      textData: []
    }
  },
  computed: {},
  mounted() {
    this.init()
  },
  methods: {
    init() {
      // 煤炭概况
      getAction(import.meta.env.VITE_APP_SITUATION_BASE_URL + '/topic/data/listAllBySql/ydd_ny_qsmtgk_kp', {}).then(
        (res) => {
          this.data = res.result[0]
          this.textData = res.result.slice(1, 3).map((item, i) => {
            return {
              value1: item.field_name,
              value2: item.field_value,
              unit: i == 0 ? '处' : '万吨/年'
            }
          })
        }
      )
    }
  }
}
</script>

<style lang="scss" scoped>
.total-container {
  .card {
    width: 100%;
    height: 320px;
    background: url('@/assets/img/smallcard/smallcard2.png') no-repeat center center;
    background-size: 100% 100%;
    position: relative;
    padding: 20px 40px;
    .top-time {
      font-family: SourceHanSansCN, SourceHanSansCN;
      font-weight: 400;
      font-size: 22px;
      color: #ffffff;
      text-align: center;
      margin-left: 11px;
      background: url('@/assets/img/date_bg.png') no-repeat center/100% 48px;
      align-items: center;
      position: absolute;
      display: flex;
      align-items: center;
      justify-content: center;
      right: -5px;
      width: 230px;
      height: 48px;
      top: 30px;
      padding-bottom: 8px;
    }
  }
}
::v-deep.box {
  width: 100%;
  height: 100%;
  .boxdiv:first-of-type {
    flex: 1;
  }
  .boxdiv:last-of-type {
    flex: 1;
  }
  .border {
    border-image: linear-gradient(to bottom, rgba(93, 140, 212, 0.5), white, rgba(93, 140, 212, 0.5)) 1;
  }
  .textBox {
    height: 170px;
    margin-top: 55px;
  }
  .value1 {
    color: white;
    font-size: 32px;
    line-height: 35px;
    max-width: unset;
    font-weight: 500;
  }
  .value2 {
    // color: #f4fd30;
    font-weight: 500;
    font-size: 68px;
    color: white;
  }
  .unit {
    color: white;
    margin-left: 10px;
    font-size: 28px;
  }
}
</style>
