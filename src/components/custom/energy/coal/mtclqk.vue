<template>
  <div class="wrapper">
    <div v-show="!showChart" class="show-chart-icon" @click="handleChartShow"></div>
    <div v-show="showChart" class="hide-chart-icon" @click="handleChartShow"></div>
    <div class="item">
      <div>
        <div class="icon"></div>
        <div class="title">{{ data.index_name }}</div>
      </div>
      <div>
        <div class="num">{{ data.index_value }}</div>
        <div class="unit">{{ data.unit }}</div>
      </div>
    </div>
    <bar v-show="showChart" :chart-data="chartData" class="bar" :type="isTest"></bar>
  </div>
</template>
<script>
import bar from './components/bar.vue'
import { getAction, postAction } from '@/api/manage'

export default {
  components: { bar },
  data() {
    return {
      data: {},
      isPad: window.matchMedia('(max-width: 600px)').matches,
      showChart: false,
      isTest: false,
      chartData: {
        data: [],
        column: ['name', 'value'],
        columnName: { name: '', value: '产量' },
        unitList: { name: '', value: '万吨' },
        type: ['bar']
        // colors: ['#0192f8', '#39c56a'],
        // rotateX: 45
        // 一次显示条数占总数百分比
        // showProp: 30
      }
    }
  },
  mounted() {
    const baseUrl = import.meta.env.VITE_APP_SITUATION_BASE_URL
    this.isTest = baseUrl.includes('/situation_dev/')
    this.getData()
    this.getChartData()
  },
  methods: {
    getData() {
      getAction(import.meta.env.VITE_APP_SITUATION_BASE_URL + '/topic/data/listAllBySql/ydd_ny_qsmtgk_mtclqk_zx').then(
        (res) => {
          this.data = res.result[0]
        }
      )
    },
    getChartData() {
      getAction(import.meta.env.VITE_APP_SITUATION_BASE_URL + '/topic/data/listAllBySql/ydd_ny_qsmtgk_mtclqk').then(
        (res) => {
          this.chartData.data = res.result.map((item) => {
            return {
              name: item.index_time,
              value: item.index_value
            }
          })
          if (this.isTest) {
            this.chartData.data.push({
              name: '2025',
              value: '8750.5',
              itemStyle: {
                color: '#a90000'
              }
            })
          }
        }
      )
    },
    handleChartShow() {
      this.showChart = !this.showChart
      this.$emit('showChart', this.showChart)
    }
  }
}
</script>
<style lang="scss" scoped>
.wrapper {
  position: relative;
  .show-chart-icon {
    height: 80px;
    width: 200px;
    background: url('./img/showChart-icon.png') no-repeat center center / 100% 100%;
    position: absolute;
    right: 0;
    top: -80px;
  }
  .hide-chart-icon {
    height: 80px;
    width: 200px;
    background: url('./img/hideChart-icon.png') no-repeat center center / 100% 100%;
    position: absolute;
    right: 0;
    top: -80px;
  }
  .bar {
    // height: 300px;
    // width: 600px;
    ::v-deep .echarts-container {
      height: 500px !important;
      width: 646px !important;
    }
  }
  .item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 646px;
    height: 104px;
    background: url('./img/coal-icon-1.png') no-repeat center center / 100% 100%;
    padding: 20px 40px 20px 20px;
    > div {
      display: flex;
      align-items: baseline;
      &:nth-child(1) {
        align-items: center;
      }
    }
    .icon {
      height: 56px;
      width: 56px;
      background: url('./img/coal-icon-2.png') no-repeat center center / 100% 100%;
    }
    .title {
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 32px;
      color: #212121;
      line-height: 28px;
      text-align: left;
      font-style: normal;
      margin-left: 10px;
    }
    .num {
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 48px;
      color: #3b93fb;
      line-height: 48px;
      text-align: right;
      font-style: normal;
    }
    .unit {
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 24px;
      color: #666666;
      line-height: 28px;
      text-align: left;
      font-style: normal;
    }
  }
}
</style>
