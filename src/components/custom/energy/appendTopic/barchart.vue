<template>
  <div ref="chart" style="width: 100%; height: 100%" />
</template>

<script>
import * as echarts from 'echarts'
export default {
  name: '<PERSON><PERSON><PERSON>',
  props: {
    type: {
      type: Number,
      default: 0
    },
    //接收的数据
    chartData: {
      type: Array,
      required: true,
      default() {
        return [
          { product: 'Matcha Latte', 2015: 43.3, 2016: 85.8, 2017: 93.7 },
          { product: 'Milk Tea', 2015: 83.1, 2016: 73.4, 2017: 55.1 },
          { product: 'Cheese Cocoa', 2015: 86.4, 2016: 65.2, 2017: 82.5 },
          { product: 'Cheese Ghsll', 2015: 52.3, 2016: 45.2, 2017: 32.5 },
          { product: 'Ghsll Cocoa', 2015: 26.4, 2016: 69.2, 2017: 72.5 },
          { product: 'Walnut Brownie', 2015: 72.4, 2016: 53.9, 2017: 39.1 }
        ]
      }
    },
    showDataZoom: {
      type: Boolean,
      required: false,
      default: false
    }
  },
  data() {
    return {
      isPad: window.matchMedia('(min-width: 600px)').matches,
      myChart: null,
      allNum: 0
    }
  },
  watch: {
    chartData: {
      handler(newVal) {
        this.initChart(newVal)
      },
      immediate: true
    }
  },
  mounted() {
    window.addEventListener('resize', this.handleResize)
  },

  beforeUnmount() {
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    initChart(chartData) {
      //因为父组件使用的时候有可能该组件还没有dom.比如父组件在setup中请求了数据或者created中
      this.$nextTick(() => {
        const echarts = this.$echarts || echarts
        this.myChart = this.$shallowRef
          ? this.$shallowRef(echarts.init(this.$refs.chart, null, { renderer: 'canvas' }))
          : echarts.init(this.$refs.chart, null, { renderer: 'canvas' })
        this.getChart(chartData)
      })
    },
    getChart(chartData) {
      const option = {
        dataset: {
          source: chartData
        },
        legend: {
          show: true,
          itemGap: 14,
          itemWidth: 10,
          itemHeight: 10,
          textStyle: {
            color: '#73767f',
            width: 90,
            fontSize: 11,
            overflow: 'break'
          }
        },
        dataZoom: this.showDataZoom
          ? [
              {
                bottom: 0,
                type: 'slider',
                showDetail: false,
                show: true,
                xAxisIndex: [0],
                start: this.isPad ? 70 : 80,
                end: 100,
                height: 17, // 高度
                handleSize: '100%', // 手柄的大小
                handleIcon:
                  'path://M50 0 C22.4 0 0 22.4 0 50 C0 77.6 22.4 100 50 100 C77.6 100 100 77.6 100 50 C100 22.4 77.6 0 50 0 Z', // 圆形手柄
                handleStyle: {
                  color: '#447bcf', // 手柄颜色
                  borderColor: '#fff', // 手柄边框颜色
                  borderWidth: 1
                },
                fillerColor: 'rgba(85, 147, 253,0.6)', // 选中范围的填充颜色
                backgroundColor: 'rgba(47, 69, 84, 0.1)', // 背景色
                borderColor: '#ddd', // 边框颜色
                brushSelect: false,
                zoomLock: true
              }
            ]
          : [],
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          // 将 tooltip 框限制在图表的区域内
          confine: 'true',
          extraCssText: 'z-index: 9;',
          backgroundColor: 'rgba(0,0,0,0.6)',
          textStyle: {
            color: 'white'
          }
        },
        grid: {
          left: '0%',
          right: '5%',
          top: '28%',
          bottom: this.showDataZoom ? '10%' : '0%',
          containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            show: true,
            axisTick: { show: false, alignWithLabel: true },
            axisLabel: {
              fontSize: 11,
              interval: 0,
              width: 40,
              overflow: 'break',
              color: '#73767f'
            }
          }
        ],
        yAxis: [
          {
            name: '单位:亿千瓦时',
            nameTextStyle: { fontSize: 11, align: 'left', color: '#73767f', padding: [0, 0, 10, 0] },
            type: 'value',
            show: true,
            axisLine: {
              show: false,
              lineStyle: { width: 4 }
            },
            axisLabel: { fontSize: 11, margin: 10, color: '#73767f' },
            splitLine: {
              show: true,
              lineStyle: { width: 1, type: 'solid' }
            }
          },
          {
            name: '单位:%',
            nameTextStyle: { fontSize: 11, align: 'right', color: '#73767f', padding: [0, 0, 10, 0] },
            type: 'value',
            show: true,
            axisLine: {
              show: false,
              lineStyle: { width: 4 }
            },
            alignTicks: true
          }
        ],

        series: [
          {
            name: '全省发电量',
            type: 'bar',
            encode: {
              x: 'name',
              y: 'value1'
            },
            barWidth: 18,
            yAxisIndex: 0,
            z: 8,
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: 'rgba(82, 168, 242,.9)'
                },
                {
                  offset: 0.9,
                  color: 'rgba(215, 235, 252,.9)'
                },
                {
                  offset: 1,
                  color: 'rgba(215, 235, 252,.9)'
                }
              ])
            }
          },
          {
            type: 'line',
            name: '发电量同比',
            barWidth: 30,
            encode: {
              x: 'name',
              y: 'value2'
            },
            label: { show: false, position: 'top', color: 'black' },
            smooth: true,
            yAxisIndex: 1,
            symbolSize: 8,
            z: 30,
            itemStyle: {
              color: 'rgb(244, 151, 10)'
            },
            lineStyle: {
              color: 'rgb(244, 151, 10)'
            }
          },
          {
            name: '全社会用电量',
            type: 'bar',
            yAxisIndex: 0,
            barWidth: 18,
            z: 8,
            encode: {
              x: 'name',
              y: 'value3'
            },
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: 'rgba(255, 211, 104,.9)'
                },
                {
                  offset: 0.9,
                  color: 'rgba(255, 235, 188,.9)'
                },
                {
                  offset: 1,
                  color: 'rgba(255, 235, 188,.9)'
                }
              ])
            }
          },
          {
            type: 'line',
            name: '用电量同比',
            encode: {
              x: 'name',
              y: 'value4'
            },
            label: { show: false, position: 'top', color: 'black' },
            smooth: true,
            yAxisIndex: 1,
            symbolSize: 8,
            z: 30,
            itemStyle: {
              color: 'rgb(146, 228, 196)'
            },
            lineStyle: {
              color: 'rgb(146, 228, 196)'
            }
          }
        ]
      }

      switch (this.type) {
        case 1:
          option.yAxis[0].name = '单位: t*(10^4￥)^-1'
          option.series = [
            {
              name: '碳排放总量',
              type: 'bar',
              encode: {
                x: 'name',
                y: 'value1'
              },
              barWidth: 18,
              yAxisIndex: 0,
              z: 8,
              itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: 'rgba(82, 168, 242,.9)'
                  },
                  {
                    offset: 0.9,
                    color: 'rgba(215, 235, 252,.9)'
                  },
                  {
                    offset: 1,
                    color: 'rgba(215, 235, 252,.9)'
                  }
                ])
              }
            },
            {
              type: 'line',
              name: '同比增速',
              barWidth: 30,
              encode: {
                x: 'name',
                y: 'value2'
              },
              label: { show: false, position: 'top', color: 'black' },
              smooth: true,
              yAxisIndex: 1,
              symbolSize: 8,
              z: 30,
              itemStyle: {
                color: 'rgb(244, 151, 10)'
              },
              lineStyle: {
                color: 'rgb(244, 151, 10)'
              }
            }
          ]
          break
        case 2:
          option.yAxis[0].name = '单位:*10^4 t'
          option.series = [
            {
              name: '碳排放强度',
              type: 'bar',
              encode: {
                x: 'name',
                y: 'value1'
              },
              barWidth: 18,
              yAxisIndex: 0,
              z: 8,
              itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: 'rgba(82, 168, 242,.9)'
                  },
                  {
                    offset: 0.9,
                    color: 'rgba(215, 235, 252,.9)'
                  },
                  {
                    offset: 1,
                    color: 'rgba(215, 235, 252,.9)'
                  }
                ])
              }
            },
            {
              type: 'line',
              name: '同比增速',
              barWidth: 30,
              encode: {
                x: 'name',
                y: 'value2'
              },
              label: { show: false, position: 'top', color: 'black' },
              smooth: true,
              yAxisIndex: 1,
              symbolSize: 8,
              z: 30,
              itemStyle: {
                color: 'rgb(244, 151, 10)'
              },
              lineStyle: {
                color: 'rgb(244, 151, 10)'
              }
            }
          ]
          break
        case 3:
          option.yAxis[0].name = '单位:*10^4 t'
          option.series = [
            {
              name: '农资投入',
              type: 'bar',
              encode: {
                x: 'name',
                y: 'value1'
              },
              barWidth: 18,
              yAxisIndex: 0,
              z: 8,
              itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: 'rgba(82, 168, 242,.9)'
                  },
                  {
                    offset: 0.9,
                    color: 'rgba(215, 235, 252,.9)'
                  },
                  {
                    offset: 1,
                    color: 'rgba(215, 235, 252,.9)'
                  }
                ])
              }
            },
            {
              type: 'line',
              name: '农资投入占比',
              barWidth: 30,
              encode: {
                x: 'name',
                y: 'value2'
              },
              label: { show: false, position: 'top', color: 'black' },
              smooth: true,
              yAxisIndex: 1,
              symbolSize: 8,
              z: 30,
              itemStyle: {
                color: 'rgb(244, 151, 10)'
              },
              lineStyle: {
                color: 'rgb(244, 151, 10)'
              }
            },
            {
              name: '农田土壤利用',
              type: 'bar',
              yAxisIndex: 0,
              barWidth: 18,
              z: 8,
              encode: {
                x: 'name',
                y: 'value3'
              },
              itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: 'rgba(146, 228, 196,.9)'
                  },
                  {
                    offset: 0.9,
                    color: 'rgba(201, 241, 224,.9)'
                  },
                  {
                    offset: 1,
                    color: 'rgba(201, 241, 224,.9)'
                  }
                ])
              }
            },
            {
              type: 'line',
              name: '农田土壤利用占比',
              encode: {
                x: 'name',
                y: 'value4'
              },
              label: { show: false, position: 'top', color: 'black' },
              smooth: true,
              yAxisIndex: 1,
              symbolSize: 8,
              z: 30,
              itemStyle: {
                color: 'rgb(249, 171, 171)'
              },
              lineStyle: {
                color: 'rgb(249, 171, 171)'
              }
            },
            {
              name: '畜禽养殖',
              type: 'bar',
              yAxisIndex: 0,
              barWidth: 18,
              z: 8,
              encode: {
                x: 'name',
                y: 'value5'
              },
              itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: 'rgba(255, 211, 104,.9)'
                  },
                  {
                    offset: 0.9,
                    color: 'rgba(255, 235, 188,.9)'
                  },
                  {
                    offset: 1,
                    color: 'rgba(255, 235, 188,.9)'
                  }
                ])
              }
            },
            {
              type: 'line',
              name: '畜禽养殖占比',
              encode: {
                x: 'name',
                y: 'value6'
              },
              label: { show: false, position: 'top', color: 'black' },
              smooth: true,
              yAxisIndex: 1,
              symbolSize: 8,
              z: 30,
              itemStyle: {
                color: 'rgb(2, 144, 249)'
              },
              lineStyle: {
                color: 'rgb(2, 144, 249)'
              }
            }
          ]
          option.dataZoom[0].start = 90
          break
      }
      this.myChart.setOption(option, true)
    }
  }
}
</script>
