<template>
  <div class="apCpn">
    <div class="topicname">
      <img src="./img/icon.png" alt="" class="img1" />
      <img src="./img/topicname.png" alt="" class="img2" />
    </div>
    <div class="chart-container">
      <div class="title">
        <img src="./img/titleicon.png" alt="" />
        电力供需平衡分析
      </div>
      <barchart :chart-data="chartData" :show-data-zoom="true" class="chart"></barchart>
    </div>
  </div>
</template>
<script>
import barchart from './barchart.vue'
import { getAction } from '@/api/manage'
export default {
  components: { barchart },
  data() {
    return {
      chartData: []
    }
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      getAction(
        import.meta.env.VITE_APP_SITUATION_BASE_URL + '/topic/data/listAllBySql/ydd_ny_qsfdlgk_qsfdlydl_ydqs',
        {}
      ).then((res) => {
        this.chartData = res.result.map((item) => {
          return {
            value1: item.qsfdl,
            value2: item.qsfdl_rate,
            name: item.index_time,
            value3: item.qshydl,
            value4: item.qshydl_rate
          }
        })
        console.log(this.chartData)
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.apCpn {
  margin-bottom: 25px;
  .topicname {
    display: flex;
    align-items: center;
    width: 100%;
    height: 116px;
    background: url('./img/bg.png') center/100% 100% no-repeat;
    .img1 {
      margin: 0 12px 0 20px;
      width: 56px;
      height: 56px;
    }
    .img2 {
      width: 320px;
      height: 38px;
    }
  }

  .chart-container {
    background: white;
    border-radius: 0 0 32px 32px;
    padding: 0 30px 30px;
    .title {
      display: flex;
      align-items: center;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 34px;
      color: #212121;
      line-height: 50px;
      text-align: left;
      font-style: normal;
      margin-bottom: 24px;

      img {
        margin-right: 12px;
        width: 12px;
        height: 31px;
      }
    }
    .chart {
      height: 630px !important;
    }
  }
}
</style>
