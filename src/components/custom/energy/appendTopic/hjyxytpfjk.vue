<template>
  <div class="apCpn">
    <div class="topicname">
      <img src="./img/icon2.png" alt="" class="img1" />
      <img src="./img/topicname2.png" alt="" class="img2" />
    </div>
    <div class="chart-container">
      <div class="title">
        <img src="./img/titleicon.png" alt="" />
        碳排放量及平均碳排放强度(农业)
      </div>
      <div class="btnlist">
        <div :class="{ apbtn: true, active: active == 1 }" @click="handleClick(1)">碳排放总量</div>
        <div :class="{ apbtn: true, active: active == 2 }" @click="handleClick(2)">碳排放强度</div>
        <div :class="{ apbtn: true, active: active == 3 }" @click="handleClick(3)">碳污染排放量</div>
      </div>
      <barchart :chart-data="chartData" :show-data-zoom="true" class="chart" :type="active"></barchart>
    </div>
  </div>
</template>
<script>
import barchart from './barchart.vue'
import { getAction } from '@/api/manage'
export default {
  components: { barchart },
  data() {
    return {
      chartData: [],
      active: 1
    }
  },
  created() {
    this.handleClick(1)
  },
  methods: {
    handleClick(index) {
      this.active = index
      switch (index) {
        case 1:
          this.chartData = [
            { name: 2000, value1: 1558.9, value2: '' },
            { name: 2001, value1: 1594.7, value2: 2.3 },
            { name: 2002, value1: 1676.3, value2: 5.1 },
            { name: 2003, value1: 1705.4, value2: 1.7 },
            { name: 2004, value1: 1745.7, value2: 2.4 },
            { name: 2005, value1: 1814.3, value2: 3.9 },
            { name: 2006, value1: 1871.4, value2: 3.1 },
            { name: 2007, value1: 1852.3, value2: -1.0 },
            { name: 2008, value1: 1832.1, value2: -1.1 },
            { name: 2009, value1: 1824.9, value2: 0.4 },
            { name: 2010, value1: 1828.0, value2: 0.2 },
            { name: 2011, value1: 1804.4, value2: -1.3 },
            { name: 2012, value1: 1807.7, value2: 0.2 },
            { name: 2013, value1: 1807.4, value2: 0.0 },
            { name: 2014, value1: 1800.2, value2: -0.4 },
            { name: 2015, value1: 1791.9, value2: -0.5 },
            { name: 2016, value1: 1781.5, value2: -0.6 },
            { name: 2017, value1: 1769.8, value2: -0.7 },
            { name: 2018, value1: 1739.5, value2: -1.7 },
            { name: 2019, value1: 1623.0, value2: -6.7 },
            { name: 2020, value1: 1583.4, value2: -2.4 }
          ]
          break
        case 2:
          this.chartData = [
            { name: 2000, value1: 0.821, value2: '' },
            { name: 2001, value1: 0.776, value2: -5.5 },
            { name: 2002, value1: 0.791, value2: 2.0 },
            { name: 2003, value1: 0.702, value2: -11.3 },
            { name: 2004, value1: 0.599, value2: -14.6 },
            { name: 2005, value1: 0.574, value2: -4.1 },
            { name: 2006, value1: 0.566, value2: -1.5 },
            { name: 2007, value1: 0.474, value2: -16.2 },
            { name: 2008, value1: 0.4, value2: -15.6 },
            { name: 2009, value1: 0.375, value2: -6.3 },
            { name: 2010, value1: 0.339, value2: -9.4 },
            { name: 2011, value1: 0.304, value2: -10.6 },
            { name: 2012, value1: 0.294, value2: -3.3 },
            { name: 2013, value1: 0.268, value2: -8.7 },
            { name: 2014, value1: 0.256, value2: -4.5 },
            { name: 2015, value1: 0.247, value2: -3.6 },
            { name: 2016, value1: 0.254, value2: 3.1 },
            { name: 2017, value1: 0.256, value2: 0.8 },
            { name: 2018, value1: 0.245, value2: -4.6 },
            { name: 2019, value1: 0.222, value2: -9.4 },
            { name: 2020, value1: 0.205, value2: -7.7 }
          ]
          break
        case 3:
          this.chartData = [
            { name: 2000, value1: 786.5, value2: 50.5, value3: 220.8, value4: 14.2, value5: 551.6, value6: 35.4 },
            { name: 2001, value1: 813.5, value2: 51.0, value3: 204.5, value4: 12.8, value5: 576.8, value6: 36.2 },
            { name: 2002, value1: 848.8, value2: 50.6, value3: 213.9, value4: 12.8, value5: 613.6, value6: 36.6 },
            { name: 2003, value1: 859.0, value2: 50.4, value3: 203.5, value4: 11.9, value5: 642.8, value6: 37.7 },
            { name: 2004, value1: 875.7, value2: 50.2, value3: 193.4, value4: 11.1, value5: 676.7, value6: 38.8 },
            { name: 2005, value1: 906.1, value2: 49.9, value3: 196.2, value4: 10.8, value5: 712.0, value6: 39.2 },
            { name: 2006, value1: 943.6, value2: 50.4, value3: 195.5, value4: 10.4, value5: 732.3, value6: 39.1 },
            { name: 2007, value1: 950.4, value2: 51.3, value3: 199.2, value4: 10.8, value5: 702.7, value6: 37.9 },
            { name: 2008, value1: 916.5, value2: 50.0, value3: 200.4, value4: 10.9, value5: 715.1, value6: 39.0 },
            { name: 2009, value1: 906.2, value2: 49.7, value3: 202.8, value4: 11.1, value5: 715.9, value6: 39.2 },
            { name: 2010, value1: 917.0, value2: 50.2, value3: 203.3, value4: 11.1, value5: 707.7, value6: 38.7 },
            { name: 2011, value1: 913.1, value2: 50.6, value3: 204.8, value4: 11.3, value5: 686.5, value6: 38.0 },
            { name: 2012, value1: 910.5, value2: 50.4, value3: 205.9, value4: 11.4, value5: 691.3, value6: 38.2 },
            { name: 2013, value1: 903.9, value2: 50.0, value3: 208.3, value4: 11.5, value5: 695.2, value6: 38.5 },
            { name: 2014, value1: 889.6, value2: 49.4, value3: 211.4, value4: 11.7, value5: 699.2, value6: 38.8 },
            { name: 2015, value1: 880.7, value2: 49.2, value3: 213.0, value4: 11.9, value5: 698.2, value6: 39.0 },
            { name: 2016, value1: 870.3, value2: 48.9, value3: 211.8, value4: 11.9, value5: 699.4, value6: 39.3 },
            { name: 2017, value1: 844.0, value2: 47.7, value3: 217.6, value4: 12.3, value5: 708.2, value6: 40.0 },
            { name: 2018, value1: 810.9, value2: 46.6, value3: 217.4, value4: 12.5, value5: 711.2, value6: 40.9 },
            { name: 2019, value1: 773.8, value2: 47.7, value3: 214.5, value4: 13.2, value5: 634.7, value6: 39.1 },
            { name: 2020, value1: 751.1, value2: 47.4, value3: 214.2, value4: 13.5, value5: 618.1, value6: 39.0 }
          ]

          break
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.apCpn {
  margin-bottom: 25px;
  .topicname {
    display: flex;
    align-items: center;
    width: 100%;
    height: 116px;
    background: url('./img/bg2.png') center/100% 100% no-repeat;
    .img1 {
      margin: 0 12px 0 20px;
      width: 56px;
      height: 56px;
    }
    .img2 {
      width: 402px;
      height: 38px;
    }
  }

  .chart-container {
    background: white;
    border-radius: 0 0 32px 32px;
    padding: 0 30px 30px;
    .title {
      display: flex;
      align-items: center;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 34px;
      color: #212121;
      line-height: 50px;
      text-align: left;
      font-style: normal;
      margin-bottom: 24px;

      img {
        margin-right: 12px;
        width: 12px;
        height: 31px;
      }
    }
    .btnlist {
      width: 100%;
      display: flex;
      align-items: center;
      margin-bottom: 25px;
      .apbtn {
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        border-radius: 29px;
        padding: 5px 15px;
        font-size: 30px;
        color: #212121;
        line-height: 45px;
        text-align: center;
        margin-right: 20px;
        font-style: normal;
        background: #f7f8fa;
      }
      .active {
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 30px;
        color: #28a0fc;
        background: #e9f3ff;
        line-height: 45px;
        text-align: center;
        font-style: normal;
      }
    }
    .chart {
      height: 630px !important;
    }
  }
}
</style>
