<template>
  <div class="box">
    <div class="toptitle">主要措施</div>

    <div class="contBox">
      <div class="box_title">
        <img src="../img/titlebg4.png" alt="" />
        <div>坚持⾼位部署推动</div>
        <img src="../img/titlebg4.png" alt="" />
      </div>
      <div class="padNeed">
        <div class="top">
          <div class="num">01</div>
          提请省政府召开全省稳定和扩大就业工作电视会议，部署安排稳定和扩大就业工作，省长<span>周乃翔</span>同志出席会议并讲话；
        </div>

        <div class="top">
          <div class="num">02</div>
          提请省政府召开全省高校毕业生等青年就业创业工作视频会议，全力做好我省高校毕业生等青年就业创业工作，副省长<span>邓云锋</span>同志出席会议并讲话。
        </div>
        <div class="imglist">
          <div v-for="(item, i) in imglist" :key="i" class="imgitem">
            <img :src="item.img" alt="" />
            <div>{{ item.name }}</div>
          </div>
        </div>
      </div>

      <div class="line"></div>
      <div class="padNeed">
        <div class="top">
          <div class="num">01</div>
          制定下发<span>《2024年全省就业和农民工工作要点》《2024年全省高校毕业生就业工作方案》</span>，对全年工作作出系统部署安排。
        </div>
        <div class="imglist">
          <div v-for="(item, i) in imglist2" :key="i" class="imgitem">
            <img :src="item.img" alt="" />
            <div>{{ item.name }}</div>
          </div>
        </div>
        <div class="line"></div>
        <div class="top">
          <div class="num">01</div>
          围绕培育和发展新质生产力，制定出台支持民营经济、支撑先进制造业、助力银发经济的“三个文件”，提升经济增长就业拉动力。
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import img1 from '../img/img1.png'
import img2 from '../img/img2.png'
import img3 from '../img/img3.png'
import img4 from '../img/img4.png'
export default {
  data() {
    return {
      isPad: window.matchMedia('(min-width: 600px)').matches,
      imglist: [
        {
          img: img1,
          name: '全省稳定和扩大就业工作电视会议'
        },
        {
          img: img2,
          name: '全省高校毕业生等青年就业创业工作视频会议'
        }
      ],
      imglist2: [
        {
          img: img3,
          name: '2024年全省就业和农民工工作要点'
        },
        {
          img: img4,
          name: '2024年全省高校毕业生就业工作方案'
        }
      ]
    }
  }
}
</script>
<style lang="scss" scoped>
.box {
  background: linear-gradient(to bottom, #e6f8ff 0%, #ffffff 20%, #ffffff 100%) center / 100% no-repeat;
  border-radius: 15px;
  position: relative;
  margin-top: 60px;
  .toptitle {
    width: 282px;
    height: 68px;
    position: absolute;
    top: -30px;
    left: 50%;
    transform: translateX(-50%);
    background: url('../img/titlebg1.png') center/100% 100% no-repeat;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 32px;
    color: #ffffff;
    line-height: 60px;
    text-align: center;
    font-style: normal;
  }
  .box_title {
    margin: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    font-size: 32px;
    color: #000000;
    line-height: 45px;
    text-align: center;
    font-style: normal;
    background: linear-gradient(0deg, #0284ff 0%, #1282ff 39%, #00bbff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    div {
      margin: 0 10px;
    }
    img {
      width: 59px;
      height: 17px;
    }
  }
  .contBox {
    margin-top: 24px;
    padding: 36px;
    position: relative;
    .line {
      width: 100%;
      height: 5px;
      margin: 32px 0 20px;
      background: url('../img/line.png') center center/604px 100% no-repeat;
    }
    .top {
      margin: 30px auto 0;
      width: 645px;
      background: #e8f3ff;
      border: 5px solid white;
      box-shadow: 0px 5px 15px rgba(222, 222, 222, 1);
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      border-radius: 16px;
      font-size: 28px;
      color: #212121;
      line-height: 48px;
      text-align: left;
      font-style: normal;
      padding: 30px 30px 30px 70px;
      text-align: left;
      position: relative;
      span {
        color: #3096ef;
      }
      .num {
        background: url('../img/numbg2.png') center/ 100% 100% no-repeat;
        width: 101px;
        height: 107px;
        position: absolute;
        left: -30px;
        top: -30px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: PingFangSC, PingFang SC;
        font-weight: 600;
        font-size: 48px;
        color: #feffff;
        line-height: 67px;
        text-align: center;
        font-style: normal;
      }
    }
    .imglist {
      width: 100%;
      display: grid;
      column-gap: 12px;
      row-gap: 12px;
      margin-top: 35px;
      grid-template-columns: repeat(2, 1fr);
      .imgitem {
        display: flex;
        flex-direction: column;
        align-items: center;
        img {
          width: 294px;
          height: 196px;
        }
        div {
          margin-top: -50px;
          width: 100%;
          height: 83px;
          display: flex;
          align-items: center;
          justify-content: center;
          text-align: center;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 24px;
          color: #ffffff;
          line-height: 28px;
          font-style: normal;
          background: url('../img/textbg.png') center / 100% 100% no-repeat;
        }
      }
    }
  }
}
</style>
