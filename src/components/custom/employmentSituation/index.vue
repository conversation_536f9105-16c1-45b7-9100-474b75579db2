<template>
  <div class="developmentBox">
    <img src="./img/topimg.png" alt="" class="topimg" />

    <div class="topTitle topTitle1">⼯作开展情况</div>
    <div class="contbox">
      <!-- 运⾏情况及趋势 -->
      <yxqkjqs />
      <!-- 主要措施 -->
      <zycs />
      <!-- 抓紧就业政策落实 -->
      <zjjyzcls />
      <!-- 促进重点群体就业 -->
      <cjzdqtjy />
      <!-- 释放创业创新活⼒ -->
      <sfcxhl />
      <!-- 做好专项招聘活动 -->
      <zhzxzphd />
      <!-- 提升⼈⼒资源服务效能 -->
      <tsrlzyfwxn />
      <!-- 开展数智就业服务 -->
      <kzszjyfw />
    </div>
    <div class="topTitle topTitle1">下步工作打算</div>
    <div class="contbox">
      <!-- 加强顶层设计 -->
      <jqdcsj />
      <!-- 突出重点群体 -->
      <tczdqt />
      <!-- 深化就业改⾰ -->
      <shjygg />
      <!-- 应对结构⽭盾 -->
      <ydjgmd />
      <!-- 提升⼈⼒资源服务效能 -->
      <fwxn />
    </div>
  </div>
</template>

<script>
import { getAction } from '@/api/manage'
import yxqkjqs from './yxqkjqs/yxqkjqs.vue'
import zycs from './zycs/zycs.vue'
import tczdqt from './tczdqt/tczdqt.vue'
import jqdcsj from './jqdcsj/jqdcsj.vue'
import kzszjyfw from './kzszjyfw/kzszjyfw.vue'
import tsrlzyfwxn from './tsrlzyfwxn/tsrlzyfwxn.vue'
import sfcxhl from './sfcxhl/sfcxhl.vue'
import fwxn from './fwxn/fwxn.vue'
import ydjgmd from './ydjgmd/ydjgmd.vue'
import zhzxzphd from './zhzxzphd/zhzxzphd.vue'
import cjzdqtjy from './cjzdqtjy/cjzdqtjy.vue'
import shjygg from './shjygg/shjygg.vue'
import zjjyzcls from './zjjyzcls/zjjyzcls.vue'
import { topicMixin } from '@/mixins/topicMixin'

export default {
  components: {
    yxqkjqs,
    zjjyzcls,
    cjzdqtjy,
    tsrlzyfwxn,
    fwxn,
    shjygg,
    ydjgmd,
    zhzxzphd,
    sfcxhl,
    kzszjyfw,
    jqdcsj,
    tczdqt,
    zycs
  },
  mixins: [topicMixin],

  data() {
    return {
      count_type: null,
      dataArr: {}
    }
  },
  mounted() {
    // this.loadData()
  },

  methods: {
    loadData() {
      getAction(import.meta.env.VITE_APP_SITUATION_BASE_URL + 'topic/data/listAllBySql/ydd_mzgz_jbqk_mkmc').then(
        (res) => {}
      )
    }
  }
}
</script>
<style lang="scss" scoped>
.developmentBox {
  background-color: #ddf4fe;
  padding-bottom: 50px;
  .topimg {
    width: 100%;
    height: 355px;
    margin-bottom: -80px;
  }

  .topTitle {
    width: 100%;
    height: 309px;
    background: url('./img/titlebg.png') center / 100% 100% no-repeat;
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    font-size: 36px;
    padding: 25px 0 0 35px;
    color: #ffffff;
    line-height: 50px;
    text-align: left;
    margin-bottom: -200px;
    font-style: normal;
    position: relative;
  }
  .topTitle1 {
    margin-top: 20px;
  }
  .contbox {
    padding: 0 24px;
  }
}
</style>
