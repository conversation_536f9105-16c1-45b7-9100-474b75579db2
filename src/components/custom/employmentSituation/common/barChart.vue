<template>
  <div ref="chart" style="width: 100%; height: 100%" />
</template>

<script>
import * as echarts from 'echarts'
export default {
  name: '<PERSON><PERSON><PERSON>',
  props: {
    //接收的修改图表的配置
    chartConfig: {
      type: Object,
      required: true,
      default() {
        return {
          color: ['#FFD368', '#BD83FF', '#48E0E8', '#FFA26F', '#3CE2B0', '#66A0FF'],
          series1_type: 'line',
          series1_encode: {
            x: 'product',
            y: '2017'
          },
          series1_name: 'serires2'
        }
      }
    },
    //接收的数据
    chartData: {
      type: Array,
      required: true,
      default() {
        return [
          { product: 'Matcha Latte', 2015: 43.3, 2016: 85.8, 2017: 93.7 },
          { product: 'Milk Tea', 2015: 83.1, 2016: 73.4, 2017: 55.1 },
          { product: 'Cheese Cocoa', 2015: 86.4, 2016: 65.2, 2017: 82.5 },
          { product: 'Cheese Ghsll', 2015: 52.3, 2016: 45.2, 2017: 32.5 },
          { product: 'Ghsll Cocoa', 2015: 26.4, 2016: 69.2, 2017: 72.5 },
          { product: 'Walnut Brownie', 2015: 72.4, 2016: 53.9, 2017: 39.1 }
        ]
      }
    },
    showDataZoom: {
      type: Boolean,
      required: false,
      default: false
    },
    //有几个柱子
    type: {
      type: String,
      required: false,
      default: '1'
    }
  },
  data() {
    return {
      isPad: window.matchMedia('(min-width: 600px)').matches,
      myChart: null,
      allNum: 0,
      colorArr: ['#0090ff', '#BD83FF', '#48E0E8', '#FFA26F', '#3CE2B0', '#66A0FF']
    }
  },
  watch: {
    chartData: {
      handler(newVal) {
        this.initChart(newVal)
      },
      immediate: true
    }
  },
  mounted() {
    window.addEventListener('resize', this.handleResize)
  },

  beforeUnmount() {
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    initChart(chartData) {
      //因为父组件使用的时候有可能该组件还没有dom.比如父组件在setup中请求了数据或者created中
      this.$nextTick(() => {
        const echarts = this.$echarts || echarts
        this.myChart = this.$shallowRef
          ? this.$shallowRef(echarts.init(this.$refs.chart, null, { renderer: 'canvas' }))
          : echarts.init(this.$refs.chart, null, { renderer: 'canvas' })
        this.getChart(chartData)
      })
    },
    getChart(chartData) {
      //数据获取到了,初始化图表
      const xData = []
      const yData1 = []
      const yData2 = []
      let series0Name = ''
      let series1Name = ''

      switch (this.type) {
        case '1':
          series0Name = '新增就业人数'
          series1Name = '同比增长率'
          chartData.forEach((element) => {
            xData.push(element.year)
            yData1.push(element.ratio)
            yData2.push(element.growth_yoy)
          })
          break
        case '2':
          series0Name = '高校毕业生落实数'
          series1Name = '同比增长率'
          chartData.forEach((element) => {
            xData.push(element.year)
            yData1.push(element.ratio)
            yData2.push(element.growth_yoy)
          })
          break
        case '3':
          series0Name = '失业人员再就业'
          series1Name = '同比增长率'
          chartData.forEach((element) => {
            xData.push(element.year)
            yData1.push(element.ratio)
            yData2.push(element.growth_yoy)
          })
          break
        case '4':
          chartData.forEach((element) => {
            xData.push(element.year)
            yData1.push(element.ratio)
          })
          break
        case '5':
          // series0Name = '外出务工农村劳动力'
          // series1Name = '同比增长率'
          chartData.forEach((element) => {
            xData.push(element.year)
            yData1.push(element.ratio)
            yData2.push(element.growth_yoy)
          })
          break

        case '6':
          series0Name = '失业人员再就业'
          series1Name = '同比增长率'
          chartData.forEach((element) => {
            xData.push(element.year)
            yData1.push(element.ratio)
            yData2.push(element.growth_yoy)
          })
          break
        case '7':
          series0Name = '规上人力资源服务机构营收'
          series1Name = '占全省规上服务业比例'
          chartData.forEach((element) => {
            xData.push(element.year)
            yData1.push(element.ratio)
            yData2.push(element.growth_yoy)
          })
          break
      }

      const option = {
        color: this.colorArr,
        // dataset: {
        //   source: chartData
        // },
        legend: {
          show: true,
          itemGap: 14,
          itemWidth: 10,
          itemHeight: 10,
          textStyle: {
            color: '#73767f',
            width: 90,
            fontSize: 11,
            overflow: 'break'
          }
        },
        dataZoom: this.showDataZoom
          ? [
              {
                bottom: 0,
                type: 'slider',
                showDetail: false,
                show: true,
                xAxisIndex: [0],
                start: this.isPad ? 70 : 50,
                end: 100,
                height: 17, // 高度
                handleSize: '100%', // 手柄的大小
                handleIcon:
                  'path://M50 0 C22.4 0 0 22.4 0 50 C0 77.6 22.4 100 50 100 C77.6 100 100 77.6 100 50 C100 22.4 77.6 0 50 0 Z', // 圆形手柄
                handleStyle: {
                  color: '#447bcf', // 手柄颜色
                  borderColor: '#fff', // 手柄边框颜色
                  borderWidth: 1
                },
                fillerColor: 'rgba(85, 147, 253,0.6)', // 选中范围的填充颜色
                backgroundColor: 'rgba(47, 69, 84, 0.1)', // 背景色
                borderColor: '#ddd', // 边框颜色
                brushSelect: false,
                zoomLock: true
              }
            ]
          : [],
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          // 将 tooltip 框限制在图表的区域内
          confine: 'true',
          extraCssText: 'z-index: 9;',
          backgroundColor: 'rgba(0,0,0,0.6)',
          textStyle: {
            color: 'white'
          }
        },
        grid: {
          left: '0%',
          right: '0%',
          top: '25%',
          bottom: this.showDataZoom ? '10%' : '0%',
          containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            show: true,
            axisTick: { show: false, alignWithLabel: true },
            axisLabel: {
              fontSize: 11,
              interval: 0,
              width: 40,
              overflow: 'break',
              color: '#73767f'
            },
            data: xData
          }
        ],
        yAxis: [
          {
            name: '',
            nameTextStyle: { fontSize: 11, align: 'left', color: '#73767f', padding: [0, 0, 10, 0] },
            type: 'value',
            show: true,
            axisLine: {
              show: false,
              lineStyle: { width: 4 }
            },
            axisLabel: { fontSize: 11, margin: 10, color: '#73767f' },
            splitLine: {
              show: true,
              lineStyle: { width: 1, type: 'solid' }
            }
          },
          {
            name: '',
            nameTextStyle: { fontSize: 11, align: 'right', color: '#73767f', padding: [0, 0, 10, 0] },
            type: 'value',
            show: true,
            axisLine: {
              show: false,
              lineStyle: { width: 4 }
            },
            alignTicks: true
          }
        ],

        series: [
          {
            name: series0Name,
            data: yData1,
            type: 'pictorialBar',
            symbolSize: [18, 6],
            symbolOffset: [0, 4],
            z: 12,
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: 'rgb(42, 123, 255)'
                  },
                  {
                    offset: 1,
                    color: 'rgb(42,123,255)'
                  }
                ])
              }
            }
          },
          {
            name: series0Name,
            data: yData1,
            type: 'bar',
            barWidth: 18,
            z: 8,
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: 'rgba(116, 174, 255,.9)'
                },
                {
                  offset: 0.9,
                  color: 'rgba(39, 114, 251,.9)'
                },
                {
                  offset: 1,
                  color: 'rgba(39, 114, 251,.9)'
                }
              ])
            }
          },
          {
            name: series0Name,
            data: yData1,
            type: 'pictorialBar',
            symbolSize: [18, 6],
            symbolOffset: [0, -4],
            yAxisIndex: 0,
            z: 12,
            symbolPosition: 'end',
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [
                    {
                      offset: 0,
                      color: '#4882db'
                    },
                    {
                      offset: 1,
                      color: '#4882db'
                    }
                  ],
                  false
                )
              }
            }
          },
          {
            type: 'line',
            name: series1Name,
            barWidth: 30,
            label: { show: false, position: 'top', color: 'black' },
            data: yData2,
            smooth: true,
            yAxisIndex: 1,
            symbolSize: 8,
            z: 30,
            itemStyle: {
              color: 'rgb(118, 223, 181)',
              shadowColor: 'rgba(0, 0, 0, 0.4)',
              shadowBlur: 8,
              shadowOffsetX: 7,
              shadowOffsetY: 7
            },
            lineStyle: {
              color: 'rgb(118, 223, 181)',
              shadowColor: 'rgba(0, 0, 0, 0.5)',
              shadowBlur: 8,
              shadowOffsetX: 7,
              shadowOffsetY: 7
            }
          }
        ]
      }

      //上面options是该组件基本配置,parseOptions函数是使用传入的参数如 :series0_label_show:true  修改上面的options series[0].label.show
      this.parseOptions(option, this.chartConfig)
      this.myChart.setOption(option)
    },

    //重新构建options,用于将options中 a.b.c的值 修改为 传入的a_b_c的值
    parseOptions(options, overrides) {
      //因为传入的参数  a0_b_c 实际映射 a[0].b.c,所以要取出末尾的数字
      //如果有数字 返回[series,0]  如果没有数字  返回[series,null]
      function extractTrailingDigits(str) {
        const REG = /\d+$/
        const matches = str.match(REG)
        const startValue = matches ? str.replace(REG, '') : str
        const endValue = matches ? matches[0] : null
        return [startValue, endValue]
      }
      function applyOverrides(obj, overrides) {
        //overrides: [width:'100rem',series_name:'aaa']
        for (const key in overrides) {
          if (Object.prototype.hasOwnProperty.call(overrides, key)) {
            //key:series_name || series0_name
            //keys:[series,name]
            const keys = key.split('_')
            let currentObj = obj
            for (let i = 0; i < keys.length - 1; i++) {
              const [startValue, endValue] = extractTrailingDigits(keys[i])
              let optionsValue
              //如果末尾有数字,那么这个key就是去掉末尾的数字,如series0  则keys[i]=series
              if (endValue) {
                keys[i] = startValue
                optionsValue = currentObj[keys[i]][endValue]
                //如果series里面只有一个值,那么series1_name  添加series[1]这个{}
                if (!optionsValue) {
                  if (!currentObj[keys[i]]) {
                    throw new Error(`没有${keys[i]}这个属性,请确保传入的属性和值正确`)
                  }
                  currentObj[keys[i]][endValue] = {}
                  optionsValue = currentObj[keys[i]][endValue]
                }
              } else {
                optionsValue = currentObj[keys[i]]
                //series_aaa_name  不做错误处理,直接添加aaa的对象
                if (!optionsValue) {
                  if (Array.isArray(currentObj)) {
                    throw new Error(`${keys[keys.length - 2]}是一个数组,请传入属性索引,如:series0_name`)
                  } else {
                    currentObj[keys[i]] = {}
                    optionsValue = currentObj[keys[i]]
                  }
                }
              }
              currentObj = optionsValue
            }
            const lastKey = keys[keys.length - 1]

            //上面循环完成,在对象当中添加最后一个值
            //如series_aaa,显然aaa没有这个属性
            if (currentObj[lastKey] === undefined) {
              if (Array.isArray(currentObj)) {
                throw new Error(`${keys[keys.length - 2]}是一个数组,请传入属性索引,如:series0_name`)
              } else {
                console.warn(`${keys[keys.length - 2]}中没有找到${keys[keys.length - 1]}属性,请确保传入的属性和值正确`)
              }
            }
            //会直接添加这个属性
            currentObj[lastKey] = overrides[key]
          }
        }
      }
      applyOverrides(options, overrides)
    },
    handleResize() {
      if (this.myChart) {
        this.myChart.resize()
      }
    }
  }
}
</script>
