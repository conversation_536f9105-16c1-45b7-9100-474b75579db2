<template>
  <div class="box">
    <div class="toptitle">加强顶层设计</div>

    <div class="contBox">
      <div class="top">
        <div class="num">01</div>
        制定出台<span>《促进高质量充分就业的实施意见》</span>，健全就业优先政策体系，推进部省共建具有山东优势特征的高质量充分就业省份。
      </div>

      <div class="top">
        <div class="num">02</div>
        提请省政府召开全省稳就业工作会议，印发<span>省就业促进处和劳动保护工作领导小组2025年工作要点</span>，对全省就业工作做出部署安排。
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      isPad: window.matchMedia('(min-width: 600px)').matches
    }
  }
}
</script>
<style lang="scss" scoped>
.box {
  background: linear-gradient(to bottom, #e6f8ff 0%, #ffffff 20%, #ffffff 100%) center / 100% no-repeat;
  border-radius: 15px;
  position: relative;
  margin-top: 60px;
  .toptitle {
    width: 282px;
    height: 68px;
    position: absolute;
    top: -30px;
    left: 50%;
    transform: translateX(-50%);
    background: url('../img/titlebg1.png') center/100% 100% no-repeat;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 32px;
    color: #ffffff;
    line-height: 60px;
    text-align: center;
    font-style: normal;
  }

  .contBox {
    margin-top: 24px;
    padding: 36px;
    position: relative;
    .line {
      width: 100%;
      height: 5px;
      margin: 32px 0 20px;
      background: url('../img/line.png') center center/604px 100% no-repeat;
    }
    .top {
      margin: 30px auto 0;
      width: 645px;
      background: #e8f3ff;
      border: 5px solid white;
      box-shadow: 0px 5px 15px rgba(222, 222, 222, 1);
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      border-radius: 16px;
      font-size: 28px;
      color: #212121;
      line-height: 48px;
      text-align: left;
      font-style: normal;
      padding: 30px 30px 30px 70px;
      text-align: left;
      position: relative;
      span {
        color: #3096ef;
      }
      .num {
        background: url('../img/numbg2.png') center/ 100% 100% no-repeat;
        width: 101px;
        height: 107px;
        position: absolute;
        left: -30px;
        top: -30px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: PingFangSC, PingFang SC;
        font-weight: 600;
        font-size: 48px;
        color: #feffff;
        line-height: 67px;
        text-align: center;
        font-style: normal;
      }
    }
  }
}
</style>
