<template>
  <div class="box">
    <div class="box_title">
      <img src="../img/titlebg4.png" alt="" />
      <div>提升⼈⼒资源服务效能</div>
      <img src="../img/titlebg4.png" alt="" />
    </div>
    <div class="contBox">
      <div class="toptext">
        引导人力资源服务企业参与重点群体促就业等活动，持续推动行业高质量发展举办第一届山东省人力资源服务业高质量发展大会暨高水平开放协作交流大会，达成合作意向<span>226</span>项
      </div>
      <div class="padNeed">
        <div class="baseCard">
          <div class="title">省级人力资源服务产业园实现营收</div>
          <div class="valbox">
            <div class="ltval">
              <div class="num">196</div>
              <div class="unit">亿元</div>
            </div>
            <div class="ctval">
              <div class="desc">同比</div>
              <img src="@/assets/img/arrow-up.png" alt="" />
              <div class="value">15<span>%</span></div>
            </div>
          </div>
        </div>
        <div class="btmCard">
          <div class="ltcont">
            <img src="../img/btmCard_icon1.png" alt="" />
            <div class="centercont">
              <div class="name">服务各类⽤⼈单位</div>
            </div>
          </div>
          <div class="val">
            <div class="value">22</div>
            <div class="unit">万家次</div>
          </div>
        </div>
        <div class="btmCard">
          <div class="ltcont">
            <img src="../img/btmCard_icon2.png" alt="" />
            <div class="centercont">
              <div class="name">服务就业⼈数</div>
            </div>
          </div>
          <div class="val">
            <div class="value">261</div>
            <div class="unit">万元</div>
          </div>
        </div>
        <div class="btmCard">
          <div class="ltcont">
            <img src="../img/btmCard_icon3.png" alt="" />
            <div class="centercont">
              <div class="name">招引人才</div>
            </div>
          </div>
          <div class="val">
            <div class="value">5.3</div>
            <div class="unit">万人</div>
          </div>
        </div>
      </div>
      <div class="padNeed">
        <div class="line"></div>
        <div class="btmcont">
          <div class="mt_btn">
            <div
              v-for="(item, i) in btnList"
              :key="i"
              :class="{ btnItem: true, active: active === item }"
              @click="handleClick(item)"
            >
              {{ item }}
            </div>
          </div>
          <div class="btmBar">
            <barChart :chart-data="chartData" :chart-config="chartConfig" type="7" :show-data-zoom="false" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import barChart from '../common/barChart.vue'
import { getAction } from '@/api/manage'
export default {
  components: {
    barChart
  },
  data() {
    return {
      isPad: window.matchMedia('(min-width: 600px)').matches,
      active: '规上⼈⼒资源服务机',
      btnList: ['规上⼈⼒资源服务机', '规上⼈⼒资源服务机...'],
      chartData: [
        { year: '2024年1月', ratio: 26.18, growth_yoy: -0.6 },
        { year: '2024年1-2月', ratio: 32.41, growth_yoy: -1.82 },
        { year: '2024年1-3月', ratio: 42.03, growth_yoy: -1.27 },
        { year: '2024年1-4月', ratio: 37.36, growth_yoy: -3.94 },
        { year: '2024年1-5月', ratio: 35.36, growth_yoy: -4.05 }
      ],
      chartConfig: {
        yAxis0_name: '单位：万元',
        yAxis1_name: '单位：%',
        tooltip_formatter: (e) => {
          return `${e[0].axisValue}:</br>${this.active}: ${e[1].value}万元</br>
          `
        }
      }
    }
  },
  methods: {
    handleClick(item) {
      this.active = item
      if (item == '规上⼈⼒资源服务机') {
        this.chartData = [
          { year: '2024年1月', ratio: 26.18, growth_yoy: -0.6 },
          { year: '2024年1-2月', ratio: 32.41, growth_yoy: -1.82 },
          { year: '2024年1-3月', ratio: 42.03, growth_yoy: -1.27 },
          { year: '2024年1-4月', ratio: 37.36, growth_yoy: -3.94 },
          { year: '2024年1-5月', ratio: 35.36, growth_yoy: -4.05 }
        ]
      } else if (item == '规上⼈⼒资源服务机...') {
        this.chartData = [
          { year: '2024年1月', ratio: 46.18, growth_yoy: 4.4 },
          { year: '2024年1-2月', ratio: 52.41, growth_yoy: 3.18 },
          { year: '2024年1-3月', ratio: 62.03, growth_yoy: 3.73 },
          { year: '2024年1-4月', ratio: 57.36, growth_yoy: 1.06 },
          { year: '2024年1-5月', ratio: 55.36, growth_yoy: 1.15 }
        ]
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.box {
  background: linear-gradient(to bottom, #e6f8ff 0%, #ffffff 20%, #ffffff 100%) center / 100% no-repeat;
  border-radius: 15px;
  position: relative;
  margin-top: 35px;
  .box_title {
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: 36px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    font-size: 32px;
    color: #000000;
    line-height: 45px;
    text-align: center;
    font-style: normal;
    width: 100%;
    background: linear-gradient(0deg, #0284ff 0%, #1282ff 39%, #00bbff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    div {
      margin: 0 10px;
    }
    img {
      width: 59px;
      height: 17px;
    }
  }
  .contBox {
    margin-top: 24px;
    padding: 110px 36px 36px;
    position: relative;

    .toptext {
      width: 100%;

      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 28px;
      color: #212121;
      line-height: 48px;
      text-align: left;
      font-style: normal;
      text-indent: 2em;
      margin: 0px auto 20px;
      span {
        color: #3096ef;
      }
    }
    .baseCard {
      width: 100%;
      height: 200px;
      background: url('../img/baseCardbg.png') center/100% 100% no-repeat;
      border-radius: 10px;
      padding: 35px 30px;
      position: relative;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      margin-bottom: 24px;
      .title {
        font-family: PingFangSC, PingFang SC;
        font-weight: 600;
        font-size: 28px;
        color: #ffffff;
        line-height: 20px;
        text-align: left;
        font-style: normal;
      }
      .valbox {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .ltval {
          display: flex;
          align-items: baseline;
          .num {
            font-family: D-DIN, D-DIN;
            font-weight: bold;
            font-size: 68px;
            color: #ffffff;
            line-height: 27px;
            text-align: justify;
            font-style: normal;
          }
          .unit {
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 24px;
            color: #ffffff;
            line-height: 27px;
            text-align: left;
            font-style: normal;
          }
        }

        .rtval {
          display: flex;
          align-items: baseline;
          margin-top: 10px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 24px;
          color: #ffffff;
          line-height: 27px;
          text-align: left;
          font-style: normal;
          span {
            display: inline-block;
            width: 50px;
            height: 48px;
            background: url('../img/numbg.png') center/100% 100% no-repeat;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 32px;
            color: #ffffff;
            line-height: 45px;
            text-align: center;
            font-style: normal;
          }
        }
        .ctval {
          display: flex;
          align-items: baseline;
          margin-right: 10px;
          .desc {
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 24px;
            color: #ffffff;
            line-height: 27px;
            text-align: left;
            font-style: normal;
          }
          img {
            width: 25px;
            height: 35px;
            margin: 0 5px;
          }
          .value {
            font-family: D-DIN, D-DIN;
            font-weight: bold;
            font-size: 48px;
            color: #de242c;
            line-height: 27px;
            text-align: justify;
            font-style: normal;
            span {
              font-size: 24px;
            }
          }
        }
      }
    }

    .line {
      width: 100%;
      height: 5px;
      margin: 32px 0 20px;
      background: url('../img/line.png') center center/604px 100% no-repeat;
    }
    .btmCard {
      width: 100%;
      height: 146px;
      padding: 0 36px;
      background: url('../img/btmCard_bg.png') center center/100% 100% no-repeat;
      display: flex;
      margin: 15px auto 0;
      align-items: center;
      justify-content: space-between;
      .ltcont {
        display: flex;
        align-items: center;
        height: 100%;
        img {
          width: 90px;
          height: 90px;
        }
        .centercont {
          display: flex;
          flex-direction: column;
          margin-left: 26px;

          .time {
            width: 70px;
            height: 36px;
            background: url('../img/btmCard_time.png') center center/100% 100% no-repeat;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 24px;
            padding-left: 10px;
            color: #ffffff;
            line-height: 33px;
            text-align: left;
            font-style: normal;
          }
          .name {
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 28px;
            color: #212121;
            line-height: 40px;
            text-align: left;
            font-style: normal;
            width: 9em;
          }
        }
      }

      .val {
        display: flex;
        align-items: baseline;
        .value {
          font-family: D-DIN, D-DIN;
          font-weight: bold;
          font-size: 68px;
          color: #ff7b05;
          line-height: 74px;
          text-align: left;
          font-style: normal;
        }
        .unit {
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 28px;
          color: #666666;
          line-height: 40px;
          text-align: left;
          font-style: normal;
        }
      }
    }
    .btmcont {
      width: 100%;
      margin: 24px auto 0;
      position: relative;
      position: relative;

      .mt_btn {
        display: flex;
        margin: 24px auto;
        align-items: center;
        justify-content: center;
        .btnItem {
          margin: 0 10px;
          height: 52px;
          padding: 0 13px;
          border-radius: 26px;
          display: flex;
          align-items: center;
          justify-content: center;
          background: rgb(247, 248, 250);
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 24px;
          line-height: 33px;
          color: rgb(105, 105, 105);
          text-align: left;
          font-style: normal;
        }
        .active {
          background: linear-gradient(to right, rgb(78, 150, 255) 0%, rgb(95, 193, 255) 100%);
          color: #ffffff;
        }
      }

      .btmBar {
        padding: 0px 10px;
        width: 100%;
        height: 480px;
        margin: 0 auto 25px;
      }
    }
  }
}
</style>
