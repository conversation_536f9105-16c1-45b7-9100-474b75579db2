<template>
  <div class="box">
    <div class="box_title">
      <img src="../img/titlebg4.png" alt="" />
      <div>释放创业创新活⼒</div>
      <img src="../img/titlebg4.png" alt="" />
    </div>
    <div class="contBox">
      <div class="padNeed">
        <div class="toptext">
          研究起草<span>《山东省“创业齐鲁”行动方案（2024-2026年）》</span>，建立健全全省创业工作协同机制。
        </div>
        <div class="toptext">
          试点开发<span>“数字人民币”“创业券”“创业风险险”</span>，试点建设省级创业街区，进一步创新服务场景。
        </div>
        <div class="imglist imglist1">
          <div v-for="(item, i) in imglist" :key="i" class="imgitem">
            <img :src="item.img" alt="" />
            <div>{{ item.name }}</div>
          </div>
        </div>
        <div class="toptext">
          组织<span>第六届“中国创翼”创业创新大赛选拔赛暨第七届山东省创业大赛、第四届全国马兰花创业培训讲师大赛选拔赛暨山东省创业培训典型案例展示赛</span>
        </div>
      </div>
      <div class="padNeed">
        <div class="toptext">
          举办<span>“创智赋能”——山东省创业讲师教学能力提升研训活动、“齐聚赋能 鲁力助创”创业引领者专项系列活动</span>
        </div>
        <div class="toptext">为创业者搭建展示交流、政策支持的综合赋能平台。</div>
        <div class="imglist imglist2">
          <div v-for="(item, i) in imglist2" :key="i" class="imgitem">
            <img :src="item.img" alt="" />
            <div>{{ item.name }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { getAction } from '@/api/manage'
import img1 from '../img/img5.png'
import img2 from '../img/img6.png'
import img3 from '../img/img7.png'
import img4 from '../img/img8.png'
import img5 from '../img/img9.png'
import img6 from '../img/img10.png'
import img7 from '../img/img11.png'
export default {
  data() {
    return {
      isPad: window.matchMedia('(min-width: 600px)').matches,
      chartData: [],
      chartConfig: {},
      imglist: [
        {
          img: img1,
          name: '数字⼈⺠币'
        },
        {
          img: img2,
          name: '创业券'
        },
        {
          img: img3,
          name: '创业⻛险险'
        }
      ],
      imglist2: [
        {
          img: img4,
          name: '第六届“中国创翼”创业创新大赛选拔赛暨第七届…'
        },
        {
          img: img5,
          name: '第四届全国马兰花创业培训讲师大赛选拔赛暨山东省…'
        },
        {
          img: img6,
          name: '“创智赋能”'
        },
        {
          img: img7,
          name: '“齐聚赋能 鲁力助创”'
        }
      ]
    }
  },
  created() {
    this.loadData()
  },
  methods: {
    async loadData() {
      await getAction(
        import.meta.env.VITE_APP_SITUATION_BASE_URL + '/topic/data/listAllBySql/ydd_sthj_dqhj_PM2d5bh_month'
      ).then((res) => {
        if (res.success) {
          this.chartData = res.result
          this.chartConfig = {
            yAxis0_name: '单位：微克/立方米',
            yAxis1_name: '单位：%',
            tooltip_formatter: (e) => {
              let content = ''
              if (e[3].value > 0) {
                content = '同比提升' + e[3].value + '个百分点'
              } else if (e[3].value == 0) {
                content = '同比持平'
              } else {
                content = '同比降低' + e[3].value + '个百分点'
              }
              return `${e[0].axisValue}:</br>${e[1].seriesName}: ${e[1].value}微克/立方米</br>${content}`
            }
          }
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.box {
  background: linear-gradient(to bottom, #e6f8ff 0%, #ffffff 20%, #ffffff 100%) center / 100% no-repeat;
  border-radius: 15px;
  position: relative;
  margin-top: 35px;
  .box_title {
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: 36px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    font-size: 32px;
    color: #000000;
    line-height: 45px;
    text-align: center;
    font-style: normal;
    width: 100%;
    background: linear-gradient(0deg, #0284ff 0%, #1282ff 39%, #00bbff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    div {
      margin: 0 10px;
    }
    img {
      width: 59px;
      height: 17px;
    }
  }
  .contBox {
    margin-top: 24px;
    padding: 110px 36px 36px;
    position: relative;
    .toptext {
      width: 100%;

      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 28px;
      color: #212121;
      line-height: 48px;
      text-align: left;
      font-style: normal;
      text-indent: 2em;
      margin: 0px auto 20px;
      span {
        color: #3096ef;
      }
    }
    .imglist {
      width: 100%;
      display: grid;
      column-gap: 12px;
      row-gap: 12px;
      margin-top: 35px;
      grid-template-columns: repeat(2, 1fr);
      .imgitem {
        display: flex;
        flex-direction: column;
        align-items: center;
        img {
          width: 294px;
          height: 196px;
        }
        div {
          margin-top: -50px;
          width: 100%;
          height: 58px;
          display: flex;
          align-items: center;
          justify-content: center;
          text-align: center;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 24px;
          color: #ffffff;
          line-height: 28px;
          font-style: normal;
          background: url('../img/textbg2.png') center / 100% 100% no-repeat;
        }
      }
    }
    .imglist1 {
      grid-template-columns: repeat(3, 1fr);
      margin-bottom: 24px;
      .imgitem {
        img {
          width: 190px;
          height: 196px;
        }
      }
    }
    .imglist2 {
      margin-bottom: 24px;
      .imgitem:nth-last-of-type(4),
      .imgitem:nth-last-of-type(3) {
        div {
          height: 83px;
          background: url('../img/textbg.png') center / 100% 100% no-repeat;
        }
      }
    }
  }
}
</style>
