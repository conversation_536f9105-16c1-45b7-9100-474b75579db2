<template>
  <div class="cjzdqtjy_cpn4">
    <div class="top2">加⼤急需紧缺职业（⼯种）技能培训⼒度</div>
    <div class="btmcont">
      <div class="mt_btn">
        <div
          v-for="(item, i) in btnList"
          :key="i"
          :class="{ btnItem: true, active: active === item }"
          @click="handleClick(item)"
        >
          {{ item }}
        </div>
      </div>
      <div class="btmBar">
        <barChart :chart-data="chartData" :chart-config="chartConfig" type="4" :show-data-zoom="false" />
      </div>
    </div>
  </div>
</template>
<script>
import { getAction } from '@/api/manage'
import barChart from '../../common/barChart.vue'
export default {
  components: {
    barChart
  },
  data() {
    return {
      active: '急需紧缺职业技能培训',
      btnList: ['急需紧缺职业技能培训', '农⺠⼯、⾼校毕业⽣等重点…'],
      isPad: window.matchMedia('(min-width: 600px)').matches,
      chartData: [
        { year: '2024年1月', ratio: 26.18, growth_yoy: -0.6 },
        { year: '2024年1-2月', ratio: 32.41, growth_yoy: -1.82 },
        { year: '2024年1-3月', ratio: 42.03, growth_yoy: -1.27 },
        { year: '2024年1-4月', ratio: 37.36, growth_yoy: -3.94 },
        { year: '2024年1-5月', ratio: 35.36, growth_yoy: -4.05 }
      ],
      chartConfig: {
        yAxis0_name: '单位：万人次',
        yAxis1_name: '单位：%',
        tooltip_formatter: (e) => {
          return `${e[0].axisValue}:</br>${this.active}: ${e[1].value}万人次</br>
          `
        }
      }
    }
  },
  methods: {
    handleClick(item) {
      this.active = item
      if (item == '急需紧缺职业技能培训') {
        this.chartData = [
          { year: '2024年1月', ratio: 26.18, growth_yoy: -0.6 },
          { year: '2024年1-2月', ratio: 32.41, growth_yoy: -1.82 },
          { year: '2024年1-3月', ratio: 42.03, growth_yoy: -1.27 },
          { year: '2024年1-4月', ratio: 37.36, growth_yoy: -3.94 },
          { year: '2024年1-5月', ratio: 35.36, growth_yoy: -4.05 }
        ]
      } else if (item == '农⺠⼯、⾼校毕业⽣等重点…') {
        this.chartData = [
          { year: '2024年1月', ratio: 46.18, growth_yoy: 4.4 },
          { year: '2024年1-2月', ratio: 52.41, growth_yoy: 3.18 },
          { year: '2024年1-3月', ratio: 62.03, growth_yoy: 3.73 },
          { year: '2024年1-4月', ratio: 57.36, growth_yoy: 1.06 },
          { year: '2024年1-5月', ratio: 55.36, growth_yoy: 1.15 }
        ]
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.cjzdqtjy_cpn4 {
  position: relative;
  .top2 {
    margin: 30px auto 0;
    width: 645px;
    background: #e8f3ff;
    border: 5px solid white;
    box-shadow: 0px 5px 15px rgba(222, 222, 222, 1);
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    border-radius: 16px;
    font-size: 28px;
    color: #212121;
    line-height: 48px;
    text-align: left;
    font-style: normal;
    padding: 30px 20px;
    text-align: center;
    span {
      color: #3096ef;
    }
  }

  .btmcont {
    width: 100%;
    margin: 24px auto 0;
    position: relative;
    position: relative;

    .mt_btn {
      display: flex;
      margin: 24px auto;
      align-items: center;
      justify-content: center;
      .btnItem {
        margin: 0 10px;
        height: 52px;
        white-space: nowrap;
        padding: 0 13px;
        border-radius: 26px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgb(247, 248, 250);
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 24px;
        line-height: 33px;
        color: rgb(105, 105, 105);
        text-align: left;
        font-style: normal;
      }
      .active {
        background: linear-gradient(to right, rgb(78, 150, 255) 0%, rgb(95, 193, 255) 100%);
        color: #ffffff;
      }
    }

    .btmBar {
      padding: 0px 10px;
      width: 100%;
      height: 480px;
      margin: 0 auto 25px;
    }
  }
}
</style>
