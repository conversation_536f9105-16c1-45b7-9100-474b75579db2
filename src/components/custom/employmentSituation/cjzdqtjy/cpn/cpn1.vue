<template>
  <div class="cjzdqtjy_cpn1">
    <div class="top2">
      扎实开展农村外出务工人员<span>“鲁力同心·春暖行动”</span>等活动，有效拓宽农村劳动力外出就业渠道。
    </div>
    <div class="btmcont">
      <div class="mt_btn">
        <div
          v-for="(item, i) in btnList"
          :key="i"
          :class="{ btnItem: true, active: active === item }"
          @click="handleClick(item)"
        >
          {{ item }}
        </div>
      </div>
      <div class="btmBar">
        <barChart :chart-data="chartData" :chart-config="chartConfig" type="5" :show-data-zoom="false" />
      </div>
    </div>
    <img v-if="isPad" src="../../img/deco.png" alt="" />
  </div>
</template>
<script>
import { getAction } from '@/api/manage'
import barChart from '../../common/barChart.vue'
export default {
  components: {
    barChart
  },
  data() {
    return {
      active: '外出务⼯农村劳动⼒',
      btnList: ['外出务⼯农村劳动⼒', '外出务⼯⽉收⼊'],
      isPad: window.matchMedia('(min-width: 600px)').matches,
      chartData: [
        { year: '2023年', ratio: 20.78, growth_yoy: 10.35 },
        { year: '2024年一季度', ratio: 14.64, growth_yoy: -3.7 },
        { year: '2024年上半年', ratio: 45.94, growth_yoy: 9.83 },
        { year: '2024年1-7月', ratio: 25.04, growth_yoy: -0.66 },
        { year: '2024年1-8月', ratio: 42.09, growth_yoy: 9.87 }
      ],
      chartConfig: {
        yAxis0_name: '单位：万人',
        yAxis1_name: '单位：%',
        tooltip_formatter: (e) => {
          console.log(e)
          return `${e[0].axisValue}:</br>${this.active}: ${e[1].value}万人</br>
          同比增长率: ${e[3].value}%
          `
        }
      }
    }
  },
  methods: {
    handleClick(item) {
      this.active = item
      if (item == '外出务⼯农村劳动⼒') {
        this.chartData = [
          { year: '2023年', ratio: 20.78, growth_yoy: 10.35 },
          { year: '2024年一季度', ratio: 14.64, growth_yoy: -3.7 },
          { year: '2024年上半年', ratio: 45.94, growth_yoy: 9.83 },
          { year: '2024年1-7月', ratio: 25.04, growth_yoy: -0.66 },
          { year: '2024年1-8月', ratio: 42.09, growth_yoy: 9.87 }
        ]
        this.chartConfig = {
          yAxis0_name: '单位：万人',
          yAxis1_name: '单位：%',
          tooltip_formatter: (e) => {
            return `${e[0].axisValue}:</br>${this.active}: ${e[1].value}万人</br>
          同比增长率: ${e[3].value}%
          `
          }
        }
      } else if (item == '外出务⼯⽉收⼊') {
        this.chartData = [
          { year: '2023年', ratio: 32.22, growth_yoy: -1.65 },
          { year: '2024年一季度', ratio: 72.64, growth_yoy: 7.3 },
          { year: '2024年上半年', ratio: 55.94, growth_yoy: -1.17 },
          { year: '2024年1-7月', ratio: 76.04, growth_yoy: 9.84 },
          { year: '2024年1-8月', ratio: 52.09, growth_yoy: 4.13 }
        ]
        this.chartConfig = {
          yAxis0_name: '单位：万元',
          yAxis1_name: '单位：%',
          tooltip_formatter: (e) => {
            return `${e[0].axisValue}:</br>${this.active}: ${e[1].value}万元</br>
          同比增长率: ${e[3].value}%
          `
          }
        }
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.cjzdqtjy_cpn1 {
  position: relative;
  .top2 {
    margin: 30px auto 0;
    width: 645px;
    background: #e8f3ff;
    border: 5px solid white;
    box-shadow: 0px 5px 15px rgba(222, 222, 222, 1);
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    border-radius: 16px;
    font-size: 28px;
    color: #212121;
    line-height: 48px;
    text-align: left;
    font-style: normal;
    padding: 30px 20px;
    text-align: center;
    span {
      color: #3096ef;
    }
  }

  .btmcont {
    width: 100%;
    margin: 24px auto 0;
    position: relative;
    position: relative;

    .mt_btn {
      display: flex;
      margin: 24px auto;
      align-items: center;
      justify-content: center;
      .btnItem {
        margin: 0 10px;
        height: 52px;
        padding: 0 13px;
        border-radius: 26px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgb(247, 248, 250);
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 24px;
        line-height: 33px;
        color: rgb(105, 105, 105);
        text-align: left;
        font-style: normal;
      }
      .active {
        background: linear-gradient(to right, rgb(78, 150, 255) 0%, rgb(95, 193, 255) 100%);
        color: #ffffff;
      }
    }

    .btmBar {
      padding: 0px 10px;
      width: 100%;
      height: 480px;
      margin: 0 auto 25px;
    }
  }
}
</style>
