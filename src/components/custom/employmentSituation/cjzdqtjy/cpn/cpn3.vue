<template>
  <div class="cjzdqtjy_cpn3">
    <div class="top2">开展城乡公益性岗位<span>“质效提升年”</span></div>
    <div class="btmcont">
      <div class="mt_btn">
        <div
          v-for="(item, i) in btnList"
          :key="i"
          :class="{ btnItem: true, active: active === item }"
          @click="handleClick(item)"
        >
          {{ item }}
        </div>
      </div>
      <div class="btmBar">
        <barChart :chart-data="chartData" :chart-config="chartConfig" type="4" :show-data-zoom="false" />
      </div>
    </div>
  </div>
</template>
<script>
import { getAction } from '@/api/manage'
import barChart from '../../common/barChart.vue'
export default {
  components: {
    barChart
  },
  data() {
    return {
      active: '新开发城乡公益性岗位',
      btnList: ['新开发城乡公益性岗位', '新安置上岗⼈数'],
      isPad: window.matchMedia('(min-width: 600px)').matches,
      chartData: [
        { year: '2024年1月', ratio: 36.18, growth_yoy: 1.9 },
        { year: '2024年1-2月', ratio: 42.41, growth_yoy: 0.68 },
        { year: '2024年1-3月', ratio: 52.03, growth_yoy: 1.23 },
        { year: '2024年1-4月', ratio: 47.36, growth_yoy: -1.44 },
        { year: '2024年1-5月', ratio: 45.36, growth_yoy: -1.55 }
      ],
      chartConfig: {
        yAxis0_name: '单位：万个',
        yAxis1_name: '单位：%',
        tooltip_formatter: (e) => {
          return `${e[0].axisValue}:</br>${this.active}: ${e[1].value}万个</br>
          `
        }
      }
    }
  },
  methods: {
    handleClick(item) {
      this.active = item
      if (item == '新开发城乡公益性岗位') {
        this.chartData = [
          { year: '2024年1月', ratio: 36.18, growth_yoy: 1.9 },
          { year: '2024年1-2月', ratio: 42.41, growth_yoy: 0.68 },
          { year: '2024年1-3月', ratio: 52.03, growth_yoy: 1.23 },
          { year: '2024年1-4月', ratio: 47.36, growth_yoy: -1.44 },
          { year: '2024年1-5月', ratio: 45.36, growth_yoy: -1.55 }
        ]
        this.chartConfig = {
          yAxis0_name: '单位：万个',
          yAxis1_name: '单位：%',
          tooltip_formatter: (e) => {
            return `${e[0].axisValue}:</br>${this.active}: ${e[1].value}万个</br>
          `
          }
        }
      } else if (item == '新安置上岗⼈数') {
        this.chartData = [
          { year: '2024年1月', ratio: 42.16, growth_yoy: 8.13 },
          { year: '2024年1-2月', ratio: 33.01, growth_yoy: -2.19 },
          { year: '2024年1-3月', ratio: 67.44, growth_yoy: 4.09 },
          { year: '2024年1-4月', ratio: 46.39, growth_yoy: 0.54 },
          { year: '2024年1-5月', ratio: 55.87, growth_yoy: 3.66 }
        ]
        this.chartConfig = {
          yAxis0_name: '单位：万人',
          yAxis1_name: '单位：%',
          tooltip_formatter: (e) => {
            return `${e[0].axisValue}:</br>${this.active}: ${e[1].value}万人</br>
          `
          }
        }
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.cjzdqtjy_cpn3 {
  position: relative;
  .top2 {
    margin: 30px auto 0;
    width: 645px;
    background: #e8f3ff;
    border: 5px solid white;
    box-shadow: 0px 5px 15px rgba(222, 222, 222, 1);
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    border-radius: 16px;
    font-size: 28px;
    color: #212121;
    line-height: 48px;
    text-align: left;
    font-style: normal;
    padding: 30px 20px;
    text-align: center;
    span {
      color: #3096ef;
    }
  }

  .btmcont {
    width: 100%;
    margin: 24px auto 0;
    position: relative;
    position: relative;

    .mt_btn {
      display: flex;
      margin: 24px auto;
      align-items: center;
      justify-content: center;
      .btnItem {
        margin: 0 10px;
        height: 52px;
        padding: 0 13px;
        border-radius: 26px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgb(247, 248, 250);
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 24px;
        line-height: 33px;
        color: rgb(105, 105, 105);
        text-align: left;
        font-style: normal;
      }
      .active {
        background: linear-gradient(to right, rgb(78, 150, 255) 0%, rgb(95, 193, 255) 100%);
        color: #ffffff;
      }
    }

    .btmBar {
      padding: 0px 10px;
      width: 100%;
      height: 480px;
      margin: 0 auto 25px;
    }
  }
}
</style>
