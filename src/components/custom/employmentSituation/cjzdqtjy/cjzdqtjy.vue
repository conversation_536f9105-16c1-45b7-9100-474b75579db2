<template>
  <div class="box">
    <div class="title">
      <img src="../img/titlebg4.png" alt="" />
      <div>促进重点群体就业</div>
      <img src="../img/titlebg4.png" alt="" />
    </div>
    <div class="contBox">
      <div class="toptext">
        面对严峻形势，主汛期前高效完成省市县三级防汛抗旱指挥体系调整优化，水利部门充分发挥牵头抓总作用和部门专业优势，先后：
      </div>
      <div class="f_cont">
        <div v-for="(item, i) in datalist" :key="i" class="f_contitem">
          <div class="row1">
            <div class="num">{{ item.value }}</div>
            <div class="unit">{{ item.unit }}</div>
          </div>
          <div class="row2">
            {{ item.name }}
          </div>
        </div>
      </div>
      <div class="padNeed">
        <div class="line"></div>
        <cpn1 />
        <div class="line"></div>
        <cpn2 />
      </div>
      <div class="padNeed">
        <div class="line"></div>
        <cpn3 />
        <div class="line"></div>
        <cpn4 />
      </div>
    </div>
  </div>
</template>
<script>
import cpn1 from './cpn/cpn1.vue'
import cpn2 from './cpn/cpn2.vue'
import cpn3 from './cpn/cpn3.vue'
import cpn4 from './cpn/cpn4.vue'
export default {
  components: {
    cpn1,
    cpn2,
    cpn3,
    cpn4
  },
  data() {
    return {
      isPad: window.matchMedia('(min-width: 600px)').matches,
      datalist: [
        {
          name: '线下校园招聘活动数',
          value: '1110',
          unit: '场'
        },
        {
          name: '毕业⽣参与次数',
          value: '74.33',
          unit: '万人次'
        },
        {
          name: '⽤⼈单位数',
          value: '4.18',
          unit: '万家'
        },
        {
          name: '提供岗位数',
          value: '112.4',
          unit: '万个'
        }
      ]
    }
  }
}
</script>
<style lang="scss" scoped>
.box {
  background: linear-gradient(to bottom, #e6f8ff 0%, #ffffff 20%, #ffffff 100%) center / 100% no-repeat;
  border-radius: 15px;
  position: relative;
  margin-top: 35px;
  .title {
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: 36px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    font-size: 32px;
    color: #000000;
    line-height: 45px;
    text-align: center;
    font-style: normal;
    width: 100%;
    background: linear-gradient(0deg, #0284ff 0%, #1282ff 39%, #00bbff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    div {
      margin: 0 10px;
    }
    img {
      width: 59px;
      height: 17px;
    }
  }
  .contBox {
    margin-top: 24px;
    padding: 110px 36px 36px;
    position: relative;
    .toptext {
      width: 100%;
      background: url('../img/toptextbg.png') center / 100% 100% no-repeat;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 28px;
      color: #212121;
      line-height: 48px;
      text-align: left;
      font-style: normal;
      text-indent: 2em;
      margin: 0px auto 20px;
      span {
        color: #3096ef;
      }
    }
    .f_cont {
      display: grid;
      gap: 24px;
      margin-top: 23px;
      grid-template-columns: repeat(2, 1fr);
      .f_contitem {
        width: 100%;
        display: flex;
        height: 227px;
        flex-direction: column;
        justify-content: space-evenly;
        align-items: center;
        background: rgb(232, 243, 255);
        border-radius: 15px;
        .row1 {
          display: flex;
          align-items: baseline;
          .num {
            font-family: D-DIN, D-DIN;
            font-weight: bold;
            font-size: 68px;
            color: #ff7b05;
            line-height: 74px;
            text-align: right;
            font-style: normal;
          }
          .unit {
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 24px;
            color: #666666;
            line-height: 33px;
            text-align: left;
            font-style: normal;
            margin: 0 0 0 5px;
          }
        }
        .row2 {
          width: 278px;
          //   height: 63px;
          padding: 10px 0px;
          background: linear-gradient(to right, rgb(69, 183, 253) 0%, rgb(91, 220, 254) 100%);
          font-family: PingFangSC, PingFang SC;
          border-radius: 32px;
          font-weight: 600;
          font-size: 28px;
          color: #ffffff;
          line-height: 40px;
          text-align: center;
          font-style: normal;
          display: flex;
          align-items: center;
          justify-content: center;
          span {
            display: inline-block;
            background: #ffffff;
            border-radius: 16px;
            opacity: 0.8;
            padding: 2px 10px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            margin-right: 5px;
            font-size: 24px;
            color: #666666;
            line-height: 33px;
            text-align: left;
            font-style: normal;
          }
        }
      }
    }
    .line {
      width: 100%;
      height: 5px;
      margin: 32px 0 20px;
      background: url('../img/line.png') center center/604px 100% no-repeat;
    }
  }
}
</style>
