<template>
  <div class="box">
    <div class="title">
      <img src="../img/titlebg4.png" alt="" />
      <div>抓紧就业政策落实</div>
      <img src="../img/titlebg4.png" alt="" />
    </div>
    <div class="contBox">
      <cpn1 />
      <div class="line"></div>
      <cpn2 />
      <div class="line"></div>
      <cpn3 />
    </div>
  </div>
</template>
<script>
import cpn1 from './cpn/cpn1.vue'
import cpn2 from './cpn/cpn2.vue'
import cpn3 from './cpn/cpn3.vue'
export default {
  components: { cpn1, cpn2, cpn3 },
  data() {
    return {
      isPad: window.matchMedia('(min-width: 600px)').matches
    }
  }
}
</script>
<style lang="scss" scoped>
.box {
  background: linear-gradient(to bottom, #e6f8ff 0%, #ffffff 20%, #ffffff 100%) center / 100% no-repeat;
  border-radius: 15px;
  position: relative;
  margin-top: 35px;
  .title {
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: 36px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    font-size: 32px;
    color: #000000;
    line-height: 45px;
    text-align: center;
    font-style: normal;
    width: 100%;
    background: linear-gradient(0deg, #0284ff 0%, #1282ff 39%, #00bbff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    div {
      margin: 0 10px;
    }
    img {
      width: 59px;
      height: 17px;
    }
  }
  .contBox {
    margin-top: 24px;
    padding: 110px 36px 36px;
    position: relative;
    .line {
      width: 100%;
      height: 5px;
      margin: 32px 0 20px;
      background: url('../img/line.png') center center/604px 100% no-repeat;
    }
  }
}
</style>
