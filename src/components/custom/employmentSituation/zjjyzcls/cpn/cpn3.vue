<template>
  <div class="zjjyzcls_cpn3">
    <div class="top">
      <div class="num">03</div>
      顶格延续实施新⼀轮稳岗返还、技能提升补贴等各项政策
    </div>
    <div class="btmcont">
      <div class="mt_btn">
        <div
          v-for="(item, i) in btnList"
          :key="i"
          :class="{ btnItem: true, active: active1 === item }"
          @click="handleClick1(item)"
        >
          {{ item }}
        </div>
      </div>
      <div class="btmBar">
        <barChart :chart-data="chartData1" :chart-config="chartConfig1" type="4" :show-data-zoom="false" />
      </div>
    </div>
    <div class="btmcont">
      <div class="mt_btn">
        <div
          v-for="(item, i) in btnList2"
          :key="i"
          :class="{ btnItem: true, active: active2 === item }"
          @click="handleClick2(item)"
        >
          {{ item }}
        </div>
      </div>
      <div class="btmBar">
        <barChart :chart-data="chartData2" :chart-config="chartConfig2" type="4" :show-data-zoom="false" />
      </div>
    </div>
  </div>
</template>
<script>
import { getAction } from '@/api/manage'
import barChart from '../../common/barChart.vue'

export default {
  components: {
    barChart
  },
  data() {
    return {
      active1: '稳岗返还资⾦',
      btnList: ['稳岗返还资⾦', '参保单位参保次数'],
      active2: '技能提升补贴',
      btnList2: ['技能提升补贴', '参保⼈次'],
      isPad: window.matchMedia('(min-width: 600px)').matches,
      chartData1: [
        { year: '2024年1月', ratio: 28.22, growth_yoy: 2.1 },
        { year: '2024年1-2月', ratio: 24.36, growth_yoy: 4.3 },
        { year: '2024年1-3月', ratio: 33.14, growth_yoy: 1.8 },
        { year: '2024年1-4月', ratio: 34.5, growth_yoy: 6.8 },
        { year: '2024年1-5月', ratio: 55.59, growth_yoy: 1.85 }
      ],
      chartConfig1: {
        yAxis1_name: '单位：亿元',
        tooltip_formatter: (e) => {
          return `${e[0].axisValue}:</br>${this.active1}: ${e[1].value}万人</br>
          `
        }
      },
      chartData2: [
        { year: '2024年1月', ratio: 32.78, growth_yoy: 2.35 },
        { year: '2024年1-2月', ratio: 50.64, growth_yoy: 4.34 },
        { year: '2024年1-3月', ratio: 40.94, growth_yoy: 1.83 },
        { year: '2024年1-4月', ratio: 41.04, growth_yoy: 6.84 },
        { year: '2024年1-5月', ratio: 30.09, growth_yoy: 1.87 }
      ],
      chartConfig2: {
        yAxis1_name: '单位：亿元',
        tooltip_formatter: (e) => {
          return `${e[0].axisValue}:</br>${this.active2}: ${e[1].value}万人</br>
          `
        }
      }
    }
  },
  methods: {
    handleClick1(item) {
      this.active1 = item
      if (item == '稳岗返还资⾦') {
        this.chartData1 = [
          { year: '2024年1月', ratio: 28.22, growth_yoy: 2.1 },
          { year: '2024年1-2月', ratio: 24.36, growth_yoy: 4.3 },
          { year: '2024年1-3月', ratio: 33.14, growth_yoy: 1.8 },
          { year: '2024年1-4月', ratio: 34.5, growth_yoy: 6.8 },
          { year: '2024年1-5月', ratio: 55.59, growth_yoy: 1.85 }
        ]
      } else if (item == '参保单位参保次数') {
        this.chartData1 = [
          { year: '2024年1月', ratio: 47.58, growth_yoy: 6.55 },
          { year: '2024年1-2月', ratio: 23.64, growth_yoy: -0.56 },
          { year: '2024年1-3月', ratio: 67.94, growth_yoy: 3.53 },
          { year: '2024年1-4月', ratio: 48.04, growth_yoy: 3.34 },
          { year: '2024年1-5月', ratio: 54.09, growth_yoy: 4.27 }
        ]
      }
    },
    handleClick2(item) {
      this.active2 = item
      if (item == '技能提升补贴') {
        this.chartData2 = [
          { year: '2024年1月', ratio: 32.78, growth_yoy: 2.35 },
          { year: '2024年1-2月', ratio: 50.64, growth_yoy: 4.34 },
          { year: '2024年1-3月', ratio: 40.94, growth_yoy: 1.83 },
          { year: '2024年1-4月', ratio: 41.04, growth_yoy: 6.84 },
          { year: '2024年1-5月', ratio: 30.09, growth_yoy: 1.87 }
        ]
      } else if (item == '参保⼈次') {
        this.chartData2 = [
          { year: '2024年1月', ratio: 51.78, growth_yoy: 6.85 },
          { year: '2024年1-2月', ratio: 21.64, growth_yoy: -0.96 },
          { year: '2024年1-3月', ratio: 70.94, growth_yoy: 3.63 },
          { year: '2024年1-4月', ratio: 52.04, growth_yoy: 3.44 },
          { year: '2024年1-5月', ratio: 58.09, growth_yoy: 4.37 }
        ]
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.zjjyzcls_cpn3 {
  .btmcont {
    width: 100%;
    margin: 24px auto 0;
    position: relative;
    position: relative;

    .mt_btn {
      display: flex;
      margin: 24px auto;
      align-items: center;
      justify-content: center;
      .btnItem {
        margin: 0 10px;
        height: 52px;
        padding: 0 13px;
        border-radius: 26px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgb(247, 248, 250);
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 24px;
        line-height: 33px;
        color: rgb(105, 105, 105);
        text-align: left;
        font-style: normal;
      }
      .active {
        background: linear-gradient(to right, rgb(78, 150, 255) 0%, rgb(95, 193, 255) 100%);
        color: #ffffff;
      }
    }

    .btmBar {
      padding: 0px 10px;
      width: 100%;
      height: 480px;
      margin: 0px auto;
    }
  }
  .btmcont:last-of-type {
    margin-top: 35px;
  }
  .top {
    margin: 30px auto 0;
    width: 100%;

    background: #e8f3ff;
    border: 5px solid white;
    box-shadow: 0px 5px 15px rgba(222, 222, 222, 1);
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    border-radius: 16px;
    font-size: 28px;
    color: #212121;
    line-height: 48px;
    text-align: left;
    font-style: normal;
    padding: 30px 30px 30px 70px;
    text-align: left;
    position: relative;
    span {
      color: #3096ef;
    }
    .num {
      background: url('../../img/numbg2.png') center/ 100% 100% no-repeat;
      width: 101px;
      height: 107px;
      position: absolute;
      left: -30px;
      top: -30px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: PingFangSC, PingFang SC;
      font-weight: 600;
      font-size: 48px;
      color: #feffff;
      line-height: 67px;
      text-align: center;
      font-style: normal;
    }
  }
}
</style>
