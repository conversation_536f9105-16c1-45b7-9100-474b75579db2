<template>
  <div class="zjjyzcls_cpn2">
    <div class="top">
      <div class="num">02</div>
      推进创业担保贷款等扶持政策落实
    </div>
    <div class="btmcont">
      <div class="mt_btn">
        <div
          v-for="(item, i) in btnList"
          :key="i"
          :class="{ btnItem: true, active: active === item }"
          @click="handleClick(item)"
        >
          {{ item }}
        </div>
      </div>
      <div class="btmBar">
        <barChart :chart-data="chartData" :chart-config="chartConfig" type="4" :show-data-zoom="false" />
      </div>
    </div>
  </div>
</template>
<script>
import { getAction } from '@/api/manage'
import barChart from '../../common/barChart.vue'

export default {
  components: {
    barChart
  },
  data() {
    return {
      active: '创业担保贷款',
      btnList: ['创业担保贷款', '创业担保贷款数', '创业提振贷'],
      isPad: window.matchMedia('(min-width: 600px)').matches,
      chartData: [
        { year: '2024年1月', ratio: 39.78, growth_yoy: 5.85 },
        { year: '2024年1-2月', ratio: 34.64, growth_yoy: 0.04 },
        { year: '2024年1-3月', ratio: 59.94, growth_yoy: 2.63 },
        { year: '2024年1-4月', ratio: 56.04, growth_yoy: 1.94 },
        { year: '2024年1-5月', ratio: 42.09, growth_yoy: 2.37 }
      ],
      chartConfig: {
        yAxis1_name: '单位：亿元',
        tooltip_formatter: (e) => {
          return `${e[0].axisValue}:</br>${this.active}: ${e[1].value}万人</br>
          `
        }
      }
    }
  },
  methods: {
    handleClick(item) {
      this.active = item
      if (item == '创业担保贷款') {
        this.chartData = [
          { year: '2024年1月', ratio: 45.78, growth_yoy: 6.85 },
          { year: '2024年1-2月', ratio: 24.64, growth_yoy: -0.96 },
          { year: '2024年1-3月', ratio: 65.94, growth_yoy: 3.63 },
          { year: '2024年1-4月', ratio: 46.04, growth_yoy: 3.44 },
          { year: '2024年1-5月', ratio: 52.09, growth_yoy: 4.37 }
        ]
      } else if (item == '创业担保贷款数') {
        this.chartData = [
          { year: '2024年1月', ratio: 25.5, growth_yoy: 2.35 },
          { year: '2024年1-2月', ratio: 51.64, growth_yoy: 4.34 },
          { year: '2024年1-3月', ratio: 35.94, growth_yoy: 1.83 },
          { year: '2024年1-4月', ratio: 36.04, growth_yoy: 6.84 },
          { year: '2024年1-5月', ratio: 22.09, growth_yoy: 1.87 }
        ]
      } else if (item == '创业提振贷') {
        this.chartData = [
          { year: '2024年1月', ratio: 39.78, growth_yoy: 5.85 },
          { year: '2024年1-2月', ratio: 34.64, growth_yoy: 0.04 },
          { year: '2024年1-3月', ratio: 59.94, growth_yoy: 2.63 },
          { year: '2024年1-4月', ratio: 56.04, growth_yoy: 1.94 },
          { year: '2024年1-5月', ratio: 42.09, growth_yoy: 2.37 }
        ]
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.zjjyzcls_cpn2 {
  .btmcont {
    width: 100%;
    margin: 24px auto 0;
    position: relative;
    position: relative;

    .mt_btn {
      display: flex;
      margin: 24px auto;
      align-items: center;
      justify-content: center;
      .btnItem {
        margin: 0 10px;
        height: 52px;
        padding: 0 13px;
        border-radius: 26px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgb(247, 248, 250);
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 24px;
        line-height: 33px;
        color: rgb(105, 105, 105);
        text-align: left;
        font-style: normal;
      }
      .active {
        background: linear-gradient(to right, rgb(78, 150, 255) 0%, rgb(95, 193, 255) 100%);
        color: #ffffff;
      }
    }

    .btmBar {
      padding: 0px 10px;
      width: 100%;
      height: 480px;
      margin: 0 auto 25px;
    }
  }
  .top {
    margin: 30px auto 0;
    width: 100%;

    background: #e8f3ff;
    border: 5px solid white;
    box-shadow: 0px 5px 15px rgba(222, 222, 222, 1);
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    border-radius: 16px;
    font-size: 28px;
    color: #212121;
    line-height: 48px;
    text-align: left;
    font-style: normal;
    padding: 30px 30px 30px 70px;
    text-align: left;
    position: relative;
    span {
      color: #3096ef;
    }
    .num {
      background: url('../../img/numbg2.png') center/ 100% 100% no-repeat;
      width: 101px;
      height: 107px;
      position: absolute;
      left: -30px;
      top: -30px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: PingFangSC, PingFang SC;
      font-weight: 600;
      font-size: 48px;
      color: #feffff;
      line-height: 67px;
      text-align: center;
      font-style: normal;
    }
  }
}
</style>
