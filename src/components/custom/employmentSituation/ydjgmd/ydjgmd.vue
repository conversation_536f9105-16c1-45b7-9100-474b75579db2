<template>
  <div class="box">
    <div class="toptitle">应对结构⽭盾</div>

    <div class="contBox">
      <div class="top">
        <div class="num">01</div>
        组织实施就业岗位与职业能力更新工程，主动应对技术进步对就业影响，大力开发新就业岗位、提升新就业技能、扩大新就业空间。
      </div>

      <div class="top">
        <div class="num">02</div>
        持续开展<span>好大中城市联合招聘、金秋招聘月</span>等公共就业服务专项活动，探索建立就业政策信息库，加强政策发布与业务经办无感对接，分级分类设计个性化标签，推动<span>“政策找人”</span>。
      </div>
      <div class="imglist">
        <div v-for="(item, i) in imglist" :key="i" class="imgitem">
          <img :src="item.img" alt="" />
          <div>{{ item.name }}</div>
        </div>
      </div>
      <div class="top">
        <div class="num">03</div>
        强化失业动态监测，适当调整失业动态监测样本，做好日常监测工作，防范化解规模性失业风险。
      </div>
    </div>
    <img v-if="isPad" src="../img/deco2.png" alt="">
  </div>
</template>
<script>
import img1 from '../img/img22.png'
import img2 from '../img/img23.png'
export default {
  data() {
    return {
      isPad: window.matchMedia('(min-width: 600px)').matches,
      imglist: [
        {
          img: img1,
          name: '好⼤中城市联合招聘'
        },
        {
          img: img2,
          name: '⾦秋招聘⽉'
        }
      ]
    }
  }
}
</script>
<style lang="scss" scoped>
.box {
  background: linear-gradient(to bottom, #e6f8ff 0%, #ffffff 20%, #ffffff 100%) center / 100% no-repeat;
  border-radius: 15px;
  position: relative;
  margin-top: 60px;
  .toptitle {
    width: 282px;
    height: 68px;
    position: absolute;
    top: -30px;
    left: 50%;
    transform: translateX(-50%);
    background: url('../img/titlebg1.png') center/100% 100% no-repeat;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 32px;
    color: #ffffff;
    line-height: 60px;
    text-align: center;
    font-style: normal;
  }

  .contBox {
    margin-top: 24px;
    padding: 36px;
    position: relative;
    .line {
      width: 100%;
      height: 5px;
      margin: 32px 0 20px;
      background: url('../img/line.png') center center/604px 100% no-repeat;
    }
    .top {
      margin: 30px auto 0;
      width: 645px;
      background: #e8f3ff;
      border: 5px solid white;
      box-shadow: 0px 5px 15px rgba(222, 222, 222, 1);
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      border-radius: 16px;
      font-size: 28px;
      color: #212121;
      line-height: 48px;
      text-align: left;
      font-style: normal;
      padding: 30px 30px 30px 70px;
      text-align: left;
      position: relative;
      span {
        color: #3096ef;
      }
      .num {
        background: url('../img/numbg2.png') center/ 100% 100% no-repeat;
        width: 101px;
        height: 107px;
        position: absolute;
        left: -30px;
        top: -30px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: PingFangSC, PingFang SC;
        font-weight: 600;
        font-size: 48px;
        color: #feffff;
        line-height: 67px;
        text-align: center;
        font-style: normal;
      }
    }

    .imglist {
      width: 100%;
      display: grid;
      column-gap: 12px;
      row-gap: 12px;
      margin-top: 35px;
      grid-template-columns: repeat(2, 1fr);
      .imgitem {
        display: flex;
        flex-direction: column;
        align-items: center;
        img {
          width: 294px;
          height: 196px;
        }
        div {
          margin-top: -50px;
          width: 100%;
          height: 58px;
          display: flex;
          align-items: center;
          justify-content: center;
          text-align: center;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 24px;
          color: #ffffff;
          line-height: 28px;
          font-style: normal;
          background: url('../img/textbg2.png') center / 100% 100% no-repeat;
        }
      }
    }
  }
}
</style>
