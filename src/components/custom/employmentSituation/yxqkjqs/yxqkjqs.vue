<template>
  <div class="box">
    <div class="toptitle">运⾏情况及趋势</div>
    <div class="contBox">
      <div class="toptext">
        全省各级人社部门积极提升政治站位，聚焦高质量充分就业，稳扎稳打、踏踏实实、蹄疾步稳，扎实做好稳就业各项工作。
      </div>
      <div class="padNeed">
        <div class="box_title">
          <img src="../img/titlebg4.png" alt="" />
          <div>领域 · 城镇新增就业情况</div>
          <img src="../img/titlebg4.png" alt="" />
        </div>
        <div class="btmBar">
          <barChart :chart-data="chartData" :chart-config="chartConfig" type="1" :show-data-zoom="false" />
        </div>
        <div class="top">
          <img src="../img/icon.png" alt="" />
          <div class="cont">
            <div class="name">1-5月城镇新增就业完成年度目标任务</div>
            <div class="val">
              <span class="num">48.32</span>
              <span class="unit">%</span>
            </div>
          </div>
        </div>
        <sourceData :source-data="localSource" class="sourcedata" :dp-type="true" />
      </div>

      <div class="line"></div>
      <syqk />
      <div class="line"></div>
      <gxbysqx />
      <div class="line"></div>
      <syryzjy />
    </div>
  </div>
</template>
<script>
import { getAction } from '@/api/manage'
import sourceData from '../common/sourceData.vue'
import barChart from '../common/barChart.vue'
import syqk from './cpn/syqk.vue'
import gxbysqx from './cpn/gxbysqx.vue'
import syryzjy from './cpn/syryzjy.vue'
export default {
  components: {
    sourceData,
    syqk,
    gxbysqx,
    syryzjy,
    barChart
  },
  data() {
    return {
      isPad: window.matchMedia('(min-width: 600px)').matches,
      localSource: {
        value1: '省人力资源社会保障厅',
        value2: '2024年5月',
        value3: '月度',
        value4: '2024年5月'
      },
      chartData: [
        {
          year: '2024年1-3月',
          ratio: 30,
          growth_yoy: 4
        },
        {
          year: '2024年1-4月',
          ratio: 42,
          growth_yoy: 1.8
        },
        {
          year: '2024年1-5月',
          ratio:45,
          growth_yoy: 4.3
        }
      ],
      chartConfig: {
        yAxis0_name: '单位：万人',
        yAxis1_name: '单位：%',
        tooltip_formatter: (e) => {
          return `${e[0].axisValue}:</br>${e[0].seriesName}: ${e[0].value}万人</br>
          ${e[3].seriesName}: ${e[3].value}%</br>
          `
        }
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.box {
  background: linear-gradient(to bottom, #e6f8ff 0%, #ffffff 20%, #ffffff 100%) center / 100% no-repeat;
  border-radius: 15px;
  position: relative;
  margin-top: 60px;
  .toptitle {
    width: 506px;
    height: 68px;
    position: absolute;
    top: -30px;
    left: 50%;
    transform: translateX(-50%);
    background: url('../img/titlebg2.png') center/100% 100% no-repeat;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 32px;
    color: #ffffff;
    line-height: 60px;
    text-align: center;
    font-style: normal;
  }

  .contBox {
    margin-top: 24px;
    padding: 72px 36px 36px;
    position: relative;

    .btmBar {
      width: 100%;
      margin-top: 35px;
      height: 530px;
    }
    .top {
      background: url('../img/tbg.png') center/100% 100% no-repeat;
      width: 100%;
      display: flex;
      padding: 24px 20px;
      margin-top: 24px;
      align-items: center;
      justify-content: center;
      img {
        width: 125px;
        height: 122px;
      }
      .cont {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        .name {
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 28px;
          color: #212121;
          line-height: 40px;
          text-align: left;
          font-style: normal;
        }
        .val {
          display: flex;
          align-items: baseline;
          .num {
            font-family: D-DIN, D-DIN;
            font-weight: bold;
            font-size: 68px;
            color: #ff7b05;
            line-height: 74px;
            text-align: left;
            font-style: normal;
          }
          .unit {
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 26px;
            color: #666666;
            line-height: 40px;
            text-align: left;
            font-style: normal;
          }
        }
      }
    }
    .toptext {
      width: 100%;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 28px;
      color: #212121;
      line-height: 48px;
      text-align: left;
      font-style: normal;
      text-indent: 2em;
      margin: 0px auto 20px;
      span {
        color: #3096ef;
      }
    }
    .box_title {
      margin: 30px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: PingFangSC, PingFang SC;
      font-weight: 600;
      font-size: 32px;
      color: #000000;
      line-height: 45px;
      text-align: center;
      font-style: normal;
      background: linear-gradient(0deg, #0284ff 0%, #1282ff 39%, #00bbff 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      div {
        margin: 0 10px;
      }
      img {
        width: 59px;
        height: 17px;
      }
    }
    .sourcedata {
      margin-top: 24px;
    }
    .line {
      width: 100%;
      height: 5px;
      margin: 32px 0 20px;
      background: url('../img/line.png') center center/604px 100% no-repeat;
    }
  }
}
</style>
