<template>
  <div class="gxbysqx_contBox">
    <div class="top">⾼校毕业⽣去向落实率有⼒提升</div>
    <div class="baseCard2">
      <div class="time">截至8月31日</div>
      <div class="contbox">
        <div class="title">2024届⾼校毕业⽣毕业去向落实率</div>
        <div class="valbox">
          <div class="ltval">
            <div class="num">87.1</div>
            <div class="unit">%</div>
          </div>
          <div class="ctval">
            <div class="desc">高于去年同期</div>
            <div class="value">3.1<span>个百分点</span></div>
          </div>
        </div>
      </div>
    </div>
    <div class="btmBar">
      <barChart :chart-data="chartData" :chart-config="chartConfig" type="2" :show-data-zoom="false" />
    </div>
    <sourceData :source-data="localSource" class="sourcedata" :dp-type="true" />
  </div>
</template>
<script>
import { getAction } from '@/api/manage'
import sourceData from '../../common/sourceData.vue'
import barChart from '../../common/barChart.vue'
export default {
  components: {
    sourceData,
    barChart
  },
  data() {
    return {
      isPad: window.matchMedia('(min-width: 600px)').matches,
      localSource: {
        value1: '省人力资源社会保障厅',
        value2: '2024年5月',
        value3: '月度',
        value4: '2024年5月'
      },
      chartData: [
        {
          year: '2023年',
          ratio: 30,
          growth_yoy: 3.4
        },
        {
          year: '2024一季度',
          ratio: 42,
          growth_yoy: 4.2
        },
        {
          year: '2024年上半年',
          ratio: 45,
          growth_yoy: 4.3
        },
        {
          year: '2024年1-7月',
          ratio: 45,
          growth_yoy: 3.9
        },
        {
          year: '2024年1-8月',
          ratio: 45,
          growth_yoy: 5.4
        }
      ],
      chartConfig: {
        yAxis0_name: '单位：万人',
        yAxis1_name: '单位：%',
        tooltip_formatter: (e) => {
          return `${e[0].axisValue}:</br>${e[0].seriesName}: ${e[0].value}万人</br>
          ${e[3].seriesName}: ${e[3].value}%</br>
          `
        }
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.gxbysqx_contBox {
  position: relative;
  .top {
    margin: 30px auto 0;
    width: 645px;
    background: #e8f3ff;
    border: 5px solid white;
    box-shadow: 0px 5px 15px rgba(222, 222, 222, 1);
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    border-radius: 16px;
    font-size: 28px;
    color: #212121;
    line-height: 48px;
    text-align: left;
    font-style: normal;
    padding: 30px 20px;
    text-align: center;
    span {
      color: #3096ef;
    }
  }
  .baseCard2 {
    margin-top: 35px;
    width: 100%;
    background: linear-gradient(to right, #4e93ff 0%, #5ec0ff 100%);
    border-radius: 10px;
    padding: 65px 30px 30px;
    position: relative;
    .time {
      background: url('../../img/timebg.png') center/100% 100% no-repeat;
      width: 228px;
      height: 71px;
      position: absolute;
      right: -4px;
      top: -8px;
      z-index: 3;
      text-align: center;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 24px;
      color: #ffffff;
      line-height: 65px;
      font-style: normal;
    }
    .contbox {
      .title {
        font-family: PingFangSC, PingFang SC;
        font-weight: 600;
        font-size: 28px;
        color: #ffffff;
        line-height: 20px;
        text-align: left;
        font-style: normal;
        margin-bottom: 58px;
      }
      .valbox {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .ltval {
          display: flex;
          align-items: baseline;
          .num {
            font-family: D-DIN, D-DIN;
            font-weight: bold;
            font-size: 68px;
            color: #ffffff;
            line-height: 27px;
            text-align: justify;
            font-style: normal;
          }
          .unit {
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 24px;
            color: #ffffff;
            line-height: 27px;
            text-align: left;
            font-style: normal;
          }
        }
        .ctval {
          display: flex;
          align-items: baseline;
          margin-right: 10px;
          .desc {
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 24px;
            color: #ffffff;
            line-height: 27px;
            text-align: left;
            font-style: normal;
          }
          img {
            width: 25px;
            height: 35px;
            margin: 0 5px;
          }
          .value {
            font-family: D-DIN, D-DIN;
            font-weight: bold;
            font-size: 48px;
            color: #de242c;
            line-height: 27px;
            text-align: justify;
            font-style: normal;
            span {
              color: #ffffff;
              font-size: 24px;
            }
          }
        }
      }
    }
  }
  .btmBar {
    width: 100%;
    height: 530px;
    margin: 24px auto 0;
  }
  .sourcedata {
    margin-top: 24px;
  }
}
</style>
