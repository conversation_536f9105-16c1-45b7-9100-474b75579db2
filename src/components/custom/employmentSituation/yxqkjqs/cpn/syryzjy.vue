<template>
  <div class="syryzjy_contBox">
    <div class="top2">失业⼈员再就业⽬标有效落实</div>

    <div class="box_title">
      <img src="../../img/titlebg4.png" alt="" />
      <div>失业⼈员再就业情况</div>
      <img src="../../img/titlebg4.png" alt="" />
    </div>

    <div class="btmBar">
      <barChart :chart-data="chartData" :chart-config="chartConfig" type="3" :show-data-zoom="false" />
    </div>
    <div class="top">
      <img src="../../img/icon.png" alt="" />
      <div class="cont">
        <div class="name">1-5月城镇新增就业完成年度目标任务</div>
        <div class="val">
          <span class="num">48.32</span>
          <span class="unit">%</span>
        </div>
      </div>
    </div>
    <sourceData :source-data="localSource" class="sourcedata" :dp-type="true" />
  </div>
</template>
<script>
import { getAction } from '@/api/manage'
import sourceData from '../../common/sourceData.vue'
import barChart from '../../common/barChart.vue'
export default {
  components: {
    sourceData,
    barChart
  },
  data() {
    return {
      active: '年度',
      btnList: ['年度', '月度'],
      isPad: window.matchMedia('(min-width: 600px)').matches,
      localSource: {
        value1: '省人力资源社会保障厅',
        value2: '2024年5月',
        value3: '月度',
        value4: '2024年5月'
      },
      chartData: [
        {
          year: '2024年1-3月',
          ratio: 30,
          growth_yoy: 2.2
        },
        {
          year: '2024年1-4月',
          ratio: 42,
          growth_yoy: 1.8
        },
        {
          year: '2024年1-5月',
          ratio: 45,
          growth_yoy: 3.3
        }
      ],
      chartConfig: {
        yAxis0_name: '单位：万人',
        yAxis1_name: '单位：%',
        tooltip_formatter: (e) => {
          return `${e[0].axisValue}:</br>${e[0].seriesName}: ${e[0].value}万人</br>
          ${e[3].seriesName}: ${e[3].value}%</br>
          `
        }
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.syryzjy_contBox {
  position: relative;
  .top2 {
    margin: 30px auto 0;
    width: 645px;
    background: #e8f3ff;
    border: 5px solid white;
    box-shadow: 0px 5px 15px rgba(222, 222, 222, 1);
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    border-radius: 16px;
    font-size: 28px;
    color: #212121;
    line-height: 48px;
    text-align: left;
    font-style: normal;
    padding: 30px 20px;
    text-align: center;
    span {
      color: #3096ef;
    }
  }
  .box_title {
    margin: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    font-size: 32px;
    color: #000000;
    line-height: 45px;
    text-align: center;
    font-style: normal;
    background: linear-gradient(0deg, #0284ff 0%, #1282ff 39%, #00bbff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    div {
      margin: 0 10px;
    }
    img {
      width: 59px;
      height: 17px;
    }
  }

  .btmcont {
    width: 100%;
    margin: 24px auto 0;
    position: relative;
    position: relative;

    .mt_btn {
      display: flex;
      margin: 24px auto;
      align-items: center;
      justify-content: center;
      .btnItem {
        margin: 0 10px;
        width: 148px;
        height: 52px;
        border-radius: 26px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgb(247, 248, 250);
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 24px;
        line-height: 33px;
        color: rgb(105, 105, 105);
        text-align: left;
        font-style: normal;
      }
      .active {
        background: linear-gradient(to right, rgb(78, 150, 255) 0%, rgb(95, 193, 255) 100%);
        color: #ffffff;
      }
    }
  }
  .btmBar {
    width: 100%;
    height: 480px;
    margin: 25px auto 0;
  }
  .top {
    background: url('../../img/tbg.png') center/100% 100% no-repeat;
    width: 100%;
    display: flex;
    padding: 24px 20px;
    margin-top: 24px;
    align-items: center;
    justify-content: center;
    img {
      width: 125px;
      height: 122px;
    }
    .cont {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      .name {
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 28px;
        color: #212121;
        line-height: 40px;
        text-align: left;
        font-style: normal;
      }
      .val {
        display: flex;
        align-items: baseline;
        .num {
          font-family: D-DIN, D-DIN;
          font-weight: bold;
          font-size: 68px;
          color: #ff7b05;
          line-height: 74px;
          text-align: left;
          font-style: normal;
        }
        .unit {
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 26px;
          color: #666666;
          line-height: 40px;
          text-align: left;
          font-style: normal;
        }
      }
    }
  }
  .sourcedata {
    margin-top: 24px;
  }
}
</style>
