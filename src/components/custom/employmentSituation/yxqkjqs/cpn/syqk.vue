<template>
  <div class="syqk_contBox">
    <div class="box_title">
      <img src="../../img/titlebg4.png" alt="" />
      <div>失业情况</div>
      <img src="../../img/titlebg4.png" alt="" />
    </div>
    <div class="baseCard2">
      <div class="time">截至2024年5月17日</div>
      <div class="contbox">
        <div class="title">全省城镇调查失业率</div>
        <div class="valbox">
          <div class="ltval">
            <div class="num">5.2</div>
            <div class="unit">%</div>
          </div>
          <div class="ctval">
            <div class="desc">控降标准</div>
            <div class="value">5.5 <span>%左右</span></div>
          </div>
        </div>
      </div>
    </div>
    <div class="btmcont">
      <div class="mt_btn">
        <div
          v-for="(item, i) in btnList"
          :key="i"
          :class="{ btnItem: true, active: active === item }"
          @click="handleClick(item)"
        >
          {{ item }}
        </div>
      </div>
      <div class="btmBar">
        <barChart :chart-data="chartData" :chart-config="chartConfig" type="1" :show-data-zoom="false" />
      </div>
    </div>
    <sourceData :source-data="localSource" class="sourcedata" :dp-type="true" />
  </div>
</template>
<script>
import { getAction } from '@/api/manage'
import sourceData from '../../common/sourceData.vue'
import barChart from '../../common/barChart2.vue'
export default {
  components: {
    sourceData,
    barChart
  },
  data() {
    return {
      active: '年度',
      btnList: ['年度', '月度'],
      isPad: window.matchMedia('(min-width: 600px)').matches,
      localSource: {
        value1: '省人力资源社会保障厅',
        value2: '2024年5月',
        value3: '月度',
        value4: '2024年5月'
      },
      chartData: [
        {
          year: '2024年1月',
          growth_yoy: 5.8
        },
        {
          year: '2024年2月',
          growth_yoy: 5.6
        },
        {
          year: '2024年3月',
          growth_yoy: 5.85
        },
        {
          year: '2024年4月',
          growth_yoy: 5.5
        }
      ],
      chartConfig: {
        yAxis1_name: '单位：%',
        tooltip_formatter: (e) => {
          return `${e[0].axisValue}:</br>城镇失业率: ${e[0].value}%</br>
          `
        }
      }
    }
  },
  methods: {
    handleClick(item) {
      this.active = item
      if (item === '年度') {
        this.chartData = [
          {
            year: '2024年1月',
            growth_yoy: 5.8
          },
          {
            year: '2024年2月',
            growth_yoy: 5.6
          },
          {
            year: '2024年3月',
            growth_yoy: 5.85
          },
          {
            year: '2024年4月',
            growth_yoy: 5.5
          }
        ]
      } else {
        this.chartData = [
          {
            year: '2024年1月',
            growth_yoy: 5.6
          },
          {
            year: '2024年2月',
            growth_yoy: 5.7
          },
          {
            year: '2024年3月',
            growth_yoy: 5.85
          },
          {
            year: '2024年4月',
            growth_yoy: 5.4
          }
        ]
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.syqk_contBox {
  position: relative;

  .box_title {
    margin: 0 30px 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    font-size: 32px;
    color: #000000;
    line-height: 45px;
    text-align: center;
    font-style: normal;
    background: linear-gradient(0deg, #0284ff 0%, #1282ff 39%, #00bbff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    div {
      margin: 0 10px;
    }
    img {
      width: 59px;
      height: 17px;
    }
  }
  .baseCard2 {
    margin-top: 35px;
    width: 100%;
    background: linear-gradient(to right, #4e93ff 0%, #5ec0ff 100%);
    border-radius: 10px;
    padding: 43px 30px;
    position: relative;
    .time {
      background: url('../../img/timebg.png') center/100% 100% no-repeat;
      width: 228px;
      height: 71px;
      position: absolute;
      right: -4px;
      top: -8px;
      z-index: 3;
      text-align: center;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 24px;
      color: #ffffff;
      line-height: 65px;
      font-style: normal;
    }
    .contbox {
      .title {
        font-family: PingFangSC, PingFang SC;
        font-weight: 600;
        font-size: 28px;
        color: #ffffff;
        line-height: 20px;
        text-align: left;
        font-style: normal;
        margin-bottom: 58px;
      }
      .valbox {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .ltval {
          display: flex;
          align-items: baseline;
          .num {
            font-family: D-DIN, D-DIN;
            font-weight: bold;
            font-size: 68px;
            color: #ffffff;
            line-height: 27px;
            text-align: justify;
            font-style: normal;
          }
          .unit {
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 24px;
            color: #ffffff;
            line-height: 27px;
            text-align: left;
            font-style: normal;
          }
        }
        .ctval {
          display: flex;
          align-items: baseline;
          margin-right: 10px;
          .desc {
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 24px;
            color: #ffffff;
            line-height: 27px;
            text-align: left;
            font-style: normal;
          }
          img {
            width: 25px;
            height: 35px;
            margin: 0 5px;
          }
          .value {
            font-family: D-DIN, D-DIN;
            font-weight: bold;
            font-size: 48px;
            color: white;
            line-height: 27px;
            text-align: justify;
            font-style: normal;
            span {
              font-size: 24px;
            }
          }
        }
      }
    }
  }
  .btmcont {
    width: 100%;
    margin: 24px auto 0;
    position: relative;
    position: relative;

    .mt_btn {
      display: flex;
      margin: 24px auto;
      align-items: center;
      justify-content: center;
      .btnItem {
        margin: 0 10px;
        width: 148px;
        height: 52px;
        border-radius: 26px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgb(247, 248, 250);
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 24px;
        line-height: 33px;
        color: rgb(105, 105, 105);
        text-align: left;
        font-style: normal;
      }
      .active {
        background: linear-gradient(to right, rgb(78, 150, 255) 0%, rgb(95, 193, 255) 100%);
        color: #ffffff;
      }
    }

    .btmBar {
      padding: 0px 10px;
      width: 100%;
      height: 420px;
      margin: 0 auto 25px;
    }
  }
  .sourcedata {
    margin-top: 24px;
  }
}
</style>
