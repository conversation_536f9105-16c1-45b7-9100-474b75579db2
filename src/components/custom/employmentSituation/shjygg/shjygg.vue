<template>
  <div class="box">
    <div class="toptitle">深化就业改⾰</div>

    <div class="contBox">
      <div class="top">
        <div class="num">01</div>
        深化农村劳动力就业工作集成改革，大力促进农村人力资源要素高效配置，全年农民工就业规模稳定在<span>2300万</span>左右。
      </div>

      <div class="top">
        <div class="num">02</div>
        建立老年人就业引导和保障机制，实施<span>“双银”</span>助力行动，促进和保障老年人就业。
      </div>
      <div class="top">
        <div class="num">03</div>
        拟在山东申请设立<span>黄河流域公共就业服务区域中心</span>，作为全国重点布局的区域性就业服务中心。
      </div>
      <div class="top">
        <div class="num">04</div>
        深化落实<span>“创业齐鲁”三年行动方案</span>，持续推进省级创业街区试点建设，完善创业保障机制。
      </div>
      <div class="top">
        <div class="num">05</div>
        深化东西部劳务协作、沿黄劳务协作、京津冀鲁苏劳务协作等联盟机制，广泛建立<span>“标准化职业指导工作室”</span>，培育一批可复制、能推广的家政服务职业化建设省级领跑机构。
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      isPad: window.matchMedia('(min-width: 600px)').matches
    }
  }
}
</script>
<style lang="scss" scoped>
.box {
  background: linear-gradient(to bottom, #e6f8ff 0%, #ffffff 20%, #ffffff 100%) center / 100% no-repeat;
  border-radius: 15px;
  position: relative;
  margin-top: 60px;
  .toptitle {
    width: 282px;
    height: 68px;
    position: absolute;
    top: -30px;
    left: 50%;
    transform: translateX(-50%);
    background: url('../img/titlebg1.png') center/100% 100% no-repeat;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 32px;
    color: #ffffff;
    line-height: 60px;
    text-align: center;
    font-style: normal;
  }

  .contBox {
    margin-top: 24px;
    padding: 36px;
    position: relative;
    .line {
      width: 100%;
      height: 5px;
      margin: 32px 0 20px;
      background: url('../img/line.png') center center/604px 100% no-repeat;
    }
    .top {
      margin: 30px auto 0;
      width: 645px;
      background: #e8f3ff;
      border: 5px solid white;
      box-shadow: 0px 5px 15px rgba(222, 222, 222, 1);
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      border-radius: 16px;
      font-size: 28px;
      color: #212121;
      line-height: 48px;
      text-align: left;
      font-style: normal;
      padding: 30px 30px 30px 70px;
      text-align: left;
      position: relative;
      span {
        color: #3096ef;
      }
      .num {
        background: url('../img/numbg2.png') center/ 100% 100% no-repeat;
        width: 101px;
        height: 107px;
        position: absolute;
        left: -30px;
        top: -30px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: PingFangSC, PingFang SC;
        font-weight: 600;
        font-size: 48px;
        color: #feffff;
        line-height: 67px;
        text-align: center;
        font-style: normal;
      }
    }
  }
}
</style>
