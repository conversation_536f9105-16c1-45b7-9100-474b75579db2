<template>
  <div class="box">
    <div class="box_title">
      <img src="../img/titlebg4.png" alt="" />
      <div>开展数智就业服务</div>
      <img src="../img/titlebg4.png" alt="" />
    </div>
    <div class="contBox">
      <div class="top">
        <div class="num">01</div>
        开展<span>“数据一库集成、服务一体提供、统计一键汇数、监测一屏通览、监管一网统管”“五个一”</span>提质增效行动，积极推动就业人才服务数字化转型。
      </div>

      <div class="top">
        <div class="num">02</div>
        创新利用移动、电力大数据做好重点行业、企业、区域和群体就业形势监测分析，防范化解规模性失业风险。
      </div>

      <div class="top">
        <div class="num">03</div>
        加强统计信息化建设，加大就业数据核查力度，对接机关事业单位参保数据，提高就业统计质量。
      </div>
      <div class="top">
        <div class="num">04</div>
        搭建就业信息化<span>“一库一平台”（全省集中就业信息资源库和对外服务平台）</span>，为<span>2925万<span>名劳动者和</span>387.5万</span>家用人单位提供<span>112项“一站式”</span>就业人才服务，今年以来服务超<span>606万</span>人次，推进数智就业服务提质升级。
      </div>
    </div>
  </div>
</template>
<script>
import { getAction } from '@/api/manage'
export default {
  components: {},
  data() {
    return {
      isPad: window.matchMedia('(min-width: 600px)').matches
    }
  }
}
</script>
<style lang="scss" scoped>
.box {
  background: linear-gradient(to bottom, #e6f8ff 0%, #ffffff 20%, #ffffff 100%) center / 100% no-repeat;
  border-radius: 15px;
  position: relative;
  margin-top: 35px;
  .box_title {
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: 36px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    font-size: 32px;
    color: #000000;
    line-height: 45px;
    text-align: center;
    font-style: normal;
    width: 100%;
    background: linear-gradient(0deg, #0284ff 0%, #1282ff 39%, #00bbff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    div {
      margin: 0 10px;
    }
    img {
      width: 59px;
      height: 17px;
    }
  }
  .contBox {
    margin-top: 24px;
    padding: 110px 36px 36px;
    position: relative;
    .line {
      width: 100%;
      height: 5px;
      margin: 32px 0 20px;
      background: url('../img/line.png') center center/604px 100% no-repeat;
    }
    .top {
      margin: 30px auto 0;
      width: 645px;
      background: #e8f3ff;
      border: 5px solid white;
      box-shadow: 0px 5px 15px rgba(222, 222, 222, 1);
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      border-radius: 16px;
      font-size: 28px;
      color: #212121;
      line-height: 48px;
      text-align: left;
      font-style: normal;
      padding: 30px 30px 30px 70px;
      text-align: left;
      position: relative;
      span {
        color: #3096ef;
      }
      .num {
        background: url('../img/numbg2.png') center/ 100% 100% no-repeat;
        width: 101px;
        height: 107px;
        position: absolute;
        left: -30px;
        top: -30px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: PingFangSC, PingFang SC;
        font-weight: 600;
        font-size: 48px;
        color: #feffff;
        line-height: 67px;
        text-align: center;
        font-style: normal;
      }
    }
  }
}
</style>
