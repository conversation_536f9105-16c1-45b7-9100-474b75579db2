<template>
  <div class="box">
    <div class="toptitle">突出重点群体</div>
    <div class="contBox">
      <div class="toptext">
        衔接做好<span>2024届</span>和<span>2025届</span>高校毕业生就业工作，及早启动“职通央企”系列活动，实施新一轮高校毕业生就业创业启航扬帆工程,发布2024年度高校毕业生就业<span>“最具吸引力”</span>系列榜单，做好2025年<span>“三支一扶”</span>计划招募实施工作，广泛推广大学生就业创业赋能中心和大学生<span>“完整就业”</span>服务站，组织开展<span>“公共就业服务进校园”“就选山东”“百校千企促就业人才对接大会”</span>等就业服务专项活动，常态化举办<span>“山东—名校人才直通车”“名校师生山东城市行”</span>等重点引才活动。
      </div>
      <div class="imglist">
        <div v-for="(item, i) in imglist" :key="i" class="imgitem">
          <img :src="item.img" alt="" />
          <div>{{ item.name }}</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import img1 from '../img/img18.png'
import img2 from '../img/img19.png'
import img3 from '../img/img20.png'
import img4 from '../img/img21.png'
export default {
  data() {
    return {
      isPad: window.matchMedia('(min-width: 600px)').matches,
      imglist: [
        {
          img: img1,
          name: '“三支一扶”'
        },
        {
          img: img2,
          name: '“公共就业服务进校园”'
        },
        {
          img: img3,
          name: '“就选山东”'
        },
        {
          img: img4,
          name: '“山东—名校人才直通车”'
        }
      ]
    }
  }
}
</script>
<style lang="scss" scoped>
.box {
  background: linear-gradient(to bottom, #e6f8ff 0%, #ffffff 20%, #ffffff 100%) center / 100% no-repeat;
  border-radius: 15px;
  position: relative;
  margin-top: 60px;
  .toptitle {
    width: 282px;
    height: 68px;
    position: absolute;
    top: -30px;
    left: 50%;
    transform: translateX(-50%);
    background: url('../img/titlebg1.png') center/100% 100% no-repeat;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 32px;
    color: #ffffff;
    line-height: 60px;
    text-align: center;
    font-style: normal;
  }

  .contBox {
    padding: 72px 36px 36px;
    position: relative;
    .toptext {
      width: 100%;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 28px;
      color: #212121;
      line-height: 48px;
      text-align: left;
      font-style: normal;
      text-indent: 2em;
      margin: 0px auto 20px;
      span {
        color: #3096ef;
      }
    }

    .imglist {
      width: 100%;
      display: grid;
      column-gap: 12px;
      row-gap: 12px;
      margin-top: 35px;
      grid-template-columns: repeat(2, 1fr);
      .imgitem {
        display: flex;
        flex-direction: column;
        align-items: center;
        img {
          width: 294px;
          height: 196px;
        }
        div {
          margin-top: -50px;
          width: 100%;
          height: 58px;
          display: flex;
          align-items: center;
          justify-content: center;
          text-align: center;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 24px;
          color: #ffffff;
          line-height: 28px;
          font-style: normal;
          background: url('../img/textbg2.png') center / 100% 100% no-repeat;
        }
      }
    }
  }
}
</style>
