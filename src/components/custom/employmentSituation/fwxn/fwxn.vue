<template>
  <div class="box">
    <div class="toptitle">提升⼈⼒资源服务效能</div>

    <div class="contBox">
      <div class="padNedd">
        <div class="top">
          <div class="num">01</div>
          持续开展<span>“数聚赋能”人力资源数字化服务典型案例评选</span>
        </div>

        <div class="top">
          <div class="num">02</div>
          办好<span>第四届山东人力资源服务创新大赛</span>
        </div>

        <div class="top">
          <div class="num">03</div>
          探索<span>“流域+省际”</span>人力资源合作新模式
        </div>
      </div>
      <div class="padNedd">
        <div class="top">
          <div class="num">04</div>
          组织实施<span>人力资源服务促就业“引航助跑”、引人才“聚才兴业”系列活动</span>
        </div>
        <div class="top">
          <div class="num">05</div>
          继续开展<span>山东人力资源服务“名企万里行”活动</span>
        </div>
        <div class="top">
          <div class="num">06</div>
          办好<span>第三届人力资源高质量发展对话会</span>
        </div>
      </div>
      <div class="imglist">
        <div v-for="(item, i) in imglist" :key="i" class="imgitem">
          <img :src="item.img" alt="" />
          <div>{{ item.name }}</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import img1 from '../img/img24.png'
import img2 from '../img/img25.png'
import img3 from '../img/img26.png'
import img4 from '../img/img27.png'
export default {
  data() {
    return {
      isPad: window.matchMedia('(min-width: 600px)').matches,
      imglist: [
        {
          img: img1,
          name: '第四届山东人力资源服务创新大赛'
        },
        {
          img: img2,
          name: '人力资源服务促就业“引航助跑”'
        },
        {
          img: img3,
          name: '山东人力资源服务“名企万里行”活动'
        },
        {
          img: img4,
          name: '第三届人力资源高质量发展对话会'
        }
      ]
    }
  }
}
</script>
<style lang="scss" scoped>
.box {
  background: linear-gradient(to bottom, #e6f8ff 0%, #ffffff 20%, #ffffff 100%) center / 100% no-repeat;
  border-radius: 15px;
  position: relative;
  margin-top: 60px;
  .toptitle {
    width: 410px;
    height: 68px;
    position: absolute;
    top: -30px;
    left: 50%;
    transform: translateX(-50%);
    background: url('../img/titlebg2.png') center/100% 100% no-repeat;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 32px;
    color: #ffffff;
    line-height: 60px;
    text-align: center;
    font-style: normal;
  }

  .contBox {
    margin-top: 24px;
    padding: 36px;
    position: relative;
    .line {
      width: 100%;
      height: 5px;
      margin: 32px 0 20px;
      background: url('../img/line.png') center center/604px 100% no-repeat;
    }
    .top {
      margin: 30px auto 0;
      width: 645px;
      background: #e8f3ff;
      border: 5px solid white;
      box-shadow: 0px 5px 15px rgba(222, 222, 222, 1);
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      border-radius: 16px;
      font-size: 28px;
      color: #212121;
      line-height: 48px;
      text-align: left;
      font-style: normal;
      padding: 30px 30px 30px 70px;
      text-align: left;
      position: relative;
      span {
        color: #3096ef;
      }
      .num {
        background: url('../img/numbg2.png') center/ 100% 100% no-repeat;
        width: 101px;
        height: 107px;
        position: absolute;
        left: -30px;
        top: -30px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: PingFangSC, PingFang SC;
        font-weight: 600;
        font-size: 48px;
        color: #feffff;
        line-height: 67px;
        text-align: center;
        font-style: normal;
      }
    }

    .imglist {
      width: 100%;
      display: grid;
      column-gap: 12px;
      row-gap: 12px;
      margin-top: 35px;
      grid-template-columns: repeat(2, 1fr);
      .imgitem {
        display: flex;
        flex-direction: column;
        align-items: center;
        img {
          width: 294px;
          height: 196px;
        }
        div {
          margin-top: -50px;
          width: 100%;
          height: 80px;
          display: flex;
          align-items: center;
          justify-content: center;
          text-align: center;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 24px;
          color: #ffffff;
          line-height: 28px;
          font-style: normal;
          background: url('../img/textbg.png') center / 100% 100% no-repeat;
        }
      }
    }
  }
}
</style>
