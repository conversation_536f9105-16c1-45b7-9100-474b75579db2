<template>
  <div class="box">
    <div class="box_title">
      <img src="../img/titlebg4.png" alt="" />
      <div>做好专项招聘活动</div>
      <img src="../img/titlebg4.png" alt="" />
    </div>
    <div class="contBox">
      <div class="padNeed">
        <div class="toptext">
          轮动开展<span>春风行动、民营企业服务月、大中城市联合招聘、百日千万招聘</span>等专项活动，创新招聘模式，加强对外联合，提升招聘质效。
        </div>
        <div class="imglist imglist1">
          <div v-for="(item, i) in imglist" :key="i" class="imgitem">
            <img :src="item.img" alt="" />
            <div>{{ item.name }}</div>
          </div>
        </div>

        <div class="f_cont">
          <div v-for="(item, i) in datalist" :key="i" class="f_contitem">
            <div class="row1">
              <div class="num">{{ item.value }}</div>
              <div class="unit">{{ item.unit }}</div>
            </div>
            <div class="row2">
              {{ item.name }}
            </div>
          </div>
        </div>
      </div>
      <div class="line"></div>
      <div class="padNeed">
        <div class="toptext">
          牵头组建<span>“公共人力资源市场服务联盟”</span>。在全省部署开展<span>“百企联百校职引向未来”</span>山东省优质人力资源服务机构进校园活动。
        </div>
        <div class="imglist imglist2">
          <div v-for="(item, i) in imglist2" :key="i" class="imgitem">
            <img :src="item.img" alt="" />
            <div>{{ item.name }}</div>
          </div>
        </div>
        <div class="f_cont">
          <div v-for="(item, i) in datalist2" :key="i" class="f_contitem">
            <div class="row1">
              <div class="num">{{ item.value }}</div>
              <div class="unit">{{ item.unit }}</div>
            </div>
            <div class="row2">
              {{ item.name }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { getAction } from '@/api/manage'
import img1 from '../img/img12.png'
import img2 from '../img/img13.png'
import img3 from '../img/img14.png'
import img4 from '../img/img15.png'
import img5 from '../img/img16.png'
import img6 from '../img/img17.png'
export default {
  data() {
    return {
      isPad: window.matchMedia('(min-width: 600px)').matches,
      chartData: [],
      datalist: [
        {
          name: '累计举办招聘活动',
          value: '9068',
          unit: '场'
        },
        {
          name: '提供就业岗位',
          value: '383',
          unit: '万个'
        }
      ],
      datalist2: [
        {
          name: '组织现场招聘',
          value: '700',
          unit: '多场次'
        },
        {
          name: '设置进校园服务专区',
          value: '300',
          unit: '余个'
        },
        {
          name: '发布各类招聘岗位',
          value: '60',
          unit: '余万个'
        },
        {
          name: '帮助达成初步就业意向毕',
          value: '13',
          unit: '万人'
        }
      ],
      chartConfig: {},
      imglist: [
        {
          img: img1,
          name: '春风行动'
        },
        {
          img: img2,
          name: '⺠营企业服务⽉'
        },
        {
          img: img3,
          name: '⼤中城市联合招聘'
        },
        {
          img: img3,
          name: '百⽇千万招聘'
        }
      ],
      imglist2: [
        {
          img: img4,
          name: '公共⼈⼒资源市场服务联盟'
        },
        {
          img: img5,
          name: '“百企联百校 职引向未来”'
        }
      ]
    }
  },
  created() {
    this.loadData()
  },
  methods: {
    async loadData() {
      await getAction(
        import.meta.env.VITE_APP_SITUATION_BASE_URL + '/topic/data/listAllBySql/ydd_sthj_dqhj_PM2d5bh_month'
      ).then((res) => {
        if (res.success) {
          this.chartData = res.result
          this.chartConfig = {
            yAxis0_name: '单位：微克/立方米',
            yAxis1_name: '单位：%',
            tooltip_formatter: (e) => {
              let content = ''
              if (e[3].value > 0) {
                content = '同比提升' + e[3].value + '个百分点'
              } else if (e[3].value == 0) {
                content = '同比持平'
              } else {
                content = '同比降低' + e[3].value + '个百分点'
              }
              return `${e[0].axisValue}:</br>${e[1].seriesName}: ${e[1].value}微克/立方米</br>${content}`
            }
          }
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.box {
  background: linear-gradient(to bottom, #e6f8ff 0%, #ffffff 20%, #ffffff 100%) center / 100% no-repeat;
  border-radius: 15px;
  position: relative;
  margin-top: 35px;
  .box_title {
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: 36px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    font-size: 32px;
    color: #000000;
    line-height: 45px;
    text-align: center;
    font-style: normal;
    width: 100%;
    background: linear-gradient(0deg, #0284ff 0%, #1282ff 39%, #00bbff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    div {
      margin: 0 10px;
    }
    img {
      width: 59px;
      height: 17px;
    }
  }
  .contBox {
    margin-top: 24px;
    padding: 110px 36px 36px;
    position: relative;
    .f_cont {
      display: grid;
      gap: 24px;
      margin-top: 23px;
      grid-template-columns: repeat(2, 1fr);
      .f_contitem {
        width: 100%;
        display: flex;
        height: 227px;
        flex-direction: column;
        justify-content: space-evenly;
        align-items: center;
        background: rgb(232, 243, 255);
        border-radius: 15px;
        .row1 {
          display: flex;
          align-items: baseline;
          .num {
            font-family: D-DIN, D-DIN;
            font-weight: bold;
            font-size: 68px;
            color: #ff7b05;
            line-height: 74px;
            text-align: right;
            font-style: normal;
          }
          .unit {
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 24px;
            color: #666666;
            line-height: 33px;
            text-align: left;
            font-style: normal;
            margin: 0 0 0 5px;
          }
        }
        .row2 {
          width: 278px;
          //   height: 63px;
          padding: 10px 0px;
          background: linear-gradient(to right, rgb(69, 183, 253) 0%, rgb(91, 220, 254) 100%);
          font-family: PingFangSC, PingFang SC;
          border-radius: 32px;
          font-weight: 600;
          font-size: 28px;
          color: #ffffff;
          line-height: 40px;
          text-align: center;
          font-style: normal;
          display: flex;
          align-items: center;
          justify-content: center;
          span {
            display: inline-block;
            background: #ffffff;
            border-radius: 16px;
            opacity: 0.8;
            padding: 2px 10px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            margin-right: 5px;
            font-size: 24px;
            color: #666666;
            line-height: 33px;
            text-align: left;
            font-style: normal;
          }
        }
      }
    }
    .toptext {
      width: 100%;

      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 28px;
      color: #212121;
      line-height: 48px;
      text-align: left;
      font-style: normal;
      text-indent: 2em;
      margin: 0px auto 20px;
      span {
        color: #3096ef;
      }
    }
    .imglist {
      width: 100%;
      display: grid;
      column-gap: 12px;
      row-gap: 12px;
      margin-top: 35px;
      grid-template-columns: repeat(2, 1fr);
      .imgitem {
        display: flex;
        flex-direction: column;
        align-items: center;
        img {
          width: 294px;
          height: 196px;
        }
        div {
          margin-top: -50px;
          width: 100%;
          height: 58px;
          display: flex;
          align-items: center;
          justify-content: center;
          text-align: center;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 24px;
          color: #ffffff;
          line-height: 28px;
          font-style: normal;
          background: url('../img/textbg2.png') center / 100% 100% no-repeat;
        }
      }
    }
    .line {
      width: 100%;
      height: 5px;
      margin: 32px 0 20px;
      background: url('../img/line.png') center center/604px 100% no-repeat;
    }
  }
}
</style>
